<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Project;

class ClientProjectsManager extends Component
{
    public $clientId;
    public $showModal = false;
    public $title;
    public $description;
    public $start_date;
    public $end_date;
    public $status = 'active';

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'nullable|string',
        'start_date' => 'nullable|date',
        'end_date' => 'nullable|date|after_or_equal:start_date',
        'status' => 'required|string',
    ];

    public function mount($clientId)
    {
        $this->clientId = $clientId;
    }

    public function openModal()
    {
        $this->reset(['title', 'description', 'start_date', 'end_date', 'status']);
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
    }

    public function saveProject()
    {
        $this->validate();
        Project::create([
            'client_id' => $this->clientId,
            'title' => $this->title,
            'description' => $this->description,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'status' => $this->status,
        ]);
        $this->closeModal();
        $this->reset(['title', 'description', 'start_date', 'end_date', 'status']);
    }

    public function render()
    {
        $projects = \App\Models\Project::where('client_id', $this->clientId)->latest()->get();
        return view('livewire.client-projects-manager', compact('projects'));
    }
}
