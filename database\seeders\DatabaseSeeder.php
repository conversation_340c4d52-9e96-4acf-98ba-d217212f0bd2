<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Comment out the default user factory to avoid conflicts with our custom users
        // User::factory(10)->create();

        // We'll use our custom UserSeeder instead of creating users here

        // Call our custom seeders
        $this->call([
            // SMMS seeders only
            UserSeeder::class,
            ProjectTypeSeeder::class,
            ClientSeeder::class,
            ProjectSeeder::class,
            MilestoneSeeder::class,
            PaymentSeeder::class,
            IncentiveRuleSeeder::class,
            DashboardWidgetSeeder::class,
            NotificationEventSeeder::class,
        ]);
    }
}
