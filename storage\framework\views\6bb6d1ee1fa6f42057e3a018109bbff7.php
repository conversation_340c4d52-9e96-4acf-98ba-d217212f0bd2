<?php if (isset($component)) { $__componentOriginal6cc610bfb7b2cdf012df93432f0ee887 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6cc610bfb7b2cdf012df93432f0ee887 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sidebar-fix-v2','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar-fix-v2'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6cc610bfb7b2cdf012df93432f0ee887)): ?>
<?php $attributes = $__attributesOriginal6cc610bfb7b2cdf012df93432f0ee887; ?>
<?php unset($__attributesOriginal6cc610bfb7b2cdf012df93432f0ee887); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6cc610bfb7b2cdf012df93432f0ee887)): ?>
<?php $component = $__componentOriginal6cc610bfb7b2cdf012df93432f0ee887; ?>
<?php unset($__componentOriginal6cc610bfb7b2cdf012df93432f0ee887); ?>
<?php endif; ?><?php /**PATH D:\wamp64\www\smms\storage\framework\views/372196686030c8f69bd3d2ee97bc0018.blade.php ENDPATH**/ ?>