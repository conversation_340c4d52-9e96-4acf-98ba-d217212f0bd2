<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class DashboardPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the dashboard.
     */
    public function view(User $user): bool
    {
        // Only allow users with specific roles to access the dashboard
        // return $user->hasAnyRole(['super_admin', 'admin', 'manager', 'marketing']);
        return $user->hasPermissionTo('view_dashboard');
    }
}
