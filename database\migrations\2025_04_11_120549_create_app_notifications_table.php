<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('app_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('notification_event_id')->constrained('notification_events')->onDelete('cascade');
            $table->string('title');                 // Notification title
            $table->text('message');                 // Notification message
            $table->json('data')->nullable();        // Additional data for the notification
            $table->timestamp('read_at')->nullable(); // When the notification was read
            $table->timestamp('sent_at')->nullable(); // When the notification was sent
            $table->string('status')->default('pending'); // Status: pending, sent, failed
            $table->timestamps();

            // Indexes for faster queries
            $table->index('user_id');
            $table->index('notification_event_id');
            $table->index('read_at');
            $table->index('status');
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_notifications');
    }
};
