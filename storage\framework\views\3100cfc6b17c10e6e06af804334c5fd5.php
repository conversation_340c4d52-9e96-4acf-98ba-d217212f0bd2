<?php
    use Illuminate\Support\Str;
?>

<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/custom.css')); ?>">

    <style>
        /* JavaScript-based tooltip styling */
        .js-tooltip {
            position: absolute;
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 99999;
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .js-tooltip.show {
            opacity: 1;
        }

        .js-tooltip::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #1f2937 transparent transparent transparent;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tooltip system initialized');
            let tooltip = null;

            function createTooltip() {
                if (!tooltip) {
                    tooltip = document.createElement('div');
                    tooltip.className = 'js-tooltip';
                    document.body.appendChild(tooltip);
                }
                return tooltip;
            }

            function showTooltip(element, text) {
                const tooltip = createTooltip();
                tooltip.textContent = text;
                tooltip.classList.add('show');

                const rect = element.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();

                tooltip.style.left = (rect.left + rect.width / 2 - tooltipRect.width / 2) + 'px';
                tooltip.style.top = (rect.top - tooltipRect.height - 10) + 'px';
            }

            function hideTooltip() {
                if (tooltip) {
                    tooltip.classList.remove('show');
                }
            }

            // Add event listeners to all elements with title attribute
            function initTooltips() {
                document.querySelectorAll('[title]').forEach(element => {
                    const title = element.getAttribute('title');
                    if (title) {
                        // Remove default browser tooltip
                        element.removeAttribute('title');
                        element.setAttribute('data-tooltip', title);

                        element.addEventListener('mouseenter', function() {
                            showTooltip(this, this.getAttribute('data-tooltip'));
                        });

                        element.addEventListener('mouseleave', hideTooltip);
                    }
                });
            }

            // Initialize tooltips
            initTooltips();

            // Re-initialize tooltips when new content is loaded (for Livewire)
            document.addEventListener('livewire:navigated', initTooltips);
            document.addEventListener('livewire:load', initTooltips);
        });
    </script>

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
        <div class="flex items-center space-x-4">
            <button type="button" wire:click="resetDashboard"
                class="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 flex items-center text-sm"
                title="Reset dashboard to default settings">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-arrow-path'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 mr-1']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                Reset
            </button>

            <a href="<?php echo e(route('filament.admin.pages.dashboard-settings')); ?>"
                class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 flex items-center"
                title="Customize dashboard widgets and layout">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-cog-6-tooth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 mr-1']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                Customize
            </a>

            
            <?php
                $user = auth()->user();
                $avatarUrl = $user && $user->avatar_url
                    ? (Str::startsWith($user->avatar_url, ['http://', 'https://', '/storage/'])
                        ? (Str::startsWith($user->avatar_url, '/storage/') ? asset(ltrim($user->avatar_url, '/')) : $user->avatar_url)
                        : asset('storage/' . ltrim($user->avatar_url, '/')))
                    : 'https://ui-avatars.com/api/?name=' . urlencode($user?->name ?? 'User');
            ?>
            <img class="fi-avatar object-cover object-center fi-circular rounded-full h-8 w-8 fi-user-avatar" src="<?php echo e($avatarUrl); ?>" alt="Avatar of <?php echo e($user?->name ?? 'User'); ?>" onerror="this.onerror=null;this.src='https://ui-avatars.com/api/?name=<?php echo e(urlencode($user?->name ?? 'User')); ?>';">
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $widgets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $widget): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $widthClass = match ($widget['width']) {
                    3 => 'col-span-1', // 25% width (1 of 4 columns)
                    4 => 'col-span-1 sm:col-span-2 md:col-span-1', // 33% width (1 of 3 columns on medium screens)
                    6 => 'col-span-1 sm:col-span-2', // 50% width (2 of 4 columns)
                    8 => 'col-span-1 sm:col-span-2 lg:col-span-3', // 75% width (3 of 4 columns)
                    12 => 'col-span-1 sm:col-span-2 lg:col-span-4', // 100% width (4 of 4 columns)
                    default => 'col-span-1 sm:col-span-2', // Default to 50% width
                };

                $heightClass = match ($widget['height']) {
                    1 => 'h-auto',
                    2 => 'h-auto',
                    3 => 'h-auto',
                    default => 'h-auto',
                };
            ?>

            <div class="<?php echo e($widthClass); ?> <?php echo e($heightClass); ?>">
                <!--[if BLOCK]><![endif]--><?php switch($widget['component']):
                    case ('TotalProjectsWidget'): ?>
                    <a href="<?php echo e(route('filament.admin.resources.projects.index')); ?>"
                       class="block transition-all duration-200 hover:scale-105 hover:shadow-lg group cursor-pointer">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-transparent group-hover:border-primary-200 dark:group-hover:border-blue-600">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500 group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-briefcase'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-8 h-8']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                        <?php echo e(\App\Models\Project::count()); ?>

                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Projects</p>
                                </div>
                            </div>
                        </div>
                    </a>
                    <?php break; ?>

                    <?php case ('TotalClientsWidget'): ?>
                    <a href="<?php echo e(route('filament.admin.resources.clients.index')); ?>"
                       class="block transition-all duration-200 hover:scale-105 hover:shadow-lg group cursor-pointer">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-transparent group-hover:border-primary-200 dark:group-hover:border-blue-600">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500 group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-users'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-8 h-8']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                        <?php echo e(\App\Models\Client::count()); ?>

                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Clients</p>
                                </div>
                            </div>
                        </div>
                    </a>
                    <?php break; ?>

                    <?php case ('PendingPaymentsWidget'): ?>
                    <a href="<?php echo e(route('filament.admin.resources.payments.index')); ?>"
                       class="block transition-all duration-200 hover:scale-105 hover:shadow-lg group cursor-pointer">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-transparent group-hover:border-primary-200 dark:group-hover:border-blue-600">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500 group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-credit-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-8 h-8']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                        ₹<?php echo e(number_format(\App\Models\Payment::where('status', 'pending')->sum('amount'), 2)); ?>

                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Pending Payments</p>
                                </div>
                            </div>
                        </div>
                    </a>
                    <?php break; ?>

                    <?php case ('ApprovedIncentivesWidget'): ?>
                    <a href="<?php echo e(route('filament.admin.resources.incentives.index')); ?>"
                       class="block transition-all duration-200 hover:scale-105 hover:shadow-lg group cursor-pointer">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-transparent group-hover:border-primary-200 dark:group-hover:border-blue-600">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500 group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-banknotes'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-8 h-8']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                        ₹<?php echo e(number_format(\App\Models\Incentive::where('status', 'approved')->sum('amount'), 2)); ?>

                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Approved Incentives</p>
                                </div>
                            </div>
                        </div>
                    </a>
                    <?php break; ?>

                    <?php case ('RecentProjectsWidget'): ?>
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Recent Projects</h2>
                                <a href="<?php echo e(route('filament.admin.resources.projects.index')); ?>"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View All →
                                </a>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200 dark:border-gray-700">
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Project
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Client
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">BDE
                                            </th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Amount
                                            </th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = \App\Models\Project::with(['client', 'user'])->latest()->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr
                                                class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150">
                                                <td class="py-2 px-2">
                                                    <div class="flex items-center">
                                                        <div class="text-primary-500 dark:text-blue-500 mr-2">
                                                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-document'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        </div>
                                                        <span class="text-gray-900 dark:text-white hover:text-gray-900 dark:hover:text-white"
                                                            title="<?php echo e($project->title); ?>"><?php echo e($project->title); ?></span>
                                                    </div>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <span
                                                        class="text-gray-900 dark:text-white"><?php echo e($project->client->name); ?></span>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <span
                                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                                        <?php echo e($project->user->name); ?>

                                                    </span>
                                                </td>
                                                <td class="py-2 px-2 text-right text-gray-900 dark:text-white">
                                                    ₹<?php echo e(number_format($project->total_payment, 2)); ?>

                                                </td>
                                                <td class="py-2 px-2 text-right">
                                                    <a href="<?php echo e(route('filament.admin.resources.projects.edit', $project)); ?>"
                                                        class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 inline-flex items-center justify-center transition-colors"
                                                        title="Edit Project">
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-pencil-square'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if(\App\Models\Project::count() === 0): ?>
                                            <tr>
                                                <td colspan="5" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                                    No projects found
                                                </td>
                                            </tr>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php break; ?>

                    <?php case ('UpcomingPaymentsWidget'): ?>
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Upcoming Payments</h2>
                                <a href="<?php echo e(route('filament.admin.resources.payments.index')); ?>"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View All →
                                </a>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200 dark:border-gray-700">
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Project
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Due
                                                Date</th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Amount
                                            </th>
                                            <th class="text-center py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Status</th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                            $payments = \App\Models\Payment::with('project')
                                                ->where('status', 'pending')
                                                ->orderBy('due_date')
                                                ->take(5)
                                                ->get();
                                        ?>

                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $isPast = $payment->due_date->isPast();
                                                $daysLeft = $isPast
                                                    ? $payment->due_date->diffInDays(now())
                                                    : now()->diffInDays($payment->due_date);
                                                $statusClass = $isPast
                                                    ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                                    : ($daysLeft <= 7
                                                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                                                        : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300');
                                                $statusText = $isPast
                                                    ? "Overdue by {$daysLeft} " . Str::plural('day', $daysLeft)
                                                    : "{$daysLeft} " . Str::plural('day', $daysLeft) . ' left';
                                            ?>
                                            <tr
                                                class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:text-white transition duration-150">
                                                <td class="py-2 px-2">
                                                    <span class="text-gray-900 dark:text-white"
                                                        title="<?php echo e($payment->project?->title ?? 'N/A'); ?>">
                                                        <?php echo e($payment->project?->title ?? 'N/A'); ?>

                                                    </span>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <div class="text-gray-900 dark:text-white">
                                                        <?php echo e($payment->due_date->format('M d, Y')); ?></div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        <?php echo e($payment->due_date->diffForHumans()); ?></div>
                                                </td>
                                                <td class="py-2 px-2 text-right text-gray-900 dark:text-white">
                                                    ₹<?php echo e(number_format($payment->amount, 2)); ?>

                                                </td>
                                                <td class="py-2 px-2 text-center">
                                                    <span
                                                        class="text-xs px-2 py-1 rounded-full inline-block <?php echo e($statusClass); ?>">
                                                        <?php echo e($statusText); ?>

                                                    </span>
                                                </td>
                                                <td class="py-2 px-2 text-right">
                                                    <a href="<?php echo e(route('filament.admin.resources.payments.edit', $payment)); ?>"
                                                        class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 inline-flex items-center justify-center transition-colors"
                                                        title="Edit Payment">
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-pencil-square'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if($payments->isEmpty()): ?>
                                            <tr>
                                                <td colspan="5" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                                    No upcoming payments found
                                                </td>
                                            </tr>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php break; ?>

                    <?php case ('ProjectStatusWidget'): ?>
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Project Status</h2>
                                <a href="<?php echo e(route('filament.admin.resources.projects.index')); ?>"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View Projects →
                                </a>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">In Progress</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white"><?php echo e(rand(1, 10)); ?></p>
                                        </div>
                                        <div class="text-primary-500 dark:text-blue-500">
                                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-clock'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Completed</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white"><?php echo e(rand(1, 10)); ?></p>
                                        </div>
                                        <div class="text-green-600 dark:text-green-500">
                                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-check-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">On Hold</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white"><?php echo e(rand(1, 10)); ?></p>
                                        </div>
                                        <div class="text-yellow-600 dark:text-yellow-500">
                                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-pause'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Cancelled</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white"><?php echo e(rand(1, 10)); ?></p>
                                        </div>
                                        <div class="text-red-600 dark:text-red-500">
                                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-x-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php break; ?>
                    <?php case ('MonthlyRevenueWidget'): ?>
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Monthly Revenue</h2>
                                <a href="<?php echo e(route('filament.admin.resources.payments.index')); ?>"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View Payments →
                                </a>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-4">
                                    <div>
                                        <span
                                            class="text-2xl font-bold text-gray-900 dark:text-white">₹<?php echo e(number_format(rand(100000, 500000), 2)); ?></span>
                                        <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">This Month</span>
                                    </div>
                                    <div class="flex items-center text-green-600 dark:text-green-500">
                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-arrow-trending-up'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 mr-1']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        <span class="text-sm font-medium">+<?php echo e(rand(5, 25)); ?>%</span>
                                    </div>
                                </div>
                                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const ctx = document.getElementById('monthlyRevenueChart').getContext('2d');
            const chartData = {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Monthly Revenue',
                    data: [1632000, 800000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Example data
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            };

            new Chart(ctx, {
                type: 'bar',
                data: chartData,
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>

    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <canvas id="monthlyRevenueChart" width="400" height="200"></canvas>
    </div>


                            </div>
                        </div>
                    <?php break; ?>

                    <?php default: ?>

                <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!--[if BLOCK]><![endif]--><?php if(count($widgets) === 0): ?>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
            <div class="text-center py-6">
                <div class="text-primary-500 dark:text-blue-500 mb-4">
                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-squares-2x2'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-12 h-12 mx-auto']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                </div>
                <h2 class="text-xl font-bold mb-2 text-gray-900 dark:text-white">No widgets configured</h2>
                <p class="text-gray-600 dark:text-gray-400 mb-4">You haven't configured any dashboard widgets yet.</p>
                <a href="<?php echo e(route('filament.admin.pages.dashboard-settings')); ?>"
                    class="inline-flex items-center px-4 py-2 bg-primary-600 dark:bg-blue-600 rounded-lg text-white hover:bg-primary-700 dark:hover:bg-blue-700 transition">
                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-cog-6-tooth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 mr-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                    Configure Dashboard
                </a>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->


 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\wamp64\www\smms\resources\views/filament/pages/dashboard.blade.php ENDPATH**/ ?>