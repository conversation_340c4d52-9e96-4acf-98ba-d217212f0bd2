{"__meta": {"id": "01JXBC60TVPTKG6XETV62W3ESH", "datetime": "2025-06-09 22:15:10", "utime": **********.428591, "method": "GET", "uri": "/admin/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 9, "messages": [{"message": "[22:15:10] LOG.info: [DASHBOARD] User widget configs {\n    \"user_id\": 1,\n    \"userConfigs\": {\n        \"1\": {\n            \"id\": 17,\n            \"user_id\": 1,\n            \"widget_id\": 1,\n            \"position\": 1,\n            \"width\": 12,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"2\": {\n            \"id\": 18,\n            \"user_id\": 1,\n            \"widget_id\": 2,\n            \"position\": 2,\n            \"width\": 3,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"3\": {\n            \"id\": 19,\n            \"user_id\": 1,\n            \"widget_id\": 3,\n            \"position\": 3,\n            \"width\": 6,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"4\": {\n            \"id\": 20,\n            \"user_id\": 1,\n            \"widget_id\": 4,\n            \"position\": 4,\n            \"width\": 3,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": false,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"5\": {\n            \"id\": 21,\n            \"user_id\": 1,\n            \"widget_id\": 5,\n            \"position\": 5,\n            \"width\": 12,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"6\": {\n            \"id\": 22,\n            \"user_id\": 1,\n            \"widget_id\": 6,\n            \"position\": 6,\n            \"width\": 12,\n            \"height\": 2,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:37:54.000000Z\"\n        },\n        \"7\": {\n            \"id\": 23,\n            \"user_id\": 1,\n            \"widget_id\": 7,\n            \"position\": 7,\n            \"width\": 12,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:38:18.000000Z\"\n        },\n        \"8\": {\n            \"id\": 24,\n            \"user_id\": 1,\n            \"widget_id\": 8,\n            \"position\": 8,\n            \"width\": 8,\n            \"height\": 2,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:37:54.000000Z\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.094214, "xdebug_link": null, "collector": "log"}, {"message": "[22:15:10] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 1,\n    \"name\": \"Total Projects\",\n    \"component\": \"TotalProjectsWidget\",\n    \"width\": 12,\n    \"height\": 1,\n    \"position\": 1,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.094651, "xdebug_link": null, "collector": "log"}, {"message": "[22:15:10] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 2,\n    \"name\": \"Total Clients\",\n    \"component\": \"TotalClientsWidget\",\n    \"width\": 3,\n    \"height\": 1,\n    \"position\": 2,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.094913, "xdebug_link": null, "collector": "log"}, {"message": "[22:15:10] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 3,\n    \"name\": \"Pending Payments\",\n    \"component\": \"PendingPaymentsWidget\",\n    \"width\": 6,\n    \"height\": 1,\n    \"position\": 3,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.095133, "xdebug_link": null, "collector": "log"}, {"message": "[22:15:10] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 5,\n    \"name\": \"Recent Projects\",\n    \"component\": \"RecentProjectsWidget\",\n    \"width\": 12,\n    \"height\": 3,\n    \"position\": 5,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.095361, "xdebug_link": null, "collector": "log"}, {"message": "[22:15:10] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 6,\n    \"name\": \"Upcoming Payments\",\n    \"component\": \"UpcomingPaymentsWidget\",\n    \"width\": 12,\n    \"height\": 2,\n    \"position\": 6,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.095569, "xdebug_link": null, "collector": "log"}, {"message": "[22:15:10] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 7,\n    \"name\": \"Project Status\",\n    \"component\": \"ProjectStatusWidget\",\n    \"width\": 12,\n    \"height\": 3,\n    \"position\": 7,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.095771, "xdebug_link": null, "collector": "log"}, {"message": "[22:15:10] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 8,\n    \"name\": \"Monthly Revenue\",\n    \"component\": \"MonthlyRevenueWidget\",\n    \"width\": 8,\n    \"height\": 2,\n    \"position\": 8,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.095973, "xdebug_link": null, "collector": "log"}, {"message": "[22:15:10] LOG.info: [DASHBOARD] Final widgets for view [\n    {\n        \"id\": 1,\n        \"name\": \"Total Projects\",\n        \"component\": \"TotalProjectsWidget\",\n        \"width\": 12,\n        \"height\": 1,\n        \"position\": 1,\n        \"settings\": null\n    },\n    {\n        \"id\": 2,\n        \"name\": \"Total Clients\",\n        \"component\": \"TotalClientsWidget\",\n        \"width\": 3,\n        \"height\": 1,\n        \"position\": 2,\n        \"settings\": null\n    },\n    {\n        \"id\": 3,\n        \"name\": \"Pending Payments\",\n        \"component\": \"PendingPaymentsWidget\",\n        \"width\": 6,\n        \"height\": 1,\n        \"position\": 3,\n        \"settings\": null\n    },\n    {\n        \"id\": 5,\n        \"name\": \"Recent Projects\",\n        \"component\": \"RecentProjectsWidget\",\n        \"width\": 12,\n        \"height\": 3,\n        \"position\": 5,\n        \"settings\": null\n    },\n    {\n        \"id\": 6,\n        \"name\": \"Upcoming Payments\",\n        \"component\": \"UpcomingPaymentsWidget\",\n        \"width\": 12,\n        \"height\": 2,\n        \"position\": 6,\n        \"settings\": null\n    },\n    {\n        \"id\": 7,\n        \"name\": \"Project Status\",\n        \"component\": \"ProjectStatusWidget\",\n        \"width\": 12,\n        \"height\": 3,\n        \"position\": 7,\n        \"settings\": null\n    },\n    {\n        \"id\": 8,\n        \"name\": \"Monthly Revenue\",\n        \"component\": \"MonthlyRevenueWidget\",\n        \"width\": 8,\n        \"height\": 2,\n        \"position\": 8,\n        \"settings\": null\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.096145, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749507307.849575, "end": **********.428625, "duration": 2.579050064086914, "duration_str": "2.58s", "measures": [{"label": "Booting", "start": 1749507307.849575, "relative_start": 0, "end": **********.513935, "relative_end": **********.513935, "duration": 1.****************, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.513952, "relative_start": 1.***************, "end": **********.428629, "relative_end": 3.814697265625e-06, "duration": 0.****************, "duration_str": "915ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.988246, "relative_start": 2.****************, "end": **********.992416, "relative_end": **********.992416, "duration": 0.004169940948486328, "duration_str": "4.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.pages.dashboard", "start": **********.115434, "relative_start": 2.****************, "end": **********.115434, "relative_end": **********.115434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f2f44b54be1e6b3a1bd77567b74ada6e", "start": **********.123432, "relative_start": 2.****************, "end": **********.123432, "relative_end": **********.123432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::543962272ccef1b0087ff8de93e25a12", "start": **********.125794, "relative_start": 2.276218891143799, "end": **********.125794, "relative_end": **********.125794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dbc0ef3ed3193e87849b3ea3fba36a2f", "start": **********.129527, "relative_start": 2.279952049255371, "end": **********.129527, "relative_end": **********.129527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b528d29c191f34648130e512c6c14c42", "start": **********.138271, "relative_start": 2.288696050643921, "end": **********.138271, "relative_end": **********.138271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d691f9b9b8ea42c9565d0eae88a8a7a", "start": **********.143678, "relative_start": 2.294102907180786, "end": **********.143678, "relative_end": **********.143678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.164405, "relative_start": 2.3148300647735596, "end": **********.164405, "relative_end": **********.164405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.169843, "relative_start": 2.320267915725708, "end": **********.169843, "relative_end": **********.169843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.17082, "relative_start": 2.321244955062866, "end": **********.17082, "relative_end": **********.17082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.171307, "relative_start": 2.3217320442199707, "end": **********.171307, "relative_end": **********.171307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.171541, "relative_start": 2.3219659328460693, "end": **********.171541, "relative_end": **********.171541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.171925, "relative_start": 2.322350025177002, "end": **********.171925, "relative_end": **********.171925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.172158, "relative_start": 2.322582960128784, "end": **********.172158, "relative_end": **********.172158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.172492, "relative_start": 2.3229169845581055, "end": **********.172492, "relative_end": **********.172492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.172702, "relative_start": 2.323127031326294, "end": **********.172702, "relative_end": **********.172702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.173017, "relative_start": 2.323441982269287, "end": **********.173017, "relative_end": **********.173017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.181817, "relative_start": 2.332242012023926, "end": **********.181817, "relative_end": **********.181817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.183229, "relative_start": 2.3336539268493652, "end": **********.183229, "relative_end": **********.183229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.185129, "relative_start": 2.3355538845062256, "end": **********.185129, "relative_end": **********.185129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.186297, "relative_start": 2.336721897125244, "end": **********.186297, "relative_end": **********.186297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.187202, "relative_start": 2.3376269340515137, "end": **********.187202, "relative_end": **********.187202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6793bd8c17ae551c989e7d8702e297bd", "start": **********.188221, "relative_start": 2.3386459350585938, "end": **********.188221, "relative_end": **********.188221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::669f45ea592e06d462edef0dc7991631", "start": **********.190022, "relative_start": 2.340446949005127, "end": **********.190022, "relative_end": **********.190022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::920886b33da20750c767a455729f43a3", "start": **********.191447, "relative_start": 2.341871976852417, "end": **********.191447, "relative_end": **********.191447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::580d32f5fef9ea18b4f700add0d02b13", "start": **********.192829, "relative_start": 2.3432538509368896, "end": **********.192829, "relative_end": **********.192829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::09b71e88617c7cb829e8c2982af46b24", "start": **********.194488, "relative_start": 2.3449130058288574, "end": **********.194488, "relative_end": **********.194488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.226507, "relative_start": 2.37693190574646, "end": **********.226507, "relative_end": **********.226507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.294741, "relative_start": 2.4451658725738525, "end": **********.294741, "relative_end": **********.294741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.302932, "relative_start": 2.453356981277466, "end": **********.302932, "relative_end": **********.302932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.304707, "relative_start": 2.455132007598877, "end": **********.304707, "relative_end": **********.304707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.308343, "relative_start": 2.458767890930176, "end": **********.308343, "relative_end": **********.308343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.30988, "relative_start": 2.4603049755096436, "end": **********.30988, "relative_end": **********.30988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.315058, "relative_start": 2.4654829502105713, "end": **********.315058, "relative_end": **********.315058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.315428, "relative_start": 2.465852975845337, "end": **********.315428, "relative_end": **********.315428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.318426, "relative_start": 2.46885085105896, "end": **********.318426, "relative_end": **********.318426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.318935, "relative_start": 2.469359874725342, "end": **********.318935, "relative_end": **********.318935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.322101, "relative_start": 2.4725260734558105, "end": **********.322101, "relative_end": **********.322101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.322465, "relative_start": 2.4728899002075195, "end": **********.322465, "relative_end": **********.322465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.324806, "relative_start": 2.4752309322357178, "end": **********.324806, "relative_end": **********.324806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.325151, "relative_start": 2.4755759239196777, "end": **********.325151, "relative_end": **********.325151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.398943, "relative_start": 2.549367904663086, "end": **********.398943, "relative_end": **********.398943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.401156, "relative_start": 2.5515809059143066, "end": **********.401156, "relative_end": **********.401156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.411273, "relative_start": 2.5616979598999023, "end": **********.411273, "relative_end": **********.411273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.412717, "relative_start": 2.5631420612335205, "end": **********.412717, "relative_end": **********.412717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.414379, "relative_start": 2.5648038387298584, "end": **********.414379, "relative_end": **********.414379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.420797, "relative_start": 2.5712220668792725, "end": **********.421008, "relative_end": **********.421008, "duration": 0.0002110004425048828, "duration_str": "211μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.424739, "relative_start": 2.5751638412475586, "end": **********.42482, "relative_end": **********.42482, "duration": 8.106231689453125e-05, "duration_str": "81μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 48817488, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 45, "nb_templates": 45, "templates": [{"name": "filament.pages.dashboard", "param_count": null, "params": [], "start": **********.115408, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.phpfilament.pages.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "__components::f2f44b54be1e6b3a1bd77567b74ada6e", "param_count": null, "params": [], "start": **********.123414, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/f2f44b54be1e6b3a1bd77567b74ada6e.blade.php__components::f2f44b54be1e6b3a1bd77567b74ada6e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Ff2f44b54be1e6b3a1bd77567b74ada6e.blade.php&line=1", "ajax": false, "filename": "f2f44b54be1e6b3a1bd77567b74ada6e.blade.php", "line": "?"}}, {"name": "__components::543962272ccef1b0087ff8de93e25a12", "param_count": null, "params": [], "start": **********.125778, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/543962272ccef1b0087ff8de93e25a12.blade.php__components::543962272ccef1b0087ff8de93e25a12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F543962272ccef1b0087ff8de93e25a12.blade.php&line=1", "ajax": false, "filename": "543962272ccef1b0087ff8de93e25a12.blade.php", "line": "?"}}, {"name": "__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "param_count": null, "params": [], "start": **********.129509, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fdbc0ef3ed3193e87849b3ea3fba36a2f.blade.php&line=1", "ajax": false, "filename": "dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php", "line": "?"}}, {"name": "__components::b528d29c191f34648130e512c6c14c42", "param_count": null, "params": [], "start": **********.138253, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b528d29c191f34648130e512c6c14c42.blade.php__components::b528d29c191f34648130e512c6c14c42", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb528d29c191f34648130e512c6c14c42.blade.php&line=1", "ajax": false, "filename": "b528d29c191f34648130e512c6c14c42.blade.php", "line": "?"}}, {"name": "__components::1d691f9b9b8ea42c9565d0eae88a8a7a", "param_count": null, "params": [], "start": **********.14366, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php__components::1d691f9b9b8ea42c9565d0eae88a8a7a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php&line=1", "ajax": false, "filename": "1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.164388, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.16982, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.170804, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.171293, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.171528, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.171911, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.172145, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.172479, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.172689, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.173005, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.1818, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.183195, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.185077, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.186281, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.187187, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::6793bd8c17ae551c989e7d8702e297bd", "param_count": null, "params": [], "start": **********.188207, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/6793bd8c17ae551c989e7d8702e297bd.blade.php__components::6793bd8c17ae551c989e7d8702e297bd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F6793bd8c17ae551c989e7d8702e297bd.blade.php&line=1", "ajax": false, "filename": "6793bd8c17ae551c989e7d8702e297bd.blade.php", "line": "?"}}, {"name": "__components::669f45ea592e06d462edef0dc7991631", "param_count": null, "params": [], "start": **********.190006, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/669f45ea592e06d462edef0dc7991631.blade.php__components::669f45ea592e06d462edef0dc7991631", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F669f45ea592e06d462edef0dc7991631.blade.php&line=1", "ajax": false, "filename": "669f45ea592e06d462edef0dc7991631.blade.php", "line": "?"}}, {"name": "__components::920886b33da20750c767a455729f43a3", "param_count": null, "params": [], "start": **********.191423, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/920886b33da20750c767a455729f43a3.blade.php__components::920886b33da20750c767a455729f43a3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F920886b33da20750c767a455729f43a3.blade.php&line=1", "ajax": false, "filename": "920886b33da20750c767a455729f43a3.blade.php", "line": "?"}}, {"name": "__components::580d32f5fef9ea18b4f700add0d02b13", "param_count": null, "params": [], "start": **********.192802, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/580d32f5fef9ea18b4f700add0d02b13.blade.php__components::580d32f5fef9ea18b4f700add0d02b13", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F580d32f5fef9ea18b4f700add0d02b13.blade.php&line=1", "ajax": false, "filename": "580d32f5fef9ea18b4f700add0d02b13.blade.php", "line": "?"}}, {"name": "__components::09b71e88617c7cb829e8c2982af46b24", "param_count": null, "params": [], "start": **********.194473, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/09b71e88617c7cb829e8c2982af46b24.blade.php__components::09b71e88617c7cb829e8c2982af46b24", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F09b71e88617c7cb829e8c2982af46b24.blade.php&line=1", "ajax": false, "filename": "09b71e88617c7cb829e8c2982af46b24.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.226487, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.29472, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}}, {"name": "filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.302913, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}}, {"name": "__components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.304689, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.308326, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.309861, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.315038, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.31541, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.318407, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.318918, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.322084, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.322449, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.32479, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.325137, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.398923, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}}, {"name": "filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.40106, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.411247, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}, {"name": "__components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.412694, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}}, {"name": "components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.414361, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}}]}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05946, "accumulated_duration_str": "59.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq' limit 1", "type": "query", "params": [], "bindings": ["yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.013114, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 4.625}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.020545, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.625, "width_percent": 2.96}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.0262551, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.585, "width_percent": 1.934}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0336392, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.519, "width_percent": 4.036}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.037738, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.555, "width_percent": 0.757}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.0491629, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.312, "width_percent": 0.992}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.0521588, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.304, "width_percent": 1.85}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.0547268, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.154, "width_percent": 0.656}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.055994, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.81, "width_percent": 1.362}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.057706, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.173, "width_percent": 0.555}, {"sql": "select * from `dashboard_widgets` where `is_available` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 75}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.067075, "duration": 0.01122, "duration_str": "11.22ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:75", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=75", "ajax": false, "filename": "Dashboard.php", "line": "75"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.728, "width_percent": 18.87}, {"sql": "select * from `dashboard_configs` where `user_id` = 1 order by `position` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.0801618, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:80", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=80", "ajax": false, "filename": "Dashboard.php", "line": "80"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.597, "width_percent": 4.272}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanAuthorizeAccess.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanAuthorizeAccess.php", "line": 9}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.102833, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.869, "width_percent": 3.246}, {"sql": "select count(*) as aggregate from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 219}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.131866, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:219", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 219}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=219", "ajax": false, "filename": "dashboard.blade.php", "line": "219"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.115, "width_percent": 6.475}, {"sql": "select count(*) as aggregate from `clients`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 257}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.140283, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:257", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 257}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=257", "ajax": false, "filename": "dashboard.blade.php", "line": "257"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.59, "width_percent": 2.876}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 295}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1445808, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:295", "source": {"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 295}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=295", "ajax": false, "filename": "dashboard.blade.php", "line": "295"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.466, "width_percent": 8.409}, {"sql": "select * from `projects` order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 369}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.151205, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:369", "source": {"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 369}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=369", "ajax": false, "filename": "dashboard.blade.php", "line": "369"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.875, "width_percent": 3.145}, {"sql": "select * from `clients` where `clients`.`id` in (1, 6, 7, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 369}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1559072, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:369", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 369}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=369", "ajax": false, "filename": "dashboard.blade.php", "line": "369"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.02, "width_percent": 3.767}, {"sql": "select * from `users` where `users`.`id` in (1, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 369}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1594381, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:369", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 369}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=369", "ajax": false, "filename": "dashboard.blade.php", "line": "369"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.787, "width_percent": 2.304}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1, 4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 369}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1619182, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:369", "source": {"index": 25, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 369}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=369", "ajax": false, "filename": "dashboard.blade.php", "line": "369"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.091, "width_percent": 0.824}, {"sql": "select count(*) as aggregate from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 444}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1732938, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:444", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=444", "ajax": false, "filename": "dashboard.blade.php", "line": "444"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.915, "width_percent": 0.706}, {"sql": "select * from `payments` where `status` = 'pending' order by `due_date` asc limit 5", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 488}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.174766, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:488", "source": {"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 488}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=488", "ajax": false, "filename": "dashboard.blade.php", "line": "488"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.622, "width_percent": 3.549}, {"sql": "select * from `projects` where `projects`.`id` in (1, 3, 5, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 488}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.178081, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:488", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 488}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=488", "ajax": false, "filename": "dashboard.blade.php", "line": "488"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.17, "width_percent": 1.093}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.266948, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.263, "width_percent": 1.598}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.296929, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.861, "width_percent": 7.114}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 63}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.305698, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.975, "width_percent": 1.766}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.3123899, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:123", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=123", "ajax": false, "filename": "notification-bell.blade.php", "line": "123"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.741, "width_percent": 2.489}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.3161569, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:123", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=123", "ajax": false, "filename": "notification-bell.blade.php", "line": "123"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.23, "width_percent": 1.245}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.319741, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:123", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=123", "ajax": false, "filename": "notification-bell.blade.php", "line": "123"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.475, "width_percent": 2.287}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.3231251, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:123", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=123", "ajax": false, "filename": "notification-bell.blade.php", "line": "123"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.762, "width_percent": 1.127}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.3258412, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:123", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=123", "ajax": false, "filename": "notification-bell.blade.php", "line": "123"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.889, "width_percent": 1.06}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiWHZ3U0VLMm5NSjIxR3d2WG5ndThPVmRna3VWMGcxTG1Tb0hFeVhNbiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYWRtaW4vZGFzaGJvYXJkIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjg6ImZpbGFtZW50IjthOjA6e319', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0' where `id` = 'yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiWHZ3U0VLMm5NSjIxR3d2WG5ndThPVmRna3VWMGcxTG1Tb0hFeVhNbiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYWRtaW4vZGFzaGJvYXJkIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjg6ImZpbGFtZW50IjthOjA6e319", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.422496, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.948, "width_percent": 2.052}]}, "models": {"data": {"App\\Models\\Project": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\DashboardWidget": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FDashboardWidget.php&line=1", "ajax": false, "filename": "DashboardWidget.php", "line": "?"}}, "App\\Models\\DashboardConfig": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FDashboardConfig.php&line=1", "ajax": false, "filename": "DashboardConfig.php", "line": "?"}}, "App\\Models\\Payment": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\Client": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 48, "is_counter": true}, "livewire": {"data": {"app.filament.pages.dashboard #fZyLRQT4rCg5lG83eflW": "array:4 [\n  \"data\" => array:15 [\n    \"widgets\" => array:7 [\n      0 => array:7 [\n        \"id\" => 1\n        \"name\" => \"Total Projects\"\n        \"component\" => \"TotalProjectsWidget\"\n        \"width\" => 12\n        \"height\" => 1\n        \"position\" => 1\n        \"settings\" => null\n      ]\n      1 => array:7 [\n        \"id\" => 2\n        \"name\" => \"Total Clients\"\n        \"component\" => \"TotalClientsWidget\"\n        \"width\" => 3\n        \"height\" => 1\n        \"position\" => 2\n        \"settings\" => null\n      ]\n      2 => array:7 [\n        \"id\" => 3\n        \"name\" => \"Pending Payments\"\n        \"component\" => \"PendingPaymentsWidget\"\n        \"width\" => 6\n        \"height\" => 1\n        \"position\" => 3\n        \"settings\" => null\n      ]\n      3 => array:7 [\n        \"id\" => 5\n        \"name\" => \"Recent Projects\"\n        \"component\" => \"RecentProjectsWidget\"\n        \"width\" => 12\n        \"height\" => 3\n        \"position\" => 5\n        \"settings\" => null\n      ]\n      4 => array:7 [\n        \"id\" => 6\n        \"name\" => \"Upcoming Payments\"\n        \"component\" => \"UpcomingPaymentsWidget\"\n        \"width\" => 12\n        \"height\" => 2\n        \"position\" => 6\n        \"settings\" => null\n      ]\n      5 => array:7 [\n        \"id\" => 7\n        \"name\" => \"Project Status\"\n        \"component\" => \"ProjectStatusWidget\"\n        \"width\" => 12\n        \"height\" => 3\n        \"position\" => 7\n        \"settings\" => null\n      ]\n      6 => array:7 [\n        \"id\" => 8\n        \"name\" => \"Monthly Revenue\"\n        \"component\" => \"MonthlyRevenueWidget\"\n        \"width\" => 8\n        \"height\" => 2\n        \"position\" => 8\n        \"settings\" => null\n      ]\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.dashboard\"\n  \"component\" => \"App\\Filament\\Pages\\Dashboard\"\n  \"id\" => \"fZyLRQT4rCg5lG83eflW\"\n]", "filament.livewire.global-search #50ur9yQlsR3po8vya0eo": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"50ur9yQlsR3po8vya0eo\"\n]", "app.filament.widgets.notification-components.notification-bell #dAIx5TshwuPY50TqtukH": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 10\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"dAIx5TshwuPY50TqtukH\"\n]", "filament.livewire.notifications #esLx8RMAiRfmBZPT9IMJ": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3574\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"esLx8RMAiRfmBZPT9IMJ\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 17, "messages": [{"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1472399017 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472399017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.244289, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-485307318 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485307318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.245732, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-280708706 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-280708706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.249226, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1347045465 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347045465\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.253312, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1235098143 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235098143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.25573, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1820877825 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820877825\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.257106, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1637283714 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637283714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.258266, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1861182305 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861182305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.259384, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-975484207 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975484207\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260358, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-831236112 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831236112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.26134, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1740706303 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740706303\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.262282, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1405447240 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405447240\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.263147, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1961325028 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961325028\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.264279, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2039304639 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2039304639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.265405, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1087143773 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087143773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.270158, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1083345112 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083345112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.2715, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-303251029 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-303251029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.283022, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard", "uri": "GET admin/dashboard", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "2.58s", "peak_memory": "54MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1269 characters\">sb-updates=3.7.1; kit_session=eyJpdiI6IkVxZ2lyckx1SVRub3NUVUFZWW1FNWc9PSIsInZhbHVlIjoiaEI2TGFtS1p0NmttY3lENC9OMjlDcFQwcFFaN2VXY1VQYm4rNUhSSXptYit4QmplUGFnYUF1ZXBYUnovY2pDbEFnM2VwQTgwRkdTMTgra2xOYlZKWDJWaWpRQmNLWTVnbGpWTHVieko0V3htVnByeVZRRHM2NU1MU1RGWEsvODgiLCJtYWMiOiI5YjM1NDkwOWZhOGZiYTE2NDRlMGQ1NmQzOGYwODEzNmE0MDUxOTZkMmE4NTc2NTAxMjRjYmFmMmU1MTllNDg0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhYeWUwYnFOSHhydjFyWFE3MU1yYlE9PSIsInZhbHVlIjoiUzcrOTRkaXRTbklxMHJ2SC9GZlhXa3huK3BwT0V0MXJlRU1kcFE1Um55eFZRSVEvM1dQV0QxOU9oUlF2d3BOTlRhdWMvRzdwcUFFQWdndEhrYjQvbU84SWdYbThnc0w5cW9jb3hwcjhKZGlRS1gzb0hVcUVJTjJmNUtteGFKQ0IiLCJtYWMiOiIxOTM3NmFhMWRmOGYyNGVhY2IwNTM4MTAzYjM1ZjA4ZTMwZjE0NGNmMDE3M2Y1NmM5OWMzZTQyZmUyMWE2MjZmIiwidGFnIjoiIn0%3D; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ii9BZkRxeXlQaENwK3RNSzFMTkV5Z2c9PSIsInZhbHVlIjoiNnBFelQwSGc4dXN1RXU2VEpXSHJjTVVsQUpucFdjVU9oeGVkUnhJZzBySGxLbHRQTFFHNldrVjhJcHpsUzV0c3ZSOFNlWmxkTUJoWFFaNlM3MTdzc0l4Q1JwV0pnS1JrTVlLdDJ6L0FwUEI4YTFZSkMyNExzTnoxWXJRZk9Wa1pMcHhoVjZiQ0F0WXFlUlU1bm9NaDVjSDJoVVVyWWhVZ0V1b2xMZFRkdXZlWWx6ak9qbjVoS3VXa3RFSmhScEl1UmY0TlZ0a0RIUm16am83MVRzaitxQmxSWHFGc3F2QlZVMW9kclB6UExUND0iLCJtYWMiOiI4NDIzOThjOTA3MWZkN2E0NjgxZjQ1MDQ0NDg4NzgyOTg0NDFjYzg1ZTRlZGNhM2FiNThhZDBmYzIzODNjNjQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-774289376 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sb-updates</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XvwSEK2nMJ21GwvXngu8OVdgkuV0g1LmSoHEyXMn</span>\"\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IaoVXF77cb5deCAtwKpLOWhQkSz6v8KjZyt6VaEz3KMxX2I2xibuZaQaf80m|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-774289376\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-869058475 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 09 Jun 2025 22:15:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IktyTVdGQ25KVUVIRGFvTjNzbThrSUE9PSIsInZhbHVlIjoieE1oZWM5eWFMWHVsK1VTbnZSbDlIckZ5SVhyME83cC90dFRHQk9iSVE1eDVxM3Vwa2l6ZlVucHJTZTlKSjErRVJ6bnhvWi8wRlBraUxRVW1kUFBySGcwYTJJOEY4ZHExem54c2VCeWRpNWx5MlhZMGhHT3VSbXg2ZkhpZnN2RTAiLCJtYWMiOiI3ZGMxNjhlODdhM2EwNDlkMzg0MzczYWIzMzRjNmNjNTVmOGUxNWI4NTg3Y2E2MzM3MzBlMDgwZDllZTNkNzJlIiwidGFnIjoiIn0%3D; expires=Tue, 10 Jun 2025 00:15:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IjlWcFNXTVE2TmRsMlZpVGpsWWRxQ1E9PSIsInZhbHVlIjoiN0xyR0tVOXRWV0pWNnRLV1FiT0dvdUpSejlSY2pJOGdUeE10anpsQmJYcElYdzQyN0p3RW1JNmVFZjhPNVBVSDBhQThzWDZzQmI5N01Kbk1MSHhndzMvVE1aOWpSQXkzU2hNTGhiUUp2WG54K0NOMkR0d2NIbjRFbHQ0bnVzVm4iLCJtYWMiOiI2ZTk1YzQ4ZDE5MGFjMjZlMzBkYTA5NzU5Y2NhYjFhYzlhZDcwNTlkMGJlYzk4MjYxM2Q1NjlkNmU2ODVmYWU1IiwidGFnIjoiIn0%3D; expires=Tue, 10 Jun 2025 00:15:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IktyTVdGQ25KVUVIRGFvTjNzbThrSUE9PSIsInZhbHVlIjoieE1oZWM5eWFMWHVsK1VTbnZSbDlIckZ5SVhyME83cC90dFRHQk9iSVE1eDVxM3Vwa2l6ZlVucHJTZTlKSjErRVJ6bnhvWi8wRlBraUxRVW1kUFBySGcwYTJJOEY4ZHExem54c2VCeWRpNWx5MlhZMGhHT3VSbXg2ZkhpZnN2RTAiLCJtYWMiOiI3ZGMxNjhlODdhM2EwNDlkMzg0MzczYWIzMzRjNmNjNTVmOGUxNWI4NTg3Y2E2MzM3MzBlMDgwZDllZTNkNzJlIiwidGFnIjoiIn0%3D; expires=Tue, 10-Jun-2025 00:15:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IjlWcFNXTVE2TmRsMlZpVGpsWWRxQ1E9PSIsInZhbHVlIjoiN0xyR0tVOXRWV0pWNnRLV1FiT0dvdUpSejlSY2pJOGdUeE10anpsQmJYcElYdzQyN0p3RW1JNmVFZjhPNVBVSDBhQThzWDZzQmI5N01Kbk1MSHhndzMvVE1aOWpSQXkzU2hNTGhiUUp2WG54K0NOMkR0d2NIbjRFbHQ0bnVzVm4iLCJtYWMiOiI2ZTk1YzQ4ZDE5MGFjMjZlMzBkYTA5NzU5Y2NhYjFhYzlhZDcwNTlkMGJlYzk4MjYxM2Q1NjlkNmU2ODVmYWU1IiwidGFnIjoiIn0%3D; expires=Tue, 10-Jun-2025 00:15:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869058475\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1544708996 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XvwSEK2nMJ21GwvXngu8OVdgkuV0g1LmSoHEyXMn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544708996\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard"}, "badge": null}}