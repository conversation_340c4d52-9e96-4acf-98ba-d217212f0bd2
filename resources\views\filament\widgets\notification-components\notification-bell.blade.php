<div x-data="{ open: false, init() { $watch('open', value => { if (value) { document.body.classList.add('overflow-y-hidden') } else { document.body.classList.remove('overflow-y-hidden') } }) } }" @click.away="open = false" wire:poll.30s="updateUnreadCount"
    class="relative notification-bell-wrapper">

    <button @click="open = !open"
        class="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none">
        <x-heroicon-o-bell class="w-5 h-5 text-gray-500 dark:text-gray-400" />

        @if ($unreadCount > 0)
            <span
                class="absolute top-0 right-0 flex h-5 w-5 items-center justify-center rounded-full bg-primary-500 text-[0.625rem] font-medium text-white">
                {{ $unreadCount > 9 ? '9+' : $unreadCount }}
            </span>
        @endif
    </button>

    <!-- Notification Panel -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform translate-y-4"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform translate-y-4"
         class="absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-2xl dark:bg-gray-800 dark:border-gray-700 overflow-hidden z-50"
         style="display: none;"
         @click.outside="open = false">

        <div
            class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
            <h3 class="text-base font-semibold text-gray-900 dark:text-white">Notifications</h3>

            @if ($unreadCount > 0)
                <button wire:click="markAllAsRead"
                    class="text-xs font-medium transition-colors duration-200 text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                    Mark all as read
                </button>
            @endif
        </div>

        <div
            class="max-h-[400px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
            @forelse($this->getNotifications() as $notification)
                <div
                    class="p-4 transition-colors duration-150 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div class="flex items-start justify-between">
                        <h4 class="pr-4 text-sm font-medium text-gray-900 dark:text-white">
                            {{ $notification->title }}</h4>
                        <button wire:click="markAsRead({{ $notification->id }})"
                            class="flex-shrink-0 p-1 text-gray-400 transition-colors duration-150 rounded-full hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600">
                            <x-heroicon-o-x-mark class="w-4 h-4" />
                        </button>
                    </div>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                        {{ $notification->message }}</p>
                    <div class="flex items-center justify-between mt-3">
                        <span class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <x-heroicon-o-clock class="w-3 h-3 mr-1" />
                            {{ $notification->created_at->diffForHumans() }}
                        </span>

                        <span
                            class="px-2 py-1 text-xs font-medium rounded-full bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                            {{ ucfirst($notification->notificationEvent->module) }}
                        </span>
                    </div>
                </div>
            @empty
                <div class="py-8 text-center text-gray-500 dark:text-gray-400">
                    <x-heroicon-o-bell-slash class="w-8 h-8 mx-auto mb-3 opacity-70" />
                    <p class="text-sm">No new notifications</p>
                </div>
            @endforelse
        </div>

        <div class="p-3 text-center border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
            <a href="{{ route('filament.admin.resources.app-notifications.index') }}"
                class="block w-full px-3 py-2 text-sm font-medium text-center transition-colors duration-200 rounded-md text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-gray-800">
                View all notifications
            </a>
        </div>
    </div>
</div>
