<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get client IDs
        $clients = \App\Models\Client::all();

        // Get user IDs (BDEs)
        $users = \App\Models\User::where('email', '!=', '<EMAIL>')->get();

        // Get project type IDs
        $projectTypes = \App\Models\ProjectType::all();

        // Create projects
        \App\Models\Project::create([
            'client_id' => $clients[0]->id,
            'user_id' => $users[0]->id,
            'project_type_id' => $projectTypes[0]->id, // Website Development
            'title' => 'Corporate Website Redesign',
            'description' => 'Complete redesign of corporate website with modern UI/UX',
            'won_date' => now()->subDays(60),
            'start_date' => now()->subDays(45),
            'end_date' => now()->addDays(45),
            'total_payment' => 250000,
            'duration' => 3,
            'duration_unit' => 'months',
            'payment_cycle' => 'monthly',
            'status' => 'active',
        ]);

        \App\Models\Project::create([
            'client_id' => $clients[1]->id,
            'user_id' => $users[1]->id,
            'project_type_id' => $projectTypes[1]->id, // Digital Marketing
            'title' => 'SEO Optimization Campaign',
            'description' => 'Comprehensive SEO optimization to improve search rankings',
            'won_date' => now()->subDays(90),
            'start_date' => now()->subDays(75),
            'end_date' => now()->addDays(110),
            'total_payment' => 180000,
            'duration' => 6,
            'duration_unit' => 'months',
            'payment_cycle' => 'monthly',
            'status' => 'active',
        ]);

        \App\Models\Project::create([
            'client_id' => $clients[2]->id,
            'user_id' => $users[0]->id,
            'project_type_id' => $projectTypes[2]->id, // Mobile App
            'title' => 'Mobile App Development',
            'description' => 'Development of a mobile app for both iOS and Android platforms',
            'won_date' => now()->subDays(120),
            'start_date' => now()->subDays(100),
            'end_date' => now()->addDays(80),
            'total_payment' => 450000,
            'duration' => 6,
            'duration_unit' => 'months',
            'payment_cycle' => 'monthly',
            'status' => 'active',
        ]);

        \App\Models\Project::create([
            'client_id' => $clients[3]->id,
            'user_id' => $users[1]->id,
            'project_type_id' => $projectTypes[3]->id, // E-commerce
            'title' => 'E-commerce Platform Development',
            'description' => 'Development of a complete e-commerce platform with payment integration',
            'won_date' => now()->subDays(150),
            'start_date' => now()->subDays(130),
            'end_date' => now()->addDays(50),
            'total_payment' => 350000,
            'duration' => 6,
            'duration_unit' => 'months',
            'payment_cycle' => 'monthly',
            'status' => 'active',
        ]);

        \App\Models\Project::create([
            'client_id' => $clients[4]->id,
            'user_id' => $users[2]->id,
            'project_type_id' => $projectTypes[4]->id, // Server Management
            'title' => 'Server Infrastructure Setup',
            'description' => 'Setup and configuration of server infrastructure with monitoring',
            'won_date' => now()->subDays(30),
            'start_date' => now()->subDays(20),
            'end_date' => now()->addDays(10),
            'total_payment' => 120000,
            'duration' => 1,
            'duration_unit' => 'months',
            'payment_cycle' => 'one_time',
            'status' => 'active',
        ]);
    }
}
