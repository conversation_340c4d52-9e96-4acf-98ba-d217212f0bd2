<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MilestoneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get projects
        $projects = \App\Models\Project::all();

        // For each project, create milestones
        foreach ($projects as $project) {
            // Create 3 milestones for each project

            // Milestone 1: Project Initiation (25%)
            \App\Models\Milestone::create([
                'project_id' => $project->id,
                'title' => 'Project Initiation',
                'description' => 'Initial project setup, requirements gathering, and planning',
                'due_date' => $project->start_date->addDays(15),
                'percentage' => 25,
                'amount' => $project->total_payment * 0.25,
                'status' => 'completed',
            ]);

            // Milestone 2: Development Phase (50%)
            \App\Models\Milestone::create([
                'project_id' => $project->id,
                'title' => 'Development Phase',
                'description' => 'Main development phase of the project',
                'due_date' => $project->start_date->addDays(45),
                'percentage' => 50,
                'amount' => $project->total_payment * 0.5,
                'status' => $project->start_date->addDays(45)->isPast() ? 'completed' : 'in_progress',
            ]);

            // Milestone 3: Project Completion (25%)
            \App\Models\Milestone::create([
                'project_id' => $project->id,
                'title' => 'Project Completion',
                'description' => 'Final delivery, testing, and project closure',
                'due_date' => $project->end_date,
                'percentage' => 25,
                'amount' => $project->total_payment * 0.25,
                'status' => $project->end_date->isPast() ? 'completed' : 'pending',
            ]);
        }
    }
}
