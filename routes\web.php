<?php

use Illuminate\Support\Facades\Route;
use App\Filament\Resources\ClientResource\Pages\ViewClient;

// Route::get('/', function () {
//     return view('welcome');
// });

Route::get('/projects-demo', function () {
    return view('projects-demo');
});

Route::get('/projects-demo-advanced', function () {
    return view('projects-demo-advanced');
});


Route::resource('dashboards', App\Http\Controllers\DashboardController::class)->only('index');

Route::resource('projects', App\Http\Controllers\ProjectController::class);

Route::resource('clients', App\Http\Controllers\ClientController::class);

Route::resource('milestones', App\Http\Controllers\MilestoneController::class);

Route::resource('payments', App\Http\Controllers\PaymentController::class);

Route::resource('incentives', App\Http\Controllers\IncentiveController::class);

Route::resource('reports', App\Http\Controllers\ReportController::class);

Route::resource('notifications', App\Http\Controllers\NotificationController::class);

// Test route for notifications
Route::get('/test-notification', [App\Http\Controllers\TestNotificationController::class, 'testNotification']);

Route::resource('users', App\Http\Controllers\UserController::class);

Route::get('/admin/clients/{record}/view', [ViewClient::class, 'render'])
    ->name('filament.resources.clients.view');
