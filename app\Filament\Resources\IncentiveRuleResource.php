<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IncentiveRuleResource\Pages;
use App\Filament\Resources\IncentiveRuleResource\RelationManagers;
use App\Models\IncentiveRule;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class IncentiveRuleResource extends Resource
{
    protected static ?string $model = IncentiveRule::class;

    protected static ?string $navigationIcon = 'heroicon-o-calculator';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Incentive Rule Details')
                    ->schema([
                        Forms\Components\Select::make('project_type_id')
                            ->relationship('projectType', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('role_id')
                            ->relationship('role', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('percentage')
                                    ->numeric()
                                    ->suffix('%')
                                    ->maxValue(100),
                                Forms\Components\TextInput::make('duration_percentage')
                                    ->required()
                                    ->numeric()
                                    ->suffix('%')
                                    ->maxValue(100)
                                    ->label('Duration Percentage (% of contract length)'),
                                Forms\Components\TextInput::make('fixed_amount')
                                    ->numeric()
                                    ->prefix('₹'),
                            ]),
                        Forms\Components\Select::make('currency')
                            ->options([
                                'INR' => 'Indian Rupee (₹)',
                                'USD' => 'US Dollar ($)',
                                'EUR' => 'Euro (€)',
                                'GBP' => 'British Pound (£)',
                            ])
                            ->default('INR')
                            ->required(),
                        Forms\Components\Select::make('calculation_type')
                            ->options([
                                'percentage' => 'Percentage of Contract Value',
                                'fixed' => 'Fixed Amount',
                                'hybrid' => 'Hybrid (Percentage + Fixed)',
                                'time_based' => 'Time-based (Monthly)',
                            ])
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('projectType.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('role.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('percentage')
                    ->numeric()
                    ->prefix(fn ($record) => $record->currency === 'USD' ? '$' : ($record->currency === 'INR' ? '₹' : ''))
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('duration_percentage')
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('fixed_amount')
                    ->money('INR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('calculation_type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'percentage' => 'success',
                        'fixed' => 'info',
                        'hybrid' => 'warning',
                        'time_based' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role_id')
                    ->label('Role')
                    ->relationship('role', 'name')
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIncentiveRules::route('/'),
            'create' => Pages\CreateIncentiveRule::route('/create'),
            'edit' => Pages\EditIncentiveRule::route('/{record}/edit'),
        ];
    }
}
