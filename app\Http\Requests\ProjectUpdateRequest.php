<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProjectUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'client_id' => ['required', 'integer', 'exists:clients,id'],
            'user_id' => ['required', 'integer', 'exists:users,id'],
            'project_type_id' => ['required', 'integer', 'exists:project_types,id'],
            'title' => ['required', 'string'],
            'description' => ['nullable', 'string'],
            'won_date' => ['required', 'date'],
            'start_date' => ['required', 'date'],
            'end_date' => ['nullable', 'date'],
            'total_payment' => ['required', 'numeric', 'between:-99999999.99,99999999.99'],
            'duration' => ['required', 'integer'],
            'duration_unit' => ['required', 'string'],
            'payment_cycle' => ['required', 'string'],
            'status' => ['required', 'string'],
        ];
    }
}
