<?php

namespace App\Http\Controllers;

use App\Http\Requests\NotificationStoreRequest;
use App\Http\Requests\NotificationUpdateRequest;
use App\Models\AppNotification;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class NotificationController extends Controller
{
    public function index(Request $request): View
    {
        $notifications = Notification::all();

        return view('notification.index', [
            'notifications' => $notifications,
        ]);
    }

    public function create(Request $request): View
    {
        return view('notification.create');
    }

    public function store(NotificationStoreRequest $request): RedirectResponse
    {
        $notification = Notification::create($request->validated());

        $request->session()->flash('notification.id', $notification->id);

        return redirect()->route('notifications.index');
    }

    public function show(Request $request, Notification $notification): View
    {
        return view('notification.show', [
            'notification' => $notification,
        ]);
    }

    public function edit(Request $request, Notification $notification): View
    {
        return view('notification.edit', [
            'notification' => $notification,
        ]);
    }

    public function update(NotificationUpdateRequest $request, Notification $notification): RedirectResponse
    {
        $notification->update($request->validated());

        $request->session()->flash('notification.id', $notification->id);

        return redirect()->route('notifications.index');
    }

    public function destroy(Request $request, Notification $notification): RedirectResponse
    {
        $notification->delete();

        return redirect()->route('notifications.index');
    }
}
