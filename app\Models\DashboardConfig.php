<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DashboardConfig extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'widget_id',
        'position',
        'width',
        'height',
        'settings',
        'is_enabled',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'settings' => 'json',
        'is_enabled' => 'boolean',
    ];

    /**
     * Get the user that owns the dashboard configuration.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
