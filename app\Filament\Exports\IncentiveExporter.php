<?php

namespace App\Filament\Exports;

use App\Models\Incentive;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class IncentiveExporter extends Exporter
{
    protected static ?string $model = Incentive::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')->label('ID'),
            ExportColumn::make('user.name')->label('BDE'),
            ExportColumn::make('project.title')->label('Project'),
            ExportColumn::make('amount')->label('Amount'),
            ExportColumn::make('calculation_date')->label('Calculation Date'),
            ExportColumn::make('payment_date')->label('Payment Date'),
            ExportColumn::make('status')->label('Status'),
            ExportColumn::make('approver.name')->label('Approved By'),
            ExportColumn::make('created_at')->label('Created At'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your incentive export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';
        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }
        return $body;
    }
}
