<?php

namespace App\Filament\Resources\NotificationRolePreferenceResource\Pages;

use App\Filament\Resources\NotificationRolePreferenceResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateNotificationRolePreference extends CreateRecord
{
    protected static string $resource = NotificationRolePreferenceResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
