<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained();
            $table->foreignId('user_id')->constrained();
            $table->foreignId('project_type_id')->constrained();
            $table->string('title');
            $table->text('description')->nullable();
            $table->date('won_date');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->decimal('total_payment', 10, 2);
            $table->integer('duration');
            $table->string('duration_unit');
            $table->string('payment_cycle');
            $table->string('status')->default('active');
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
