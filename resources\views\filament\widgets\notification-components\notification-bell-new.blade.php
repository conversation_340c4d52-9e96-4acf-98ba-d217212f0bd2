<div class="relative" x-data="{ open: false }" @click.away="open = false" wire:poll.30s="updateUnreadCount">
    <button @click="open = !open"
        class="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none">
        <x-heroicon-o-bell class="w-5 h-5 text-gray-500 dark:text-gray-400" />

        @if($unreadCount > 0)
            <span
                class="absolute top-0 right-0 flex h-5 w-5 items-center justify-center rounded-full bg-primary-500 text-[0.625rem] font-medium text-white">
                {{ $unreadCount > 9 ? '9+' : $unreadCount }}
            </span>
        @endif
    </button>

    <div 
        x-show="open" 
        x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="transform opacity-0 scale-95"
        x-transition:enter-end="transform opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="transform opacity-100 scale-100"
        x-transition:leave-end="transform opacity-0 scale-95"
        class="absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden z-[100] border border-gray-200 dark:border-gray-700"
        style="display: none;"
    >
        <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gray-50 dark:bg-gray-900">
            <h3 class="font-semibold text-base text-gray-900 dark:text-white">Notifications</h3>
            
            @if($unreadCount > 0)
                <button 
                    wire:click="markAllAsRead"
                    class="text-xs font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200"
                >
                    Mark all as read
                </button>
            @endif
        </div>
        
        <div class="max-h-[400px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
            @forelse($this->getNotifications() as $notification)
                <div class="p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                    <div class="flex justify-between items-start">
                        <h4 class="font-medium text-sm text-gray-900 dark:text-white pr-4">{{ $notification->title }}</h4>
                        <button 
                            wire:click="markAsRead({{ $notification->id }})"
                            class="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-150 flex-shrink-0"
                        >
                            <x-heroicon-o-x-mark class="w-4 h-4" />
                        </button>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">{{ $notification->message }}</p>
                    <div class="flex justify-between items-center mt-3">
                        <span class="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <x-heroicon-o-clock class="w-3 h-3 mr-1" />
                            {{ $notification->created_at->diffForHumans() }}
                        </span>
                        
                        <span class="text-xs px-2 py-1 rounded-full bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300 font-medium">
                            {{ ucfirst($notification->notificationEvent->module) }}
                        </span>
                    </div>
                </div>
            @empty
                <div class="py-8 text-center text-gray-500 dark:text-gray-400">
                    <x-heroicon-o-bell-slash class="w-8 h-8 mx-auto mb-3 opacity-70" />
                    <p class="text-sm">No new notifications</p>
                </div>
            @endforelse
        </div>
        
        <div class="p-3 border-t border-gray-200 dark:border-gray-700 text-center bg-gray-50 dark:bg-gray-900">
            <a 
                href="{{ route('filament.admin.resources.app-notifications.index') }}" 
                class="block w-full py-2 px-3 text-sm font-medium text-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200"
            >
                View all notifications
            </a>
        </div>
    </div>
</div>
