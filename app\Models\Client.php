<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'company_name',
        'company_email', // was email
        'phone',
        'address',
        'contact_person',
        'status',
        'tax_id',
        'registered_address',
        'official_email',
        'company_number', // was contact_number
        'personnel_details', // JSON column
        'social_media_access', // JSON column
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'personnel_details' => 'array',
        'social_media_access' => 'array',
    ];

    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    public function milestones()
    {
        return $this->hasManyThrough(
            \App\Models\Milestone::class,
            \App\Models\Project::class,
            'client_id', // Foreign key on projects table
            'project_id', // Foreign key on milestones table
            'id', // Local key on clients table
            'id'  // Local key on projects table
        );
    }

    public function payments()
    {
        return $this->hasManyThrough(
            \App\Models\Payment::class,
            \App\Models\Project::class,
            'client_id', // Foreign key on projects table
            'project_id', // Foreign key on payments table
            'id', // Local key on clients table
            'id'  // Local key on projects table
        );
    }

    public function incentives()
    {
        return $this->hasManyThrough(
            \App\Models\Incentive::class,
            \App\Models\Project::class,
            'client_id', // Foreign key on projects table
            'project_id', // Foreign key on incentives table
            'id', // Local key on clients table
            'id'  // Local key on projects table
        );
    }
}
