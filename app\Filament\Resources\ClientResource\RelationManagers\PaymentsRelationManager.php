<?php

namespace App\Filament\Resources\ClientResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class PaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'payments';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('project_id')
                    ->label('Project')
                    ->options(fn ($livewire) => \App\Models\Project::where('client_id', $livewire->ownerRecord->id ?? null)
                        ->pluck('title', 'id'))
                    ->searchable()
                    ->preload()
                    ->required()
                    ->reactive(),
                Forms\Components\Select::make('milestone_id')
                    ->label('Milestone')
                    ->options(function (callable $get) {
                        $projectId = $get('project_id');
                        if (!$projectId) {
                            return [];
                        }
                        return \App\Models\Milestone::where('project_id', $projectId)
                            ->pluck('title', 'id')
                            ->toArray();
                    })
                    ->searchable()
                    ->preload()
                    ->required()
                    ->disabled(fn (callable $get) => !$get('project_id'))
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                        // Auto-calculate amount based on selected milestone
                        $milestone = \App\Models\Milestone::find($state);
                        if ($milestone) {
                            $set('amount', $milestone->amount);
                        } else {
                            $set('amount', '');
                        }
                    }),
                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->numeric(), // removed prefix
                Forms\Components\DatePicker::make('due_date')
                    ->required(),
                Forms\Components\DatePicker::make('paid_date'),
                Forms\Components\Select::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'paid' => 'Paid',
                        'overdue' => 'Overdue',
                        'cancelled' => 'Cancelled',
                    ])
                    ->default('pending')
                    ->required(),
                Forms\Components\Select::make('payment_method')
                    ->options([
                        'bank_transfer' => 'Bank Transfer',
                        'credit_card' => 'Credit Card',
                        'upi' => 'UPI',
                        'cash' => 'Cash',
                        'cheque' => 'Cheque',
                        'other' => 'Other',
                    ]),
                Forms\Components\TextInput::make('transaction_id')
                    ->maxLength(255),
                Forms\Components\Textarea::make('notes')
                    ->maxLength(65535)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('amount')
            ->columns([
                Tables\Columns\TextColumn::make('milestone.title')->label('Milestone')->sortable(),
                Tables\Columns\TextColumn::make('amount')->money('INR')->sortable(),
                Tables\Columns\TextColumn::make('due_date')->date()->sortable(),
                Tables\Columns\TextColumn::make('paid_date')->date()->sortable(),
                Tables\Columns\TextColumn::make('status')->badge()->color(fn($state) => $state === 'paid' ? 'success' : 'gray'),
                Tables\Columns\TextColumn::make('payment_method')->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
