<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Incentive extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'project_id',
        'milestone_id',
        'incentive_rule_id', // added
        'amount',
        'calculation_date',
        'payment_date',
        'status',
        'description',
        'approved_by',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'project_id' => 'integer',
        'milestone_id' => 'integer',
        'incentive_rule_id' => 'integer', // added
        'amount' => 'decimal:2',
        'calculation_date' => 'date',
        'payment_date' => 'date',
        'approved_by' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function incentiveRule(): BelongsTo
    {
        return $this->belongsTo(IncentiveRule::class);
    }

    protected static function booted()
    {
        static::creating(function ($incentive) {
            if (empty($incentive->incentive_rule_id)) {
                $project = \App\Models\Project::find($incentive->project_id ?? null);
                $milestone = \App\Models\Milestone::find($incentive->milestone_id ?? null);
                $user = \App\Models\User::find($incentive->user_id ?? null);
                \Log::info('[INCENTIVE_MODEL] creating: lookup', [
                    'project_id' => $incentive->project_id ?? null,
                    'milestone_id' => $incentive->milestone_id ?? null,
                    'user_id' => $incentive->user_id ?? null,
                    'project' => $project?->id,
                    'milestone' => $milestone?->id,
                    'user' => $user?->id,
                ]);
                if ($project && $milestone && $user) {
                    $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                        ->where('model_type', 'App\\Models\\User')
                        ->where('model_id', $user->id)
                        ->value('role_id');
                    \Log::info('[INCENTIVE_MODEL] creating: roleId', ['role_id' => $roleId]);
                    $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                        ->where('role_id', $roleId)
                        ->where('duration_percentage', '<=', $milestone->percentage)
                        ->orderByDesc('duration_percentage')
                        ->first();
                    \Log::info('[INCENTIVE_MODEL] creating: rule', ['rule_id' => $rule?->id]);
                    if ($rule) {
                        $incentive->incentive_rule_id = $rule->id;
                    }
                }
            }
        });
        static::updating(function ($incentive) {
            if (empty($incentive->incentive_rule_id)) {
                $project = \App\Models\Project::find($incentive->project_id ?? null);
                $milestone = \App\Models\Milestone::find($incentive->milestone_id ?? null);
                $user = \App\Models\User::find($incentive->user_id ?? null);
                \Log::info('[INCENTIVE_MODEL] updating: lookup', [
                    'project_id' => $incentive->project_id ?? null,
                    'milestone_id' => $incentive->milestone_id ?? null,
                    'user_id' => $incentive->user_id ?? null,
                    'project' => $project?->id,
                    'milestone' => $milestone?->id,
                    'user' => $user?->id,
                ]);
                if ($project && $milestone && $user) {
                    $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                        ->where('model_type', 'App\\Models\\User')
                        ->where('model_id', $user->id)
                        ->value('role_id');
                    \Log::info('[INCENTIVE_MODEL] updating: roleId', ['role_id' => $roleId]);
                    $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                        ->where('role_id', $roleId)
                        ->where('duration_percentage', '<=', $milestone->percentage)
                        ->orderByDesc('duration_percentage')
                        ->first();
                    \Log::info('[INCENTIVE_MODEL] updating: rule', ['rule_id' => $rule?->id]);
                    if ($rule) {
                        $incentive->incentive_rule_id = $rule->id;
                    }
                }
            }
        });
    }
}
