<?php
namespace App\Observers;
use App\Models\Incentive;
use App\Services\NotificationService;

class IncentiveObserver
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function created(Incentive $incentive)
    {
        \Log::info('[INCENTIVE_OBSERVER] Incentive created', [
            'incentive_id' => $incentive->id,
            'user_id' => $incentive->user_id,
            'status' => $incentive->status,
        ]);

        // Prepare notification data
        $data = [
            'incentive_id' => $incentive->id,
            'user_name' => $incentive->user->name,
            'project_title' => $incentive->project->title,
            'amount' => $incentive->amount,
            'calculation_date' => $incentive->calculation_date,
        ];

        // Send notification
        $this->notificationService->send('incentive_submitted', $data);
    }

    public function updated(Incentive $incentive)
    {
        if ($incentive->isDirty('status')) {
            $oldStatus = $incentive->getOriginal('status');
            $newStatus = $incentive->status;
            \Log::info('[INCENTIVE_OBSERVER] Incentive status changed', [
                'incentive_id' => $incentive->id,
                'user_id' => $incentive->user_id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
            ]);
            if (in_array($newStatus, ['approved', 'paid', 'rejected'])) {
                $data = [
                    'incentive_id' => $incentive->id,
                    'user_name' => $incentive->user->name,
                    'project_title' => $incentive->project->title,
                    'amount' => $incentive->amount,
                    'status' => $newStatus,
                ];
                // Send notification to the incentive user (for bell icon)
                \Log::info('[INCENTIVE_OBSERVER] Sending notification to user', [
                    'user_id' => $incentive->user_id,
                    'status' => $newStatus,
                ]);
                $this->notificationService->send('incentive_status_changed', $data, $incentive->user);
                // Optionally, also send to approver if needed:
                if ($incentive->approved_by) {
                    \Log::info('[INCENTIVE_OBSERVER] Sending notification to approver', [
                        'approver_id' => $incentive->approved_by,
                        'status' => $newStatus,
                    ]);
                    $this->notificationService->send('incentive_status_changed', $data, $incentive->approver);
                }
                // Set notification status to sent
                $incentive->status = 'sent';
                $incentive->saveQuietly();
            }
        }
    }
}
