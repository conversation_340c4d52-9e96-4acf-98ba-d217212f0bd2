<?php

namespace App\Http\Controllers;

use App\Http\Requests\IncentiveStoreRequest;
use App\Http\Requests\IncentiveUpdateRequest;
use App\Models\Incentive;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class IncentiveController extends Controller
{
    public function index(Request $request): View
    {
        $incentives = Incentive::all();

        return view('incentive.index', [
            'incentives' => $incentives,
        ]);
    }

    public function create(Request $request): View
    {
        return view('incentive.create');
    }

    public function store(IncentiveStoreRequest $request): RedirectResponse
    {
        $incentive = Incentive::create($request->validated());

        $request->session()->flash('incentive.id', $incentive->id);

        return redirect()->route('incentives.index');
    }

    public function show(Request $request, Incentive $incentive): View
    {
        return view('incentive.show', [
            'incentive' => $incentive,
        ]);
    }

    public function edit(Request $request, Incentive $incentive): View
    {
        return view('incentive.edit', [
            'incentive' => $incentive,
        ]);
    }

    public function update(IncentiveUpdateRequest $request, Incentive $incentive): RedirectResponse
    {
        $incentive->update($request->validated());

        $request->session()->flash('incentive.id', $incentive->id);

        return redirect()->route('incentives.index');
    }

    public function destroy(Request $request, Incentive $incentive): RedirectResponse
    {
        $incentive->delete();

        return redirect()->route('incentives.index');
    }
}
