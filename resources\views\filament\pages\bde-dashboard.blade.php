<x-filament-panels::page>
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome, {{ auth()->user()->name }}</h1>
        <p class="text-gray-500 dark:text-gray-400">Here's an overview of your projects and activities</p>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <!-- Projects Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
            <div class="flex items-center gap-4">
                <div class="text-primary-500 dark:text-blue-500">
                    <x-heroicon-o-briefcase class="w-8 h-8" />
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                        {{ $this->getProjectsCount() }}
                    </h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Your Projects</p>
                </div>
            </div>
        </div>

        <!-- Clients Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
            <div class="flex items-center gap-4">
                <div class="text-primary-500 dark:text-blue-500">
                    <x-heroicon-o-users class="w-8 h-8" />
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                        {{ $this->getClientsCount() }}
                    </h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Your Clients</p>
                </div>
            </div>
        </div>

        <!-- Incentives Count Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
            <div class="flex items-center gap-4">
                <div class="text-primary-500 dark:text-blue-500">
                    <x-heroicon-o-banknotes class="w-8 h-8" />
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                        {{ $this->getIncentivesCount() }}
                    </h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Incentives</p>
                </div>
            </div>
        </div>

        <!-- Incentives Amount Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
            <div class="flex items-center gap-4">
                <div class="text-primary-500 dark:text-blue-500">
                    <x-heroicon-o-currency-rupee class="w-8 h-8" />
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                        ₹{{ number_format($this->getIncentivesAmount(), 2) }}
                    </h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Incentives</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Projects -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
            <h2 class="text-lg font-bold mb-4 text-gray-900 dark:text-white">Recent Projects</h2>
            
            @if($this->getRecentProjects()->count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th class="px-4 py-2">Title</th>
                                <th class="px-4 py-2">Client</th>
                                <th class="px-4 py-2">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($this->getRecentProjects() as $project)
                                <tr class="border-b dark:border-gray-700">
                                    <td class="px-4 py-2">
                                        <a href="{{ route('filament.admin.resources.projects.edit', $project) }}" class="text-primary-600 hover:underline">
                                            {{ $project->title }}
                                        </a>
                                    </td>
                                    <td class="px-4 py-2">{{ $project->client->name }}</td>
                                    <td class="px-4 py-2">
                                        <span class="px-2 py-1 text-xs rounded-full 
                                            @if($project->status === 'active') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                            @elseif($project->status === 'completed') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                            @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                            @elseif($project->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                            @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                            @endif">
                                            {{ ucfirst($project->status) }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-right">
                    <a href="{{ route('filament.admin.resources.projects.index') }}" class="text-primary-600 hover:underline text-sm">
                        View all projects →
                    </a>
                </div>
            @else
                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                    No projects found
                </div>
            @endif
        </div>

        <!-- Upcoming Milestones -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
            <h2 class="text-lg font-bold mb-4 text-gray-900 dark:text-white">Upcoming Milestones</h2>
            
            @if($this->getUpcomingMilestones()->count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th class="px-4 py-2">Title</th>
                                <th class="px-4 py-2">Project</th>
                                <th class="px-4 py-2">Due Date</th>
                                <th class="px-4 py-2">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($this->getUpcomingMilestones() as $milestone)
                                <tr class="border-b dark:border-gray-700">
                                    <td class="px-4 py-2">
                                        <a href="{{ route('filament.admin.resources.milestones.edit', $milestone) }}" class="text-primary-600 hover:underline">
                                            {{ $milestone->title }}
                                        </a>
                                    </td>
                                    <td class="px-4 py-2">{{ $milestone->project->title }}</td>
                                    <td class="px-4 py-2">{{ $milestone->due_date->format('M d, Y') }}</td>
                                    <td class="px-4 py-2">
                                        <span class="px-2 py-1 text-xs rounded-full 
                                            @if($milestone->status === 'pending') bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                            @elseif($milestone->status === 'in_progress') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                            @elseif($milestone->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                            @elseif($milestone->status === 'delayed') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                            @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                            @endif">
                                            {{ ucfirst(str_replace('_', ' ', $milestone->status)) }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-right">
                    <a href="{{ route('filament.admin.resources.milestones.index') }}" class="text-primary-600 hover:underline text-sm">
                        View all milestones →
                    </a>
                </div>
            @else
                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                    No upcoming milestones
                </div>
            @endif
        </div>
    </div>
</x-filament-panels::page>
