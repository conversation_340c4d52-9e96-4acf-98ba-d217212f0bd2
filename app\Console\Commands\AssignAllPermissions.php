<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AssignAllPermissions extends Command
{
    protected $signature = 'permissions:assign-all {email? : The email of the user to assign permissions to}';
    protected $description = 'Assign all permissions to a user or create a super admin role with all permissions';

    public function handle()
    {
        $email = $this->argument('email');

        if ($email) {
            $user = User::where('email', $email)->first();
            
            if (!$user) {
                $this->error("User with email {$email} not found.");
                return 1;
            }
            
            $this->assignPermissionsToUser($user);
        } else {
            $this->createOrUpdateSuperAdminRole();
        }
        
        return 0;
    }
    
    private function assignPermissionsToUser(User $user)
    {
        // Get or create super_admin role
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin']);
        
        // Assign all permissions to the role
        $permissions = Permission::all();
        $superAdminRole->syncPermissions($permissions);
        
        // Assign the role to the user
        $user->assignRole($superAdminRole);
        
        $this->info("All permissions assigned to user {$user->name} via super_admin role.");
        $this->info("Total permissions: {$permissions->count()}");
    }
    
    private function createOrUpdateSuperAdminRole()
    {
        // Get or create super_admin role
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin']);
        
        // Assign all permissions to the role
        $permissions = Permission::all();
        $superAdminRole->syncPermissions($permissions);
        
        $this->info("Super admin role created/updated with all permissions.");
        $this->info("Total permissions: {$permissions->count()}");
        $this->info("You can assign this role to a user with: php artisan permissions:assign-all <EMAIL>");
    }
}
