<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RoleNotificationSettings extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'role_id',
        'notification_settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'notification_settings' => 'array',
    ];

    /**
     * Get the role that owns the notification settings.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get a specific notification setting.
     *
     * @param int $eventId The notification event ID
     * @param string $key The setting key (email, in_app, is_enabled)
     * @param mixed $default The default value if the setting is not found
     * @return mixed The setting value
     */
    public function getSetting(int $eventId, string $key, $default = false)
    {
        if (!isset($this->notification_settings[$eventId])) {
            return $default;
        }

        return $this->notification_settings[$eventId][$key] ?? $default;
    }

    /**
     * Check if a notification event is enabled for this role.
     *
     * @param int $eventId The notification event ID
     * @return bool Whether the notification is enabled
     */
    public function isEventEnabled(int $eventId): bool
    {
        return $this->getSetting($eventId, 'is_enabled', false);
    }

    /**
     * Check if email notifications are enabled for this event and role.
     *
     * @param int $eventId The notification event ID
     * @return bool Whether email notifications are enabled
     */
    public function isEmailEnabled(int $eventId): bool
    {
        return $this->getSetting($eventId, 'email', false) && $this->isEventEnabled($eventId);
    }

    /**
     * Check if in-app notifications are enabled for this event and role.
     *
     * @param int $eventId The notification event ID
     * @return bool Whether in-app notifications are enabled
     */
    public function isInAppEnabled(int $eventId): bool
    {
        return $this->getSetting($eventId, 'in_app', false) && $this->isEventEnabled($eventId);
    }
}
