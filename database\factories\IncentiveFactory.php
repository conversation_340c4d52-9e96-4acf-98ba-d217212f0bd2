<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Incentive;
use App\Models\Project;
use App\Models\User;

class IncentiveFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Incentive::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'project_id' => Project::factory(),
            'amount' => fake()->randomFloat(2, 0, 99999999.99),
            'calculation_date' => fake()->date(),
            'payment_date' => fake()->date(),
            'status' => fake()->word(),
            'description' => fake()->text(),
            'approved_by' => User::factory()->create()->approved_by,
        ];
    }
}
