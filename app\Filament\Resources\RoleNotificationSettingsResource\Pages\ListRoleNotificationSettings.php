<?php

namespace App\Filament\Resources\RoleNotificationSettingsResource\Pages;

use App\Filament\Resources\RoleNotificationSettingsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRoleNotificationSettings extends ListRecords
{
    protected static string $resource = RoleNotificationSettingsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
