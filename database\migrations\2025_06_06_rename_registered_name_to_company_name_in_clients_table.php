<?php
// database/migrations/2025_06_06_rename_registered_name_to_company_name_in_clients_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->renameColumn('registered_name', 'company_name');
        });
    }
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->renameColumn('company_name', 'registered_name');
        });
    }
};
