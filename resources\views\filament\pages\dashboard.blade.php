@php
    use Illuminate\Support\Str;
@endphp

<x-filament-panels::page>
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">

    <style>
        /* Simple and reliable tooltip styling */
        .tooltip-container {
            position: relative;
            display: inline-block;
        }

        .tooltip-container:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        .tooltip-text {
            visibility: hidden;
            width: auto;
            min-width: 120px;
            background-color: #1f2937;
            color: white;
            text-align: center;
            border-radius: 6px;
            padding: 8px 12px;
            position: absolute;
            z-index: 9999;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s ease;
            font-size: 12px;
            white-space: nowrap;
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #1f2937 transparent transparent transparent;
        }

        /* Force show tooltips on hover for buttons */
        .fi-header button[title]:hover::after,
        .fi-header a[title]:hover::after,
        button[title]:hover::after,
        a[title]:hover::after {
            content: attr(title) !important;
            position: absolute !important;
            bottom: calc(100% + 5px) !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            background: #1f2937 !important;
            color: white !important;
            padding: 8px 12px !important;
            border-radius: 6px !important;
            font-size: 12px !important;
            white-space: nowrap !important;
            z-index: 99999 !important;
            pointer-events: none !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
            display: block !important;
        }

        .fi-header button[title]:hover::before,
        .fi-header a[title]:hover::before,
        button[title]:hover::before,
        a[title]:hover::before {
            content: '' !important;
            position: absolute !important;
            bottom: 100% !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            border: 5px solid transparent !important;
            border-top-color: #1f2937 !important;
            z-index: 99999 !important;
            pointer-events: none !important;
            display: block !important;
        }

        .fi-header button[title],
        .fi-header a[title],
        button[title],
        a[title] {
            position: relative !important;
        }
    </style>

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
        <div class="flex items-center space-x-4">
            <button type="button" wire:click="resetDashboard"
                class="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 flex items-center text-sm"
                title="Reset dashboard to default settings">
                <x-heroicon-o-arrow-path class="w-4 h-4 mr-1" />
                Reset
            </button>

            <a href="{{ route('filament.admin.pages.dashboard-settings') }}"
                class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 flex items-center"
                title="Customize dashboard widgets and layout">
                <x-heroicon-o-cog-6-tooth class="w-5 h-5 mr-1" />
                Customize
            </a>

            {{-- User Avatar --}}
            @php
                $user = auth()->user();
                $avatarUrl = $user && $user->avatar_url
                    ? (Str::startsWith($user->avatar_url, ['http://', 'https://', '/storage/'])
                        ? (Str::startsWith($user->avatar_url, '/storage/') ? asset(ltrim($user->avatar_url, '/')) : $user->avatar_url)
                        : asset('storage/' . ltrim($user->avatar_url, '/')))
                    : 'https://ui-avatars.com/api/?name=' . urlencode($user?->name ?? 'User');
            @endphp
            <img class="fi-avatar object-cover object-center fi-circular rounded-full h-8 w-8 fi-user-avatar" src="{{ $avatarUrl }}" alt="Avatar of {{ $user?->name ?? 'User' }}" onerror="this.onerror=null;this.src='https://ui-avatars.com/api/?name={{ urlencode($user?->name ?? 'User') }}';">
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        @foreach ($widgets as $widget)
            @php
                $widthClass = match ($widget['width']) {
                    3 => 'col-span-1', // 25% width (1 of 4 columns)
                    4 => 'col-span-1 sm:col-span-2 md:col-span-1', // 33% width (1 of 3 columns on medium screens)
                    6 => 'col-span-1 sm:col-span-2', // 50% width (2 of 4 columns)
                    8 => 'col-span-1 sm:col-span-2 lg:col-span-3', // 75% width (3 of 4 columns)
                    12 => 'col-span-1 sm:col-span-2 lg:col-span-4', // 100% width (4 of 4 columns)
                    default => 'col-span-1 sm:col-span-2', // Default to 50% width
                };

                $heightClass = match ($widget['height']) {
                    1 => 'h-auto',
                    2 => 'h-auto',
                    3 => 'h-auto',
                    default => 'h-auto',
                };
            @endphp

            <div class="{{ $widthClass }} {{ $heightClass }}">
                @switch($widget['component'])
                    @case('TotalProjectsWidget')
                    <a href="{{ route('filament.admin.resources.projects.index') }}" class="block">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500">
                                    <x-heroicon-o-briefcase class="w-8 h-8" />
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                                        {{ \App\Models\Project::count() }}
                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Projects</p>
                                </div>
                            </div>
                        </div>
                    </a>
                    @break

                    @case('TotalClientsWidget')
                    <a href="{{ route('filament.admin.resources.clients.index') }}" class="block">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-white">
                                    <x-heroicon-o-users class="w-8 h-8" />
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                                        {{ \App\Models\Client::count() }}
                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Clients</p>
                                </div>
                            </div>
                        </div>
                    </a>
                    @break

                    @case('PendingPaymentsWidget')
                    <a href="{{ route('filament.admin.resources.payments.index') }}" class="block">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-white">
                                    <x-heroicon-o-credit-card class="w-8 h-8" />
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                                        ₹{{ number_format(\App\Models\Payment::where('status', 'pending')->sum('amount'), 2) }}
                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Pending Payments</p>
                                </div>
                            </div>
                        </div>
                    </a>
                    @break

                    @case('ApprovedIncentivesWidget')
                    <a href="{{ route('filament.admin.resources.incentives.index') }}" class="block">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-white">
                                    <x-heroicon-o-banknotes class="w-8 h-8" />
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                                        ₹{{ number_format(\App\Models\Incentive::where('status', 'approved')->sum('amount'), 2) }}
                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Approved Incentives</p>
                                </div>
                            </div>
                        </div>
                    </a>
                    @break

                    @case('RecentProjectsWidget')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Recent Projects</h2>
                                <a href="{{ route('filament.admin.resources.projects.index') }}"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View All →
                                </a>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200 dark:border-gray-700">
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Project
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Client
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">BDE
                                            </th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Amount
                                            </th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (\App\Models\Project::with(['client', 'user'])->latest()->take(5)->get() as $project)
                                            <tr
                                                class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150">
                                                <td class="py-2 px-2">
                                                    <div class="flex items-center">
                                                        <div class="text-primary-500 dark:text-blue-500 mr-2">
                                                            <x-heroicon-o-document class="w-4 h-4" />
                                                        </div>
                                                        <span class="text-gray-900 dark:text-white hover:text-gray-900 dark:hover:text-white"
                                                            title="{{ $project->title }}">{{ $project->title }}</span>
                                                    </div>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <span
                                                        class="text-gray-900 dark:text-white">{{ $project->client->name }}</span>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <span
                                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                                        {{ $project->user->name }}
                                                    </span>
                                                </td>
                                                <td class="py-2 px-2 text-right text-gray-900 dark:text-white">
                                                    ₹{{ number_format($project->total_payment, 2) }}
                                                </td>
                                                <td class="py-2 px-2 text-right">
                                                    <a href="{{ route('filament.admin.resources.projects.edit', $project) }}"
                                                        class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 inline-flex items-center justify-center transition-colors"
                                                        title="Edit Project">
                                                        <x-heroicon-o-pencil-square class="w-4 h-4" />
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach

                                        @if (\App\Models\Project::count() === 0)
                                            <tr>
                                                <td colspan="5" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                                    No projects found
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @break

                    @case('UpcomingPaymentsWidget')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Upcoming Payments</h2>
                                <a href="{{ route('filament.admin.resources.payments.index') }}"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View All →
                                </a>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200 dark:border-gray-700">
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Project
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Due
                                                Date</th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Amount
                                            </th>
                                            <th class="text-center py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Status</th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                            $payments = \App\Models\Payment::with('project')
                                                ->where('status', 'pending')
                                                ->orderBy('due_date')
                                                ->take(5)
                                                ->get();
                                        @endphp

                                        @foreach ($payments as $payment)
                                            @php
                                                $isPast = $payment->due_date->isPast();
                                                $daysLeft = $isPast
                                                    ? $payment->due_date->diffInDays(now())
                                                    : now()->diffInDays($payment->due_date);
                                                $statusClass = $isPast
                                                    ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                                    : ($daysLeft <= 7
                                                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                                                        : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300');
                                                $statusText = $isPast
                                                    ? "Overdue by {$daysLeft} " . Str::plural('day', $daysLeft)
                                                    : "{$daysLeft} " . Str::plural('day', $daysLeft) . ' left';
                                            @endphp
                                            <tr
                                                class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:text-white transition duration-150">
                                                <td class="py-2 px-2">
                                                    <span class="text-gray-900 dark:text-white"
                                                        title="{{ $payment->project?->title ?? 'N/A' }}">
                                                        {{ $payment->project?->title ?? 'N/A' }}
                                                    </span>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <div class="text-gray-900 dark:text-white">
                                                        {{ $payment->due_date->format('M d, Y') }}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        {{ $payment->due_date->diffForHumans() }}</div>
                                                </td>
                                                <td class="py-2 px-2 text-right text-gray-900 dark:text-white">
                                                    ₹{{ number_format($payment->amount, 2) }}
                                                </td>
                                                <td class="py-2 px-2 text-center">
                                                    <span
                                                        class="text-xs px-2 py-1 rounded-full inline-block {{ $statusClass }}">
                                                        {{ $statusText }}
                                                    </span>
                                                </td>
                                                <td class="py-2 px-2 text-right">
                                                    <a href="{{ route('filament.admin.resources.payments.edit', $payment) }}"
                                                        class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 inline-flex items-center justify-center transition-colors"
                                                        title="Edit Payment">
                                                        <x-heroicon-o-pencil-square class="w-4 h-4" />
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach

                                        @if ($payments->isEmpty())
                                            <tr>
                                                <td colspan="5" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                                    No upcoming payments found
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @break

                    @case('ProjectStatusWidget')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Project Status</h2>
                                <a href="{{ route('filament.admin.resources.projects.index') }}"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View Projects →
                                </a>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">In Progress</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white">{{ rand(1, 10) }}</p>
                                        </div>
                                        <div class="text-primary-500 dark:text-blue-500">
                                            <x-heroicon-o-clock class="w-5 h-5" />
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Completed</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white">{{ rand(1, 10) }}</p>
                                        </div>
                                        <div class="text-green-600 dark:text-green-500">
                                            <x-heroicon-o-check-circle class="w-5 h-5" />
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">On Hold</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white">{{ rand(1, 10) }}</p>
                                        </div>
                                        <div class="text-yellow-600 dark:text-yellow-500">
                                            <x-heroicon-o-pause class="w-5 h-5" />
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Cancelled</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white">{{ rand(1, 10) }}</p>
                                        </div>
                                        <div class="text-red-600 dark:text-red-500">
                                            <x-heroicon-o-x-circle class="w-5 h-5" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @break
                    @case('MonthlyRevenueWidget')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Monthly Revenue</h2>
                                <a href="{{ route('filament.admin.resources.payments.index') }}"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View Payments →
                                </a>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-4">
                                    <div>
                                        <span
                                            class="text-2xl font-bold text-gray-900 dark:text-white">₹{{ number_format(rand(100000, 500000), 2) }}</span>
                                        <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">This Month</span>
                                    </div>
                                    <div class="flex items-center text-green-600 dark:text-green-500">
                                        <x-heroicon-o-arrow-trending-up class="w-5 h-5 mr-1" />
                                        <span class="text-sm font-medium">+{{ rand(5, 25) }}%</span>
                                    </div>
                                </div>
                                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const ctx = document.getElementById('monthlyRevenueChart').getContext('2d');
            const chartData = {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Monthly Revenue',
                    data: [1632000, 800000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Example data
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            };

            new Chart(ctx, {
                type: 'bar',
                data: chartData,
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>

    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <canvas id="monthlyRevenueChart" width="400" height="200"></canvas>
    </div>


                            </div>
                        </div>
                    @break

                    @default

                @endswitch
            </div>
        @endforeach
    </div>

    @if (count($widgets) === 0)
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
            <div class="text-center py-6">
                <div class="text-primary-500 dark:text-blue-500 mb-4">
                    <x-heroicon-o-squares-2x2 class="w-12 h-12 mx-auto" />
                </div>
                <h2 class="text-xl font-bold mb-2 text-gray-900 dark:text-white">No widgets configured</h2>
                <p class="text-gray-600 dark:text-gray-400 mb-4">You haven't configured any dashboard widgets yet.</p>
                <a href="{{ route('filament.admin.pages.dashboard-settings') }}"
                    class="inline-flex items-center px-4 py-2 bg-primary-600 dark:bg-blue-600 rounded-lg text-white hover:bg-primary-700 dark:hover:bg-blue-700 transition">
                    <x-heroicon-o-cog-6-tooth class="w-5 h-5 mr-2" />
                    Configure Dashboard
                </a>
            </div>
        </div>
    @endif


</x-filament-panels::page>
