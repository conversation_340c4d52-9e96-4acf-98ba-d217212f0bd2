<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class FixPermissions extends Command
{
    protected $signature = 'permissions:fix {email?}';
    protected $description = 'Fix permissions for super_admin role and optionally assign to a user';

    public function handle()
    {
        $this->info('Fixing permissions...');
        
        // First, make sure we have all permissions generated
        $this->info('Generating shield permissions...');
        Artisan::call('shield:generate');
        $this->info(Artisan::output());
        
        // Get or create super_admin role
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin']);
        $this->info("Super admin role ID: {$superAdminRole->id}");
        
        // Get all permissions
        $permissions = Permission::all();
        $this->info("Total permissions: {$permissions->count()}");
        
        // Assign all permissions to super_admin role
        $superAdminRole->syncPermissions($permissions);
        $this->info('All permissions assigned to super_admin role');
        
        // If email is provided, assign super_admin role to the user
        if ($email = $this->argument('email')) {
            $user = User::where('email', $email)->first();
            
            if (!$user) {
                $this->error("User with email {$email} not found");
                return 1;
            }
            
            $user->assignRole($superAdminRole);
            $this->info("Super admin role assigned to user {$user->name}");
            
            // Also directly assign all permissions to the user
            $user->syncPermissions($permissions);
            $this->info("All permissions directly assigned to user {$user->name}");
        }
        
        // Fix any potential issues with the permission tables
        $this->fixPermissionTables();
        
        $this->info('Permissions fixed successfully!');
        return 0;
    }
    
    private function fixPermissionTables()
    {
        $this->info('Fixing permission tables...');
        
        // Make sure all users with super_admin role have all permissions
        $superAdminRole = Role::where('name', 'super_admin')->first();
        if ($superAdminRole) {
            $superAdminUsers = User::role('super_admin')->get();
            $permissions = Permission::all();
            
            foreach ($superAdminUsers as $user) {
                $this->info("Ensuring user {$user->name} has all permissions...");
                $user->syncPermissions($permissions);
            }
        }
        
        // Clear cache
        $this->info('Clearing permission cache...');
        Artisan::call('cache:clear');
        
        $this->info('Permission tables fixed');
    }
}
