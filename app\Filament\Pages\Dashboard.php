<?php

// This is a comment added as per user request

namespace App\Filament\Pages;

use App\Models\DashboardConfig;
use App\Models\DashboardWidget;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use <PERSON><PERSON>han<PERSON>alleh\FilamentShield\Traits\HasPageShield;

class Dashboard extends Page
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?int $navigationSort = -2;

    protected static string $view = 'filament.pages.dashboard';

    public static function canAccess(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // If user is super_admin, always grant access
        if (auth()->user()->hasRole('super_admin')) {
            return true;
        }

        // For other users, check for specific roles
        return auth()->user()->hasAnyRole(['admin', 'manager', 'marketing']);
    }

    public static function shouldRegisterNavigation(): bool
    {
        // Hide navigation for bde_team users
        return !(auth()->check() && auth()->user()->hasRole('bde_team'));
    }

    public array $widgets = [];

    // Make this the default page
    public static function getSlug(): string
    {
        return 'dashboard';
    }

    public function resetDashboard()
    {
        $userId = Filament::auth()->id();

        // Delete all dashboard configurations for the current user
        DashboardConfig::where('user_id', $userId)->delete();

        // Show a notification
        Notification::make()
            ->title('Dashboard reset successfully')
            ->success()
            ->send();

        // Reload the page
        $this->redirect(route('filament.admin.pages.dashboard'));
    }

    public function mount(): void
    {
        $userId = Filament::auth()->id();
        $allWidgets = DashboardWidget::where('is_available', true)->get();

        // Get user's widget configurations
        $userConfigs = DashboardConfig::where('user_id', $userId)
            ->orderBy('position')
            ->get()
            ->keyBy('widget_id');

        \Log::info('[DASHBOARD] User widget configs', [
            'user_id' => $userId,
            'userConfigs' => $userConfigs->toArray(),
        ]);

        // Prepare widgets data for the view
        $this->widgets = [];

        foreach ($allWidgets as $widget) {
            // Skip if user has explicitly disabled this widget
            if (isset($userConfigs[$widget->id]) && !$userConfigs[$widget->id]->is_enabled) {
                continue;
            }

            // Get user config or use defaults
            $config = $userConfigs[$widget->id] ?? null;

            $widgetArr = [
                'id' => $widget->id,
                'name' => $widget->name,
                'component' => $widget->component,
                'width' => $config ? $config->width : $widget->default_width,
                'height' => $config ? $config->height : $widget->default_height,
                'position' => $config ? $config->position : $widget->id,
                'settings' => $config ? $config->settings : $widget->default_settings,
            ];
            \Log::info('[DASHBOARD] Widget prepared', $widgetArr);
            $this->widgets[] = $widgetArr;
        }

        // Sort widgets by position
        usort($this->widgets, function ($a, $b) {
            return $a['position'] <=> $b['position'];
        });

        \Log::info('[DASHBOARD] Final widgets for view', $this->widgets);
    }
}
