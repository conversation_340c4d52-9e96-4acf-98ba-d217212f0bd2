<?php

namespace Database\Seeders;

use App\Models\NotificationEvent;
use Illuminate\Database\Seeder;

class OverdueMilestoneNotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        NotificationEvent::updateOrCreate(
            ['name' => 'milestone_overdue'],
            [
                'display_name' => 'Milestone Overdue',
                'module' => 'milestones',
                'description' => 'Triggered when a milestone due date has passed and the milestone is not completed',
                'is_active' => true,
                'is_hierarchical' => true,
                'data_schema' => [
                    'milestone_id' => 'integer',
                    'project_id' => 'integer',
                    'milestone_title' => 'string',
                    'project_title' => 'string',
                    'due_date' => 'date',
                    'days_overdue' => 'integer',
                    'bde_name' => 'string',
                ],
            ]
        );
    }
}
