{"__meta": {"id": "01JXB9B2D1XH9WQ6YJ0EWZM181", "datetime": "2025-06-09 21:25:30", "utime": **********.146466, "method": "GET", "uri": "/admin/dashboard", "ip": "12*******"}, "messages": {"count": 10, "messages": [{"message": "[21:25:26] LOG.info: [DASHBOARD] User widget configs {\n    \"user_id\": 1,\n    \"userConfigs\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.692145, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 1,\n    \"name\": \"Total Projects\",\n    \"component\": \"TotalProjectsWidget\",\n    \"width\": 3,\n    \"height\": 1,\n    \"position\": 1,\n    \"settings\": \"{\\\"icon\\\":\\\"heroicon-o-briefcase\\\"}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.693009, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 2,\n    \"name\": \"Total Clients\",\n    \"component\": \"TotalClientsWidget\",\n    \"width\": 3,\n    \"height\": 1,\n    \"position\": 2,\n    \"settings\": \"{\\\"icon\\\":\\\"heroicon-o-users\\\"}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.693261, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 3,\n    \"name\": \"Pending Payments\",\n    \"component\": \"PendingPaymentsWidget\",\n    \"width\": 3,\n    \"height\": 1,\n    \"position\": 3,\n    \"settings\": \"{\\\"icon\\\":\\\"heroicon-o-credit-card\\\"}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.69342, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 4,\n    \"name\": \"Approved Incentives\",\n    \"component\": \"ApprovedIncentivesWidget\",\n    \"width\": 3,\n    \"height\": 1,\n    \"position\": 4,\n    \"settings\": \"{\\\"icon\\\":\\\"heroicon-o-banknotes\\\"}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.693559, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 5,\n    \"name\": \"Recent Projects\",\n    \"component\": \"RecentProjectsWidget\",\n    \"width\": 12,\n    \"height\": 2,\n    \"position\": 5,\n    \"settings\": \"{\\\"limit\\\":5}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.693716, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 6,\n    \"name\": \"Upcoming Payments\",\n    \"component\": \"UpcomingPaymentsWidget\",\n    \"width\": 12,\n    \"height\": 2,\n    \"position\": 6,\n    \"settings\": \"{\\\"limit\\\":5}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.693888, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 7,\n    \"name\": \"Project Status\",\n    \"component\": \"ProjectStatusWidget\",\n    \"width\": 6,\n    \"height\": 2,\n    \"position\": 7,\n    \"settings\": \"{\\\"chart_type\\\":\\\"pie\\\"}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.694088, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 8,\n    \"name\": \"Monthly Revenue\",\n    \"component\": \"MonthlyRevenueWidget\",\n    \"width\": 6,\n    \"height\": 2,\n    \"position\": 8,\n    \"settings\": \"{\\\"chart_type\\\":\\\"bar\\\",\\\"months\\\":6}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.694282, "xdebug_link": null, "collector": "log"}, {"message": "[21:25:26] LOG.info: [DASHBOARD] Final widgets for view [\n    {\n        \"id\": 1,\n        \"name\": \"Total Projects\",\n        \"component\": \"TotalProjectsWidget\",\n        \"width\": 3,\n        \"height\": 1,\n        \"position\": 1,\n        \"settings\": \"{\\\"icon\\\":\\\"heroicon-o-briefcase\\\"}\"\n    },\n    {\n        \"id\": 2,\n        \"name\": \"Total Clients\",\n        \"component\": \"TotalClientsWidget\",\n        \"width\": 3,\n        \"height\": 1,\n        \"position\": 2,\n        \"settings\": \"{\\\"icon\\\":\\\"heroicon-o-users\\\"}\"\n    },\n    {\n        \"id\": 3,\n        \"name\": \"Pending Payments\",\n        \"component\": \"PendingPaymentsWidget\",\n        \"width\": 3,\n        \"height\": 1,\n        \"position\": 3,\n        \"settings\": \"{\\\"icon\\\":\\\"heroicon-o-credit-card\\\"}\"\n    },\n    {\n        \"id\": 4,\n        \"name\": \"Approved Incentives\",\n        \"component\": \"ApprovedIncentivesWidget\",\n        \"width\": 3,\n        \"height\": 1,\n        \"position\": 4,\n        \"settings\": \"{\\\"icon\\\":\\\"heroicon-o-banknotes\\\"}\"\n    },\n    {\n        \"id\": 5,\n        \"name\": \"Recent Projects\",\n        \"component\": \"RecentProjectsWidget\",\n        \"width\": 12,\n        \"height\": 2,\n        \"position\": 5,\n        \"settings\": \"{\\\"limit\\\":5}\"\n    },\n    {\n        \"id\": 6,\n        \"name\": \"Upcoming Payments\",\n        \"component\": \"UpcomingPaymentsWidget\",\n        \"width\": 12,\n        \"height\": 2,\n        \"position\": 6,\n        \"settings\": \"{\\\"limit\\\":5}\"\n    },\n    {\n        \"id\": 7,\n        \"name\": \"Project Status\",\n        \"component\": \"ProjectStatusWidget\",\n        \"width\": 6,\n        \"height\": 2,\n        \"position\": 7,\n        \"settings\": \"{\\\"chart_type\\\":\\\"pie\\\"}\"\n    },\n    {\n        \"id\": 8,\n        \"name\": \"Monthly Revenue\",\n        \"component\": \"MonthlyRevenueWidget\",\n        \"width\": 6,\n        \"height\": 2,\n        \"position\": 8,\n        \"settings\": \"{\\\"chart_type\\\":\\\"bar\\\",\\\"months\\\":6}\"\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.694816, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749504325.844027, "end": **********.146515, "duration": 4.302487850189209, "duration_str": "4.3s", "measures": [{"label": "Booting", "start": 1749504325.844027, "relative_start": 0, "end": **********.356249, "relative_end": **********.356249, "duration": 0.****************, "duration_str": "512ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.356264, "relative_start": 0.****************, "end": **********.146518, "relative_end": 3.0994415283203125e-06, "duration": 3.****************, "duration_str": "3.79s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.607032, "relative_start": 0.****************, "end": **********.610496, "relative_end": **********.610496, "duration": 0.0034639835357666016, "duration_str": "3.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.pages.dashboard", "start": **********.749817, "relative_start": 0.***************, "end": **********.749817, "relative_end": **********.749817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f2f44b54be1e6b3a1bd77567b74ada6e", "start": **********.17135, "relative_start": 3.****************, "end": **********.17135, "relative_end": **********.17135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::543962272ccef1b0087ff8de93e25a12", "start": **********.21811, "relative_start": 3.3740830421447754, "end": **********.21811, "relative_end": **********.21811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dbc0ef3ed3193e87849b3ea3fba36a2f", "start": **********.264294, "relative_start": 3.42026686668396, "end": **********.264294, "relative_end": **********.264294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b528d29c191f34648130e512c6c14c42", "start": **********.312351, "relative_start": 3.4683239459991455, "end": **********.312351, "relative_end": **********.312351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d691f9b9b8ea42c9565d0eae88a8a7a", "start": **********.363991, "relative_start": 3.5199639797210693, "end": **********.363991, "relative_end": **********.363991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::55804b29716228158ef2a01fb0fb6bf6", "start": **********.408654, "relative_start": 3.564626932144165, "end": **********.408654, "relative_end": **********.408654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.492936, "relative_start": 3.648908853530884, "end": **********.492936, "relative_end": **********.492936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.549502, "relative_start": 3.705474853515625, "end": **********.549502, "relative_end": **********.549502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.58634, "relative_start": 3.7423129081726074, "end": **********.58634, "relative_end": **********.58634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.587052, "relative_start": 3.743025064468384, "end": **********.587052, "relative_end": **********.587052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.587492, "relative_start": 3.743464946746826, "end": **********.587492, "relative_end": **********.587492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.587907, "relative_start": 3.743880033493042, "end": **********.587907, "relative_end": **********.587907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.588109, "relative_start": 3.744081974029541, "end": **********.588109, "relative_end": **********.588109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.588441, "relative_start": 3.7444138526916504, "end": **********.588441, "relative_end": **********.588441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.588644, "relative_start": 3.744616985321045, "end": **********.588644, "relative_end": **********.588644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.588936, "relative_start": 3.7449090480804443, "end": **********.588936, "relative_end": **********.588936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.600037, "relative_start": 3.756010055541992, "end": **********.600037, "relative_end": **********.600037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.601806, "relative_start": 3.7577788829803467, "end": **********.601806, "relative_end": **********.601806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.603246, "relative_start": 3.75921893119812, "end": **********.603246, "relative_end": **********.603246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.604078, "relative_start": 3.7600510120391846, "end": **********.604078, "relative_end": **********.604078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.604754, "relative_start": 3.7607269287109375, "end": **********.604754, "relative_end": **********.604754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6793bd8c17ae551c989e7d8702e297bd", "start": **********.606563, "relative_start": 3.76253604888916, "end": **********.606563, "relative_end": **********.606563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::669f45ea592e06d462edef0dc7991631", "start": **********.64482, "relative_start": 3.800792932510376, "end": **********.64482, "relative_end": **********.64482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::920886b33da20750c767a455729f43a3", "start": **********.702704, "relative_start": 3.8586769104003906, "end": **********.702704, "relative_end": **********.702704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::580d32f5fef9ea18b4f700add0d02b13", "start": **********.7433, "relative_start": 3.899272918701172, "end": **********.7433, "relative_end": **********.7433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::09b71e88617c7cb829e8c2982af46b24", "start": **********.808384, "relative_start": 3.9643568992614746, "end": **********.808384, "relative_end": **********.808384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.927711, "relative_start": 4.083683967590332, "end": **********.927711, "relative_end": **********.927711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.992143, "relative_start": 4.148115873336792, "end": **********.992143, "relative_end": **********.992143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.008841, "relative_start": 4.164813995361328, "end": **********.008841, "relative_end": **********.008841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.010935, "relative_start": 4.166908025741577, "end": **********.010935, "relative_end": **********.010935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.014425, "relative_start": 4.170397996902466, "end": **********.014425, "relative_end": **********.014425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.016644, "relative_start": 4.172616958618164, "end": **********.016644, "relative_end": **********.016644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.023748, "relative_start": 4.179720878601074, "end": **********.023748, "relative_end": **********.023748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.024149, "relative_start": 4.180121898651123, "end": **********.024149, "relative_end": **********.024149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.026526, "relative_start": 4.182498931884766, "end": **********.026526, "relative_end": **********.026526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.026894, "relative_start": 4.182867050170898, "end": **********.026894, "relative_end": **********.026894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.029563, "relative_start": 4.185535907745361, "end": **********.029563, "relative_end": **********.029563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.029939, "relative_start": 4.1859118938446045, "end": **********.029939, "relative_end": **********.029939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.032482, "relative_start": 4.188454866409302, "end": **********.032482, "relative_end": **********.032482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.032782, "relative_start": 4.188755035400391, "end": **********.032782, "relative_end": **********.032782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.107219, "relative_start": 4.2631919384002686, "end": **********.107219, "relative_end": **********.107219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.108786, "relative_start": 4.264759063720703, "end": **********.108786, "relative_end": **********.108786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.127448, "relative_start": 4.283421039581299, "end": **********.127448, "relative_end": **********.127448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.129484, "relative_start": 4.285456895828247, "end": **********.129484, "relative_end": **********.129484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.130766, "relative_start": 4.286738872528076, "end": **********.130766, "relative_end": **********.130766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.136247, "relative_start": 4.292219877243042, "end": **********.136471, "relative_end": **********.136471, "duration": 0.00022411346435546875, "duration_str": "224μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.142287, "relative_start": 4.298259973526001, "end": **********.142365, "relative_end": **********.142365, "duration": 7.796287536621094e-05, "duration_str": "78μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 48658168, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 46, "nb_templates": 46, "templates": [{"name": "filament.pages.dashboard", "param_count": null, "params": [], "start": **********.749791, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.phpfilament.pages.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "__components::f2f44b54be1e6b3a1bd77567b74ada6e", "param_count": null, "params": [], "start": **********.171164, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/f2f44b54be1e6b3a1bd77567b74ada6e.blade.php__components::f2f44b54be1e6b3a1bd77567b74ada6e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Ff2f44b54be1e6b3a1bd77567b74ada6e.blade.php&line=1", "ajax": false, "filename": "f2f44b54be1e6b3a1bd77567b74ada6e.blade.php", "line": "?"}}, {"name": "__components::543962272ccef1b0087ff8de93e25a12", "param_count": null, "params": [], "start": **********.218019, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/543962272ccef1b0087ff8de93e25a12.blade.php__components::543962272ccef1b0087ff8de93e25a12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F543962272ccef1b0087ff8de93e25a12.blade.php&line=1", "ajax": false, "filename": "543962272ccef1b0087ff8de93e25a12.blade.php", "line": "?"}}, {"name": "__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "param_count": null, "params": [], "start": **********.264257, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fdbc0ef3ed3193e87849b3ea3fba36a2f.blade.php&line=1", "ajax": false, "filename": "dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php", "line": "?"}}, {"name": "__components::b528d29c191f34648130e512c6c14c42", "param_count": null, "params": [], "start": **********.312331, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b528d29c191f34648130e512c6c14c42.blade.php__components::b528d29c191f34648130e512c6c14c42", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb528d29c191f34648130e512c6c14c42.blade.php&line=1", "ajax": false, "filename": "b528d29c191f34648130e512c6c14c42.blade.php", "line": "?"}}, {"name": "__components::1d691f9b9b8ea42c9565d0eae88a8a7a", "param_count": null, "params": [], "start": **********.363953, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php__components::1d691f9b9b8ea42c9565d0eae88a8a7a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php&line=1", "ajax": false, "filename": "1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php", "line": "?"}}, {"name": "__components::55804b29716228158ef2a01fb0fb6bf6", "param_count": null, "params": [], "start": **********.408632, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/55804b29716228158ef2a01fb0fb6bf6.blade.php__components::55804b29716228158ef2a01fb0fb6bf6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F55804b29716228158ef2a01fb0fb6bf6.blade.php&line=1", "ajax": false, "filename": "55804b29716228158ef2a01fb0fb6bf6.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.492895, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.549479, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.586293, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.587038, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.587478, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.587895, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.588098, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.588426, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.588633, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.588924, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.600017, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.60179, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.603229, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.604064, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.604743, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::6793bd8c17ae551c989e7d8702e297bd", "param_count": null, "params": [], "start": **********.606543, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/6793bd8c17ae551c989e7d8702e297bd.blade.php__components::6793bd8c17ae551c989e7d8702e297bd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F6793bd8c17ae551c989e7d8702e297bd.blade.php&line=1", "ajax": false, "filename": "6793bd8c17ae551c989e7d8702e297bd.blade.php", "line": "?"}}, {"name": "__components::669f45ea592e06d462edef0dc7991631", "param_count": null, "params": [], "start": **********.644793, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/669f45ea592e06d462edef0dc7991631.blade.php__components::669f45ea592e06d462edef0dc7991631", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F669f45ea592e06d462edef0dc7991631.blade.php&line=1", "ajax": false, "filename": "669f45ea592e06d462edef0dc7991631.blade.php", "line": "?"}}, {"name": "__components::920886b33da20750c767a455729f43a3", "param_count": null, "params": [], "start": **********.702651, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/920886b33da20750c767a455729f43a3.blade.php__components::920886b33da20750c767a455729f43a3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F920886b33da20750c767a455729f43a3.blade.php&line=1", "ajax": false, "filename": "920886b33da20750c767a455729f43a3.blade.php", "line": "?"}}, {"name": "__components::580d32f5fef9ea18b4f700add0d02b13", "param_count": null, "params": [], "start": **********.743265, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/580d32f5fef9ea18b4f700add0d02b13.blade.php__components::580d32f5fef9ea18b4f700add0d02b13", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F580d32f5fef9ea18b4f700add0d02b13.blade.php&line=1", "ajax": false, "filename": "580d32f5fef9ea18b4f700add0d02b13.blade.php", "line": "?"}}, {"name": "__components::09b71e88617c7cb829e8c2982af46b24", "param_count": null, "params": [], "start": **********.808343, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/09b71e88617c7cb829e8c2982af46b24.blade.php__components::09b71e88617c7cb829e8c2982af46b24", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F09b71e88617c7cb829e8c2982af46b24.blade.php&line=1", "ajax": false, "filename": "09b71e88617c7cb829e8c2982af46b24.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.927688, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.992124, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}}, {"name": "filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.008814, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}}, {"name": "__components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.010912, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.014405, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.016621, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.02373, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.024134, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.026509, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.02688, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.029546, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.029925, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.032468, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.032769, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.107198, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}}, {"name": "filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.108766, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.127429, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}, {"name": "__components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.129465, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}}, {"name": "components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.130747, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}}]}, "queries": {"count": 33, "nb_statements": 33, "nb_visible_statements": 33, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07477999999999997, "accumulated_duration_str": "74.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'jxkhZdGMROOSU4U80hX5Jk115sFIUKaX39FwYZ6k' limit 1", "type": "query", "params": [], "bindings": ["jxkhZdGMROOSU4U80hX5Jk115sFIUKaX39FwYZ6k"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.621062, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 4.761}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.6279411, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.761, "width_percent": 3.891}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.634517, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.652, "width_percent": 2.22}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.643062, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.872, "width_percent": 1.605}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.645588, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.477, "width_percent": 1.752}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.656799, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.228, "width_percent": 0.695}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.6590738, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.924, "width_percent": 0.562}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.66032, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.485, "width_percent": 0.428}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.6614, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.913, "width_percent": 0.401}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.662445, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.315, "width_percent": 0.481}, {"sql": "select * from `dashboard_widgets` where `is_available` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 75}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.675279, "duration": 0.0047599999999999995, "duration_str": "4.76ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:75", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=75", "ajax": false, "filename": "Dashboard.php", "line": "75"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.796, "width_percent": 6.365}, {"sql": "select * from `dashboard_configs` where `user_id` = 1 order by `position` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.683387, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:80", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=80", "ajax": false, "filename": "Dashboard.php", "line": "80"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.161, "width_percent": 3.009}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanAuthorizeAccess.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanAuthorizeAccess.php", "line": 9}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.701207, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.17, "width_percent": 3.062}, {"sql": "select count(*) as aggregate from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 134}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.305593, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:134", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=134", "ajax": false, "filename": "dashboard.blade.php", "line": "134"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.232, "width_percent": 4.908}, {"sql": "select count(*) as aggregate from `clients`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 172}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.3571012, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:172", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=172", "ajax": false, "filename": "dashboard.blade.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.14, "width_percent": 5.296}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 210}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.398465, "duration": 0.0076, "duration_str": "7.6ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:210", "source": {"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=210", "ajax": false, "filename": "dashboard.blade.php", "line": "210"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.436, "width_percent": 10.163}, {"sql": "select sum(`amount`) as aggregate from `incentives` where `status` = 'approved'", "type": "query", "params": [], "bindings": ["approved"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 248}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.445975, "duration": 0.004900000000000001, "duration_str": "4.9ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:248", "source": {"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 248}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=248", "ajax": false, "filename": "dashboard.blade.php", "line": "248"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.599, "width_percent": 6.553}, {"sql": "select * from `projects` order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 284}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.451957, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:284", "source": {"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 284}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=284", "ajax": false, "filename": "dashboard.blade.php", "line": "284"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.151, "width_percent": 4.079}, {"sql": "select * from `clients` where `clients`.`id` in (1, 6, 7, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 284}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.4653351, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:284", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 284}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=284", "ajax": false, "filename": "dashboard.blade.php", "line": "284"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.23, "width_percent": 4.854}, {"sql": "select * from `users` where `users`.`id` in (1, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 284}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.47012, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:284", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 284}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=284", "ajax": false, "filename": "dashboard.blade.php", "line": "284"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.084, "width_percent": 1.591}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1, 4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 284}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.472316, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:284", "source": {"index": 25, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 284}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=284", "ajax": false, "filename": "dashboard.blade.php", "line": "284"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.676, "width_percent": 0.629}, {"sql": "select count(*) as aggregate from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.589205, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:359", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=359", "ajax": false, "filename": "dashboard.blade.php", "line": "359"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.304, "width_percent": 0.602}, {"sql": "select * from `payments` where `status` = 'pending' order by `due_date` asc limit 5", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 403}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.5905828, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:403", "source": {"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=403", "ajax": false, "filename": "dashboard.blade.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.906, "width_percent": 1.752}, {"sql": "select * from `projects` where `projects`.`id` in (1, 3, 5, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 403}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.593044, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:403", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=403", "ajax": false, "filename": "dashboard.blade.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.658, "width_percent": 0.776}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.957458, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.433, "width_percent": 0.642}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.9945798, "duration": 0.01219, "duration_str": "12.19ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.075, "width_percent": 16.301}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.0120609, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.376, "width_percent": 1.257}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0195231, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.633, "width_percent": 4.132}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.024843, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.765, "width_percent": 0.883}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.02761, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.648, "width_percent": 1.163}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.030613, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.811, "width_percent": 1.056}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.03338, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.868, "width_percent": 0.709}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoicjlEZnM5VGN1cVJFMXk2TVpjd1U4NXc5bVJ1MjZWWllnbzN0YnpQbCI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czozNzoiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL2Rhc2hib2FyZCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6ODoiZmlsYW1lbnQiO2E6MDp7fXM6NjoidGFibGVzIjthOjE6e3M6NDE6IjgyNTJjZmE1NjA4MzhlZmMwMDM5NjI4MzQxZjNhNDZmX3Blcl9wYWdlIjtzOjM6ImFsbCI7fX0=', `last_activity` = **********, `user_id` = 1, `ip_address` = '12*******', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'jxkhZdGMROOSU4U80hX5Jk115sFIUKaX39FwYZ6k'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoicjlEZnM5VGN1cVJFMXk2TVpjd1U4NXc5bVJ1MjZWWllnbzN0YnpQbCI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czozNzoiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL2Rhc2hib2FyZCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6ODoiZmlsYW1lbnQiO2E6MDp7fXM6NjoidGFibGVzIjthOjE6e3M6NDE6IjgyNTJjZmE1NjA4MzhlZmMwMDM5NjI4MzQxZjNhNDZmX3Blcl9wYWdlIjtzOjM6ImFsbCI7fX0=", **********, 1, "12*******", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "jxkhZdGMROOSU4U80hX5Jk115sFIUKaX39FwYZ6k"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.137931, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.577, "width_percent": 3.423}]}, "models": {"data": {"App\\Models\\Project": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\DashboardWidget": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FDashboardWidget.php&line=1", "ajax": false, "filename": "DashboardWidget.php", "line": "?"}}, "App\\Models\\Payment": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\Client": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 40, "is_counter": true}, "livewire": {"data": {"app.filament.pages.dashboard #nWvz3kJw3C5Ez44Wye6T": "array:4 [\n  \"data\" => array:15 [\n    \"widgets\" => array:8 [\n      0 => array:7 [\n        \"id\" => 1\n        \"name\" => \"Total Projects\"\n        \"component\" => \"TotalProjectsWidget\"\n        \"width\" => 3\n        \"height\" => 1\n        \"position\" => 1\n        \"settings\" => \"{\"icon\":\"heroicon-o-briefcase\"}\"\n      ]\n      1 => array:7 [\n        \"id\" => 2\n        \"name\" => \"Total Clients\"\n        \"component\" => \"TotalClientsWidget\"\n        \"width\" => 3\n        \"height\" => 1\n        \"position\" => 2\n        \"settings\" => \"{\"icon\":\"heroicon-o-users\"}\"\n      ]\n      2 => array:7 [\n        \"id\" => 3\n        \"name\" => \"Pending Payments\"\n        \"component\" => \"PendingPaymentsWidget\"\n        \"width\" => 3\n        \"height\" => 1\n        \"position\" => 3\n        \"settings\" => \"{\"icon\":\"heroicon-o-credit-card\"}\"\n      ]\n      3 => array:7 [\n        \"id\" => 4\n        \"name\" => \"Approved Incentives\"\n        \"component\" => \"ApprovedIncentivesWidget\"\n        \"width\" => 3\n        \"height\" => 1\n        \"position\" => 4\n        \"settings\" => \"{\"icon\":\"heroicon-o-banknotes\"}\"\n      ]\n      4 => array:7 [\n        \"id\" => 5\n        \"name\" => \"Recent Projects\"\n        \"component\" => \"RecentProjectsWidget\"\n        \"width\" => 12\n        \"height\" => 2\n        \"position\" => 5\n        \"settings\" => \"{\"limit\":5}\"\n      ]\n      5 => array:7 [\n        \"id\" => 6\n        \"name\" => \"Upcoming Payments\"\n        \"component\" => \"UpcomingPaymentsWidget\"\n        \"width\" => 12\n        \"height\" => 2\n        \"position\" => 6\n        \"settings\" => \"{\"limit\":5}\"\n      ]\n      6 => array:7 [\n        \"id\" => 7\n        \"name\" => \"Project Status\"\n        \"component\" => \"ProjectStatusWidget\"\n        \"width\" => 6\n        \"height\" => 2\n        \"position\" => 7\n        \"settings\" => \"{\"chart_type\":\"pie\"}\"\n      ]\n      7 => array:7 [\n        \"id\" => 8\n        \"name\" => \"Monthly Revenue\"\n        \"component\" => \"MonthlyRevenueWidget\"\n        \"width\" => 6\n        \"height\" => 2\n        \"position\" => 8\n        \"settings\" => \"{\"chart_type\":\"bar\",\"months\":6}\"\n      ]\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.dashboard\"\n  \"component\" => \"App\\Filament\\Pages\\Dashboard\"\n  \"id\" => \"nWvz3kJw3C5Ez44Wye6T\"\n]", "filament.livewire.global-search #QUh0e4oif7zhTXJ5J3MQ": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"QUh0e4oif7zhTXJ5J3MQ\"\n]", "app.filament.widgets.notification-components.notification-bell #qwPIPugziJb0iwVdLFHZ": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 10\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"qwPIPugziJb0iwVdLFHZ\"\n]", "filament.livewire.notifications #yTAUlBspcnHMOcODWzIO": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2523\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"yTAUlBspcnHMOcODWzIO\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 17, "messages": [{"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-665121609 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665121609\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.934649, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-125593023 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125593023\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.936186, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1398618791 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398618791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.942465, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-639135917 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639135917\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.943601, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1545619316 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545619316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.945552, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1280361742 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280361742\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.946657, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-877049978 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877049978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.947683, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1476746956 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476746956\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948907, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1873054582 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873054582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.949931, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1179629887 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179629887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.95086, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1882818470 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882818470\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.951802, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1974392087 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974392087\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.953412, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1744471065 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744471065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.954548, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1005680286 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005680286\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.955401, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-6064543 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6064543\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.960776, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-528069583 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528069583\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.962255, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-552576267 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-552576267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.977278, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard", "uri": "GET admin/dashboard", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "4.31s", "peak_memory": "54MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ik0rNHk4U1d2V2J0aGFhRzhwWHNaV3c9PSIsInZhbHVlIjoiMUNUbkFkWDNWbVVWdmhjbDhpWVdBblAvd3RpbWcxWXZiaFg1SS9FK2FXUklSZFdUUktiZFFadGh1U2UwT21pTDBEbFVEK0hCRWRneTl6bkEwenB4SThpVElhL1hrVzZoRGZCS3lhMUxkZTM1by80WTdsVmRuWEEvU3VzTExMZHBRTkRmSXZEdmZiY01nM21sbHBVcU4wcWhKMGkyK2MwbGhBTmdsUlRuaVUxd1FtL3dCUFBUYUY3bklRTE1kdUUyR2E4d0tuVzE4NEVwRE5oVEJSMnFLa2h2SE5RZFVsK2FHdkdWZC9uT0tqbz0iLCJtYWMiOiI3ZmE1NGZkZTQ4MDUyMzFmYTNiMjBlZDdmYjM1MDQwMzBhNmM0YTA5NjI0YWNmYjY0YjFhMjczMzhhMTg5YjZkIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJ3WjJYWjF1eFNBY2RrZ1JuQW1xakE9PSIsInZhbHVlIjoiR1c5QWRRaVNNVEhGVkxXcnl4U1FuMHFFODRwRldDWGZwRHdZOVZlUjVMNXhhZ2V5M2hNNUwwZFB5WlBnTXRMNmgzY29WanRtbHJRTGkrcUtuSzI0S0FVeTZUZEh0RWYwVFBhc1J2US91Tnh0RmI0WDhXVEp0ZElJVXJoay95SGUiLCJtYWMiOiJmMWJjYjNkNjMxZjBiZDlkYTJkMTg5MjQzOGQyYjI2NmZhNzk0NjBhODEzOGVmNTdhOTFiNjI2MjUzMDZkZDcyIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6ImdRN3c4cVBNSDdrb3lBbzhxNHo1RWc9PSIsInZhbHVlIjoiZXFQWlo0VkZsRGNaSDEvaERrYThvcVhXdzJTUWdmdUlSU3A5M1E0V2kvZWx1V0lnSFpxZlNvTE5KYWZtNC8xZXp6Nm9LQXlvT2RpWjJsbzVLdWRoT3p3cEFIbm5vNWlnSE1aNmdnOWNYak9kQkQyZGhFKzFyZEZuTjVLN0pMZTciLCJtYWMiOiJlYWViOTM3M2VmYTNiZmRhMzRlYWE1MzJlOGVjMWRmMzk0NTgwMTg0OGM3MGZiMmQwYmFhOGU5ZmVkYjJkNzkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1697359135 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IaoVXF77cb5deCAtwKpLOWhQkSz6v8KjZyt6VaEz3KMxX2I2xibuZaQaf80m|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r9Dfs9TcuqRE1y6MZcwU85w9mRu26VZYgo3tbzPl</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jxkhZdGMROOSU4U80hX5Jk115sFIUKaX39FwYZ6k</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697359135\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-799114580 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 09 Jun 2025 21:25:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InhmcU1PMXpuVWdVZzFoQm84TkFQanc9PSIsInZhbHVlIjoiQ25yaGtuVjhmZ1kveVNtSjFoUUU1d2hyTk9rY202UVpnSHJpUkM2cnprazFTaFE2YWswaURzc2RnSWg2N094dlBpVnA1d0p0R0Y2TnFjNkpWMXJMUHovT01IT3FtY0FkVmc5akt1VTJlWXNKajBaakFwMzdzcEpTTmJ1Z0c5Q1kiLCJtYWMiOiJmZmJmZDBkZWFkNjk2OWY2MWZjZDcyYzMwMzEyY2MxM2NmODM5NjFmYzFhM2I0YzI5MGFjOWNlNjlmNDUyMTczIiwidGFnIjoiIn0%3D; expires=Mon, 09 Jun 2025 23:25:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6ImNIdUJjVHVFSytjZTdSWjE3T3lseWc9PSIsInZhbHVlIjoiUWJuTDJTbElWMDV4aW1FMExHUDF2WWRCbVZhWFJHNS9ScjIycjVONWpSL0hYQU9qWWpFL2d3MmwrK3V1S3dEY3U1ZlJmdFNhb0JZM0Y1VWh2SVNzZ0xRblhzNFpxN29HMnpJQWZrNzhqVi8zUU1iM0VCeWZHZytLVHVldWhUbHoiLCJtYWMiOiIzZThhZWJiZjk5YTA2Y2FmNzUzNjJlNjRhNDM3ZDM2ODRmODJhNzMzZDE2YzE0MDA4MmRjYTgwMTIwZGQ4ZjM1IiwidGFnIjoiIn0%3D; expires=Mon, 09 Jun 2025 23:25:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InhmcU1PMXpuVWdVZzFoQm84TkFQanc9PSIsInZhbHVlIjoiQ25yaGtuVjhmZ1kveVNtSjFoUUU1d2hyTk9rY202UVpnSHJpUkM2cnprazFTaFE2YWswaURzc2RnSWg2N094dlBpVnA1d0p0R0Y2TnFjNkpWMXJMUHovT01IT3FtY0FkVmc5akt1VTJlWXNKajBaakFwMzdzcEpTTmJ1Z0c5Q1kiLCJtYWMiOiJmZmJmZDBkZWFkNjk2OWY2MWZjZDcyYzMwMzEyY2MxM2NmODM5NjFmYzFhM2I0YzI5MGFjOWNlNjlmNDUyMTczIiwidGFnIjoiIn0%3D; expires=Mon, 09-Jun-2025 23:25:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6ImNIdUJjVHVFSytjZTdSWjE3T3lseWc9PSIsInZhbHVlIjoiUWJuTDJTbElWMDV4aW1FMExHUDF2WWRCbVZhWFJHNS9ScjIycjVONWpSL0hYQU9qWWpFL2d3MmwrK3V1S3dEY3U1ZlJmdFNhb0JZM0Y1VWh2SVNzZ0xRblhzNFpxN29HMnpJQWZrNzhqVi8zUU1iM0VCeWZHZytLVHVldWhUbHoiLCJtYWMiOiIzZThhZWJiZjk5YTA2Y2FmNzUzNjJlNjRhNDM3ZDM2ODRmODJhNzMzZDE2YzE0MDA4MmRjYTgwMTIwZGQ4ZjM1IiwidGFnIjoiIn0%3D; expires=Mon, 09-Jun-2025 23:25:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799114580\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2103408194 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r9Dfs9TcuqRE1y6MZcwU85w9mRu26VZYgo3tbzPl</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>8252cfa560838efc0039628341f3a46f_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103408194\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard"}, "badge": null}}