<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\IncentiveRule;
use App\Models\ProjectType;
use App\Models\Role;

class IncentiveRuleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = IncentiveRule::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'project_type_id' => ProjectType::factory(),
            'role_id' => Role::factory(),
            'percentage' => fake()->randomFloat(2, 0, 999.99),
            'duration_percentage' => fake()->randomFloat(2, 0, 999.99),
            'fixed_amount' => fake()->randomFloat(2, 0, 99999999.99),
            'currency' => fake()->word(),
            'calculation_type' => fake()->word(),
        ];
    }
}
