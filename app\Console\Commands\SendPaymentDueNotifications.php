<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Payment;
use App\Services\NotificationService;
use Carbon\Carbon;

class SendPaymentDueNotifications extends Command
{
    protected $signature = 'payments:send-due-notifications';
    protected $description = 'Send notifications for payments that are overdue';

    public function handle(NotificationService $notificationService)
    {
        $now = Carbon::now();
        $payments = Payment::where('status', 'pending')
            ->whereDate('due_date', '<', $now->toDateString())
            ->get();

        foreach ($payments as $payment) {
            $project = $payment->project;
            if (!$project || !$project->user) {
                continue;
            }
            $data = [
                'payment_id' => $payment->id,
                'payment_amount' => $payment->amount,
                'project_title' => $project->title,
                'due_date' => $payment->due_date,
            ];
            $notificationService->send('payment_due', $data, $project->user);
        }
        $this->info('Payment due notifications sent.');
    }
}
