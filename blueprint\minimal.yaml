models:
  User:
    name: string
    email: string unique
    password: string
    last_login_at: nullable timestamp
    remember_token: nullable string
    relationships:
      belongsTo: Role
      hasMany: Project, Notification, NotificationPreference, Incentive

  Role:
    name: string
    description: nullable string
    relationships:
      hasMany: User
      belongsToMany: Permission

  Permission:
    name: string
    description: nullable string
    relationships:
      belongsToMany: Role

  Client:
    name: string
    email: string
    phone: nullable string
    address: nullable text
    contact_person: nullable string
    status: string default:active
    relationships:
      hasMany: Project

  Project:
    client_id: id foreign
    user_id: id foreign:users
    project_type_id: id foreign
    title: string
    description: nullable text
    won_date: date
    start_date: date
    end_date: nullable date
    total_payment: decimal:10,2
    duration: integer
    duration_unit: string
    payment_cycle: string
    status: string default:active
    relationships:
      belongsTo: Client, User, ProjectType
      hasMany: Milestone, Payment, Incentive

  ProjectType:
    name: string
    description: nullable text
    relationships:
      hasMany: Project, IncentiveRule

  Milestone:
    project_id: id foreign
    title: string
    description: nullable text
    due_date: date
    percentage: decimal:5,2
    amount: decimal:10,2
    status: string default:pending
    relationships:
      belongsTo: Project

  Payment:
    project_id: id foreign
    milestone_id: nullable id foreign
    amount: decimal:10,2
    due_date: date
    paid_date: nullable date
    status: string default:pending
    payment_method: nullable string
    transaction_id: nullable string
    notes: nullable text
    relationships:
      belongsTo: Project, Milestone

  Incentive:
    user_id: id foreign
    project_id: id foreign
    amount: decimal:10,2
    calculation_date: date
    payment_date: nullable date
    status: string default:pending
    description: nullable text
    approved_by: nullable id foreign:users
    relationships:
      belongsTo: User, Project

  IncentiveRule:
    project_type_id: id foreign
    role_id: id foreign
    percentage: decimal:5,2
    duration_percentage: decimal:5,2
    fixed_amount: nullable decimal:10,2
    currency: string default:INR
    calculation_type: string
    relationships:
      belongsTo: ProjectType, Role

  Notification:
    user_id: id foreign
    title: string
    message: text
    type: string
    read_at: nullable timestamp
    data: nullable json
    relationships:
      belongsTo: User

  NotificationPreference:
    user_id: id foreign
    notification_type: string
    email: boolean default:true
    in_app: boolean default:true
    relationships:
      belongsTo: User

  CurrencyRate:
    from_currency: string
    to_currency: string
    rate: decimal:10,6
    last_updated_at: timestamp

  Report:
    user_id: id foreign
    title: string
    type: string
    parameters: json
    created_at: timestamp
    file_path: nullable string
    relationships:
      belongsTo: User

controllers:
  Dashboard:
    resource: index

  ProjectController:
    resource: web

  ClientController:
    resource: web

  MilestoneController:
    resource: web

  PaymentController:
    resource: web

  IncentiveController:
    resource: web

  ReportController:
    resource: web

  NotificationController:
    resource: web

  UserController:
    resource: web
