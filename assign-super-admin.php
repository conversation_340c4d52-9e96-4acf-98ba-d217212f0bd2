<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Get the super_admin role
$role = \Spatie\Permission\Models\Role::findByName('super_admin', 'web');

// Get the admin user
$user = \App\Models\User::where('email', '<EMAIL>')->first();

// Assign the role to the user
$user->assignRole($role);

// Give all permissions to the super_admin role
$permissions = \Spatie\Permission\Models\Permission::all();
$role->syncPermissions($permissions);

echo "Super admin role assigned to {$user->name} with all permissions.\n";
