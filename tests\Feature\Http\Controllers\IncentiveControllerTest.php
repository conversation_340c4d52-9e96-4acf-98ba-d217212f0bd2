<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Incentive;
use App\Models\Project;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Carbon;
use JMac\Testing\Traits\AdditionalAssertions;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * @see \App\Http\Controllers\IncentiveController
 */
final class IncentiveControllerTest extends TestCase
{
    use AdditionalAssertions, RefreshDatabase, WithFaker;

    #[Test]
    public function index_displays_view(): void
    {
        $incentives = Incentive::factory()->count(3)->create();

        $response = $this->get(route('incentives.index'));

        $response->assertOk();
        $response->assertViewIs('incentive.index');
        $response->assertViewHas('incentives');
    }


    #[Test]
    public function create_displays_view(): void
    {
        $response = $this->get(route('incentives.create'));

        $response->assertOk();
        $response->assertViewIs('incentive.create');
    }


    #[Test]
    public function store_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\IncentiveController::class,
            'store',
            \App\Http\Requests\IncentiveControllerStoreRequest::class
        );
    }

    #[Test]
    public function store_saves_and_redirects(): void
    {
        $user = User::factory()->create();
        $project = Project::factory()->create();
        $amount = fake()->randomFloat(/** decimal_attributes **/);
        $calculation_date = Carbon::parse(fake()->date());
        $status = fake()->word();

        $response = $this->post(route('incentives.store'), [
            'user_id' => $user->id,
            'project_id' => $project->id,
            'amount' => $amount,
            'calculation_date' => $calculation_date->toDateString(),
            'status' => $status,
        ]);

        $incentives = Incentive::query()
            ->where('user_id', $user->id)
            ->where('project_id', $project->id)
            ->where('amount', $amount)
            ->where('calculation_date', $calculation_date)
            ->where('status', $status)
            ->get();
        $this->assertCount(1, $incentives);
        $incentive = $incentives->first();

        $response->assertRedirect(route('incentives.index'));
        $response->assertSessionHas('incentive.id', $incentive->id);
    }


    #[Test]
    public function show_displays_view(): void
    {
        $incentive = Incentive::factory()->create();

        $response = $this->get(route('incentives.show', $incentive));

        $response->assertOk();
        $response->assertViewIs('incentive.show');
        $response->assertViewHas('incentive');
    }


    #[Test]
    public function edit_displays_view(): void
    {
        $incentive = Incentive::factory()->create();

        $response = $this->get(route('incentives.edit', $incentive));

        $response->assertOk();
        $response->assertViewIs('incentive.edit');
        $response->assertViewHas('incentive');
    }


    #[Test]
    public function update_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\IncentiveController::class,
            'update',
            \App\Http\Requests\IncentiveControllerUpdateRequest::class
        );
    }

    #[Test]
    public function update_redirects(): void
    {
        $incentive = Incentive::factory()->create();
        $user = User::factory()->create();
        $project = Project::factory()->create();
        $amount = fake()->randomFloat(/** decimal_attributes **/);
        $calculation_date = Carbon::parse(fake()->date());
        $status = fake()->word();

        $response = $this->put(route('incentives.update', $incentive), [
            'user_id' => $user->id,
            'project_id' => $project->id,
            'amount' => $amount,
            'calculation_date' => $calculation_date->toDateString(),
            'status' => $status,
        ]);

        $incentive->refresh();

        $response->assertRedirect(route('incentives.index'));
        $response->assertSessionHas('incentive.id', $incentive->id);

        $this->assertEquals($user->id, $incentive->user_id);
        $this->assertEquals($project->id, $incentive->project_id);
        $this->assertEquals($amount, $incentive->amount);
        $this->assertEquals($calculation_date, $incentive->calculation_date);
        $this->assertEquals($status, $incentive->status);
    }


    #[Test]
    public function destroy_deletes_and_redirects(): void
    {
        $incentive = Incentive::factory()->create();

        $response = $this->delete(route('incentives.destroy', $incentive));

        $response->assertRedirect(route('incentives.index'));

        $this->assertModelMissing($incentive);
    }
}
