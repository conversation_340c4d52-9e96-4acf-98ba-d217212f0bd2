<?php

namespace App\Filament\Widgets;

use App\Models\AppNotification;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class NotificationsWidget extends Widget
{
    protected static string $view = 'filament.widgets.notifications-widget';
    
    protected int|string|array $columnSpan = 'full';
    
    protected static ?int $sort = -1;
    
    public function getNotifications()
    {
        return AppNotification::where('user_id', Auth::id())
            ->whereNull('read_at')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
    }
    
    public function markAsRead($notificationId)
    {
        $notification = AppNotification::find($notificationId);
        
        if ($notification && $notification->user_id === Auth::id()) {
            $notification->markAsRead();
        }
        
        $this->dispatch('notification-read');
    }
    
    public function markAllAsRead()
    {
        AppNotification::where('user_id', Auth::id())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
            
        $this->dispatch('all-notifications-read');
    }
    
    public function getUnreadCount()
    {
        return AppNotification::where('user_id', Auth::id())
            ->whereNull('read_at')
            ->count();
    }
}
