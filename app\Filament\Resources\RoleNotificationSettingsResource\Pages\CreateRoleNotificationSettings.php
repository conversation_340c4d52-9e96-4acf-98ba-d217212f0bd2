<?php

namespace App\Filament\Resources\RoleNotificationSettingsResource\Pages;

use App\Filament\Resources\RoleNotificationSettingsResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateRoleNotificationSettings extends CreateRecord
{
    protected static string $resource = RoleNotificationSettingsResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
