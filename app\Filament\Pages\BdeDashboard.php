<?php

namespace App\Filament\Pages;

use App\Models\Project;
use App\Models\Client;
use App\Models\Incentive;
use App\Models\Milestone;
use Filament\Pages\Page;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class BdeDashboard extends Page
{
    use HasPageShield;
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $navigationLabel = 'Home';

    protected static ?int $navigationSort = -2;

    protected static string $view = 'filament.pages.bde-dashboard';

    public static function canAccess(): bool
    {
        // Only allow BDE users to access this dashboard
        return auth()->check() && auth()->user()->hasRole('bde_team');
    }

    // Make this the default page for BDE users
    public static function getSlug(): string
    {
        return 'home';
    }

    public function getProjectsCount()
    {
        return Project::where('user_id', auth()->id())->count();
    }

    public function getClientsCount()
    {
        return Client::whereHas('projects', function ($query) {
            $query->where('user_id', auth()->id());
        })->count();
    }

    public function getIncentivesCount()
    {
        return Incentive::where('user_id', auth()->id())->count();
    }

    public function getIncentivesAmount()
    {
        return Incentive::where('user_id', auth()->id())->sum('amount');
    }

    public function getRecentProjects()
    {
        return Project::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
    }

    public function getUpcomingMilestones()
    {
        return Milestone::whereHas('project', function ($query) {
            $query->where('user_id', auth()->id());
        })
        ->where('status', '!=', 'completed')
        ->orderBy('due_date')
        ->limit(5)
        ->get();
    }
}
