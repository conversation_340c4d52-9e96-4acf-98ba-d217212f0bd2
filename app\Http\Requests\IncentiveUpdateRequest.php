<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class IncentiveUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => ['required', 'integer', 'exists:users,id'],
            'project_id' => ['required', 'integer', 'exists:projects,id'],
            'amount' => ['required', 'numeric', 'between:-99999999.99,99999999.99'],
            'calculation_date' => ['required', 'date'],
            'payment_date' => ['nullable', 'date'],
            'status' => ['required', 'string'],
            'description' => ['nullable', 'string'],
            'approved_by' => ['nullable'],
        ];
    }
}
