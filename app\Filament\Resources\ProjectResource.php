<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProjectResource\Pages;
use App\Filament\Resources\ProjectResource\RelationManagers;
use App\Models\Project;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProjectResource extends Resource
{
    protected static ?string $model = Project::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';

    protected static ?string $recordTitleAttribute = 'title';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // If user has BDE role, only show their projects
        if (auth()->check() && auth()->user()->hasRole('bde')) {
            $query->where('user_id', auth()->id());
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Project Details')
                    ->schema([
                        Forms\Components\Select::make('client_id')
                            ->relationship('client', 'company_name', function (Builder $query) {
                                // If user has BDE role, only show their clients
                                if (auth()->check() && auth()->user()->hasRole('bde')) {
                                    $query->whereHas('projects', function (Builder $query) {
                                        $query->where('user_id', auth()->id());
                                    });
                                }
                                return $query;
                            })
                            ->searchable()
                            ->preload()
                            ->getOptionLabelFromRecordUsing(fn ($record) => $record?->company_name ?? '-')
                            ->createOptionForm([
                                Forms\Components\TextInput::make('company_name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('company_email')
                                    ->email()
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('phone')
                                    ->tel()
                                    ->maxLength(20),
                            ])
                            ->required(),
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->default(fn() => auth()->id())
                            ->disabled(fn() => auth()->user()->hasRole('bde'))
                            ->required()
                            ->searchable()
                            ->preload(),
                        Forms\Components\Select::make('project_type_id')
                            ->relationship('projectType', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->columnSpanFull(),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\DatePicker::make('won_date')
                                    ->required(),
                                Forms\Components\DatePicker::make('start_date')
                                    ->required()
                                    ->label('Start Date'),
                                Forms\Components\DatePicker::make('end_date')
                                    ->label('End Date'),
                            ]),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('duration')
                                    ->required()
                                    ->numeric()
                                    ->minValue(1),
                                Forms\Components\Select::make('duration_unit')
                                    ->options([
                                        'days' => 'Days',
                                        'weeks' => 'Weeks',
                                        'months' => 'Months',
                                        'years' => 'Years',
                                    ])
                                    ->required(),
                                Forms\Components\Select::make('payment_cycle')
                                    ->options([
                                        'weekly' => 'Weekly',
                                        'biweekly' => 'Bi-weekly',
                                        'monthly' => 'Monthly',
                                        'quarterly' => 'Quarterly',
                                        'milestone' => 'Milestone-based',
                                        'upfront' => 'Upfront',
                                    ])
                                    ->required(),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('total_payment')
                                    ->required()
                                    ->numeric(),
                                Forms\Components\Select::make('status')
                                    ->options([
                                        'active' => 'Active',
                                        'completed' => 'Completed',
                                        'on_hold' => 'On Hold',
                                        'cancelled' => 'Cancelled',
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('currency')
                                    ->options([
                                        'INR' => 'Indian Rupee (₹)',
                                        'USD' => 'US Dollar ($)',
                                        'EUR' => 'Euro (€)',
                                        'GBP' => 'British Pound (£)',
                                    ])
                                    ->default('INR')
                                    ->required()
                                    ->label('Currency'),
                            ]),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('client.company_name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('BDE')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('projectType.name')
                    ->label('Type')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_payment')
                    ->formatStateUsing(function ($state, $record) {
                        $currencySymbol = match ($record->currency) {
                            'INR' => '₹',
                            'USD' => '$',
                            'EUR' => '€',
                            'GBP' => '£',
                            default => '',
                        };
                        return $currencySymbol . ' ' . number_format($state, 2);
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'on_hold' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\MilestonesRelationManager::class,
            RelationManagers\PaymentsRelationManager::class,
            RelationManagers\IncentivesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProjects::route('/'),
            'create' => Pages\CreateProject::route('/create'),
            'edit' => Pages\EditProject::route('/{record}/edit'),
        ];
    }
}
