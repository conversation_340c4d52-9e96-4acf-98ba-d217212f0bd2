<?php

namespace App\Filament\Resources\ClientResource\Pages;

use App\Filament\Resources\ClientResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListClients extends ListRecords
{
    protected static string $resource = ClientResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            Actions\Action::make('view')
                ->label('View')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(fn($record) => route('filament.resources.clients.view', ['record' => $record->getKey()]))
                ->openUrlInNewTab(),
        ];
    }
}
