<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NotificationEvent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'display_name',
        'module',
        'description',
        'is_active',
        'is_hierarchical',
        'data_schema',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'is_hierarchical' => 'boolean',
        'data_schema' => 'array',
    ];

    /**
     * Get the role preferences for this notification event.
     */
    public function rolePreferences(): Has<PERSON>any
    {
        return $this->hasMany(NotificationRolePreference::class);
    }

    /**
     * Get the user preferences for this notification event.
     */
    public function userPreferences(): HasMany
    {
        return $this->hasMany(NotificationPreference::class);
    }

    /**
     * Get the notifications for this event.
     */
    public function notifications(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(AppNotification::class);
    }
}
