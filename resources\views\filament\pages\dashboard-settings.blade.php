<x-filament-panels::page>
    <div class="mb-6">
        <p class="text-gray-500">
            Customize your dashboard by enabling or disabling widgets, changing their size, and rearranging their order.
            Click "Save Settings" when you're done to apply your changes.
        </p>
    </div>

    <form wire:submit="save">
        {{ $this->form }}
        <div class="mt-6 flex justify-between">
            <x-filament::button type="button" color="gray" icon="heroicon-m-arrow-left" tag="a"
                href="{{ route('filament.admin.pages.dashboard') }}">
                Back to Dashboard
            </x-filament::button>

            <x-filament::button type="submit" icon="heroicon-m-check">
                Save Settings
            </x-filament::button>
        </div>
    </form>
</x-filament-panels::page>
