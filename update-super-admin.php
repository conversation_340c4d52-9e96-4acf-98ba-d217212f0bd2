<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Get the super_admin role
$role = \Spatie\Permission\Models\Role::findByName('super_admin', 'web');

// Get all permissions
$permissions = \Spatie\Permission\Models\Permission::all();

// Sync all permissions to the super_admin role
$role->syncPermissions($permissions);

echo "Super admin role updated with all permissions.\n";
