<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects Dashboard - Advanced</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="{{ asset('css/project-table.css') }}" rel="stylesheet">
    <style>
        body {
            background-color: #121212;
            color: #f3f4f6;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-header {
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: #ffffff;
        }

        .dashboard-subtitle {
            color: #9ca3af;
            margin-top: 0.5rem;
        }

        /* Additional styles for advanced features */
        .filter-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .filter-select {
            background-color: #1e1e1e;
            border: 1px solid #333;
            color: #fff;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
        }

        .search-input {
            background-color: #1e1e1e;
            border: 1px solid #333;
            color: #fff;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            flex-grow: 1;
        }

        .search-input::placeholder {
            color: #6b7280;
        }

        .action-button {
            background-color: #2563eb;
            color: #fff;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .action-button:hover {
            background-color: #1d4ed8;
        }

        .projects-table th {
            cursor: pointer;
            user-select: none;
        }

        .projects-table th:hover {
            color: #fff;
        }

        .sort-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-left: 0.25rem;
        }

        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
            color: #9ca3af;
            font-size: 0.875rem;
        }

        .pagination-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .pagination-button {
            background-color: #1e1e1e;
            border: 1px solid #333;
            color: #fff;
            width: 2rem;
            height: 2rem;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }

        .pagination-button:hover:not(.pagination-button-active) {
            background-color: #333;
        }

        .pagination-button-active {
            background-color: #2563eb;
            border-color: #2563eb;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 0;
            color: #6b7280;
        }
    </style>
</head>

<body>
    <div class="container" x-data="projectsData()">
        <div class="dashboard-header">
            <div>
                <h1 class="dashboard-title">Projects Dashboard</h1>
                <p class="dashboard-subtitle">Monitor your ongoing and recent projects</p>
            </div>
            <button class="action-button">+ New Project</button>
        </div>

        <div class="filter-bar">
            <select class="filter-select" x-model="filters.status">
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="on-hold">On Hold</option>
            </select>

            <select class="filter-select" x-model="filters.bde">
                <option value="">All BDEs</option>
                <option value="Priya Patel">Priya Patel</option>
                <option value="Amit Kumar">Amit Kumar</option>
                <option value="Rahul Sharma">Rahul Sharma</option>
            </select>

            <input type="text" class="search-input" placeholder="Search projects..." x-model="filters.search">
        </div>

        <div class="projects-card">
            <h2>Recent Projects</h2>
            <table class="projects-table">
                <thead>
                    <tr>
                        <th @click="sortBy('title')">
                            Title
                            <span class="sort-icon" x-show="sortColumn === 'title'">
                                <template x-if="sortDirection === 'asc'">↑</template>
                                <template x-if="sortDirection === 'desc'">↓</template>
                            </span>
                        </th>
                        <th @click="sortBy('client')">
                            Client
                            <span class="sort-icon" x-show="sortColumn === 'client'">
                                <template x-if="sortDirection === 'asc'">↑</template>
                                <template x-if="sortDirection === 'desc'">↓</template>
                            </span>
                        </th>
                        <th @click="sortBy('bde')">
                            BDE
                            <span class="sort-icon" x-show="sortColumn === 'bde'">
                                <template x-if="sortDirection === 'asc'">↑</template>
                                <template x-if="sortDirection === 'desc'">↓</template>
                            </span>
                        </th>
                        <th @click="sortBy('amount')" class="text-right">
                            Amount
                            <span class="sort-icon" x-show="sortColumn === 'amount'">
                                <template x-if="sortDirection === 'asc'">↑</template>
                                <template x-if="sortDirection === 'desc'">↓</template>
                            </span>
                        </th>
                        <th @click="sortBy('status')">
                            Status
                            <span class="sort-icon" x-show="sortColumn === 'status'">
                                <template x-if="sortDirection === 'asc'">↑</template>
                                <template x-if="sortDirection === 'desc'">↓</template>
                            </span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <template x-if="filteredProjects.length === 0">
                        <tr>
                            <td colspan="5" class="empty-state">
                                No projects found matching your filters
                            </td>
                        </tr>
                    </template>
                    <template x-for="project in filteredProjects" :key="project.id">
                        <tr>
                            <td>
                                <div class="project-title" x-text="project.title"></div>
                            </td>
                            <td>
                                <div class="client-name" x-text="project.client"></div>
                            </td>
                            <td>
                                <div class="bde-name">
                                    <div class="bde-avatar" x-text="getInitials(project.bde)"></div>
                                    <span x-text="project.bde"></span>
                                </div>
                            </td>
                            <td class="amount-column" x-text="formatCurrency(project.amount)"></td>
                            <td>
                                <span class="status-badge"
                                    :class="{
                                        'status-active': project.status === 'active',
                                        'status-completed': project.status === 'completed',
                                        'status-on-hold': project.status === 'on-hold'
                                    }"
                                    x-text="formatStatus(project.status)"></span>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>

            <div class="pagination">
                <div>
                    Showing <span x-text="filteredProjects.length"></span> projects
                </div>
                <div class="pagination-buttons">
                    <button class="pagination-button pagination-button-active">1</button>
                    <button class="pagination-button">2</button>
                    <button class="pagination-button">3</button>
                    <button class="pagination-button">→</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function projectsData() {
            return {
                projects: [{
                        id: 1,
                        title: 'SEO Optimization Campaign',
                        client: 'TechSolutions Inc.',
                        bde: 'Priya Patel',
                        amount: 180000,
                        status: 'active'
                    },
                    {
                        id: 2,
                        title: 'Mobile App Development',
                        client: 'Global Enterprises',
                        bde: 'Amit Kumar',
                        amount: 450000,
                        status: 'active'
                    },
                    {
                        id: 3,
                        title: 'E-commerce Platform Development',
                        client: 'Innovate Solutions',
                        bde: 'Priya Patel',
                        amount: 350000,
                        status: 'completed'
                    },
                    {
                        id: 4,
                        title: 'Server Infrastructure Setup',
                        client: 'NextGen Systems',
                        bde: 'Rahul Sharma',
                        amount: 120000,
                        status: 'on-hold'
                    },
                    {
                        id: 5,
                        title: 'Content Marketing Strategy',
                        client: 'Acme Corporation',
                        bde: 'Amit Kumar',
                        amount: 95000,
                        status: 'active'
                    }
                ],
                filters: {
                    search: '',
                    status: '',
                    bde: ''
                },
                sortColumn: 'id',
                sortDirection: 'asc',

                get filteredProjects() {
                    return this.projects
                        .filter(project => {
                            const matchesSearch = this.filters.search === '' ||
                                project.title.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                                project.client.toLowerCase().includes(this.filters.search.toLowerCase());

                            const matchesStatus = this.filters.status === '' ||
                                project.status === this.filters.status;

                            const matchesBde = this.filters.bde === '' ||
                                project.bde === this.filters.bde;

                            return matchesSearch && matchesStatus && matchesBde;
                        })
                        .sort((a, b) => {
                            const aValue = a[this.sortColumn];
                            const bValue = b[this.sortColumn];

                            if (typeof aValue === 'string') {
                                if (this.sortDirection === 'asc') {
                                    return aValue.localeCompare(bValue);
                                } else {
                                    return bValue.localeCompare(aValue);
                                }
                            } else {
                                if (this.sortDirection === 'asc') {
                                    return aValue - bValue;
                                } else {
                                    return bValue - aValue;
                                }
                            }
                        });
                },

                sortBy(column) {
                    if (this.sortColumn === column) {
                        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        this.sortColumn = column;
                        this.sortDirection = 'asc';
                    }
                },

                formatCurrency(amount) {
                    return '₹' + amount.toLocaleString('en-IN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                },

                formatStatus(status) {
                    if (status === 'on-hold') return 'On Hold';
                    return status.charAt(0).toUpperCase() + status.slice(1);
                },

                getInitials(name) {
                    return name.split(' ').map(part => part.charAt(0)).join('');
                }
            };
        }
    </script>
</body>

</html>
