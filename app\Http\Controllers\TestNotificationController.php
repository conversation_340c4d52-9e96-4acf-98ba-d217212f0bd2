<?php

namespace App\Http\Controllers;

use App\Models\NotificationEvent;
use App\Models\Role;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;

class TestNotificationController extends Controller
{
    public function testNotification(Request $request, NotificationService $notificationService)
    {
        // Create role preferences for the notification event
        $event = NotificationEvent::where('name', 'project_created')->first();
        
        if (!$event) {
            return response()->json(['error' => 'Notification event not found'], 404);
        }
        
        // Get admin role
        $adminRole = Role::where('name', 'admin')->first();
        
        if (!$adminRole) {
            return response()->json(['error' => 'Admin role not found'], 404);
        }
        
        // Create role preference if it doesn't exist
        $event->rolePreferences()->updateOrCreate(
            ['role_id' => $adminRole->id],
            [
                'email' => true,
                'in_app' => true,
                'is_enabled' => true,
            ]
        );
        
        // Get admin user
        $adminUser = User::whereHas('roles', function ($query) use ($adminRole) {
            $query->where('id', $adminRole->id);
        })->first();
        
        if (!$adminUser) {
            return response()->json(['error' => 'Admin user not found'], 404);
        }
        
        // Send notification
        $notifications = $notificationService->send('project_created', [
            'project_id' => 1,
            'project_title' => 'Test Project',
            'client_name' => 'Test Client',
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Notification sent successfully',
            'notifications' => $notifications,
        ]);
    }
}
