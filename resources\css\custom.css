/* Custom CSS for Monthly Revenue Chart */
.chart-bar {
    background-color: #4f46e5; /* Primary color */
    border-radius: 0.5rem; /* Rounded corners */
}
.chart-bar.dark {
    background-color: #1e40af; /* Dark mode color */
}
.chart-bar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.chart-bar-label {
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: #6b7280; /* Gray color */
}
.chart-bar-label.dark {
    color: #d1d5db; /* Light gray for dark mode */
}
