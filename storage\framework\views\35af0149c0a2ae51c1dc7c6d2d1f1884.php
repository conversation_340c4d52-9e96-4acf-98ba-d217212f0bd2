<?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('notification-bell', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-3163051866-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?><?php /**PATH D:\wamp64\www\smms\storage\framework\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php ENDPATH**/ ?>