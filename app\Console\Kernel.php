<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Send payment due notifications daily at midnight
        $schedule->command('payments:send-due-notifications')
            ->daily()
            ->description('Send notifications for overdue payments');

        // Check for overdue milestones daily at 9:00 AM
        $schedule->command('milestones:check-overdue')
            ->daily()
            ->at('09:00')
            ->description('Check for overdue milestones and send notifications');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
