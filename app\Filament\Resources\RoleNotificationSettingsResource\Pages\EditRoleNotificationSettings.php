<?php

namespace App\Filament\Resources\RoleNotificationSettingsResource\Pages;

use App\Filament\Resources\RoleNotificationSettingsResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditRoleNotificationSettings extends EditRecord
{
    protected static string $resource = RoleNotificationSettingsResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
