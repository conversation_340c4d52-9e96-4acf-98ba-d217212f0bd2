<?php

namespace App\Console\Commands;

use App\Models\Milestone;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CheckOverdueMilestones extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'milestones:check-overdue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for overdue milestones and send notifications';

    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for overdue milestones...');

        // Get overdue milestones (due date has passed and status is not completed)
        $overdueMilestones = Milestone::with(['project', 'project.user'])
            ->where('due_date', '<', Carbon::today())
            ->whereNotIn('status', ['completed'])
            ->get();

        if ($overdueMilestones->isEmpty()) {
            $this->info('No overdue milestones found.');
            return;
        }

        $this->info("Found {$overdueMilestones->count()} overdue milestones.");

        foreach ($overdueMilestones as $milestone) {
            $this->processOverdueMilestone($milestone);
        }

        $this->info('Overdue milestone notifications sent successfully.');
    }

    private function processOverdueMilestone(Milestone $milestone)
    {
        $daysOverdue = Carbon::parse($milestone->due_date)->diffInDays(Carbon::today());

        // Update milestone status to delayed if not already
        if ($milestone->status !== 'delayed') {
            $milestone->update(['status' => 'delayed']);
            $this->line("Updated milestone '{$milestone->title}' status to delayed.");
        }

        // Prepare notification data
        $notificationData = [
            'milestone_id' => $milestone->id,
            'project_id' => $milestone->project_id,
            'milestone_title' => $milestone->title,
            'project_title' => $milestone->project->title,
            'due_date' => $milestone->due_date->format('Y-m-d'),
            'days_overdue' => $daysOverdue,
            'bde_name' => $milestone->project->user->name,
        ];

        // Send notification using NotificationService (respects role settings)
        $notifications = $this->notificationService->send('milestone_overdue', $notificationData);

        // Also send to specific project owner if not covered by role settings
        if ($milestone->project->user) {
            $this->notificationService->send('milestone_overdue', $notificationData, $milestone->project->user);
        }

        $this->line("Sent notifications for overdue milestone: {$milestone->title} ({$daysOverdue} days overdue)");
    }
}
