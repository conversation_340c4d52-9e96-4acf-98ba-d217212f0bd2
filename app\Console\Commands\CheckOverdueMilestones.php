<?php

namespace App\Console\Commands;

use App\Models\Milestone;
use App\Models\User;
use App\Models\AppNotification;
use App\Models\NotificationEvent;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CheckOverdueMilestones extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'milestones:check-overdue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for overdue milestones and send notifications';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for overdue milestones...');

        // Get overdue milestones (due date has passed and status is not completed)
        $overdueMilestones = Milestone::with(['project', 'project.user'])
            ->where('due_date', '<', Carbon::today())
            ->whereNotIn('status', ['completed'])
            ->get();

        if ($overdueMilestones->isEmpty()) {
            $this->info('No overdue milestones found.');
            return;
        }

        $this->info("Found {$overdueMilestones->count()} overdue milestones.");

        foreach ($overdueMilestones as $milestone) {
            $this->processOverdueMilestone($milestone);
        }

        $this->info('Overdue milestone notifications sent successfully.');
    }

    private function processOverdueMilestone(Milestone $milestone)
    {
        $daysOverdue = Carbon::parse($milestone->due_date)->diffInDays(Carbon::today());

        // Update milestone status to delayed if not already
        if ($milestone->status !== 'delayed') {
            $milestone->update(['status' => 'delayed']);
            $this->line("Updated milestone '{$milestone->title}' status to delayed.");
        }

        // Get the notification event
        $event = NotificationEvent::where('name', 'milestone_overdue')->first();
        if (!$event) {
            $this->error("Notification event 'milestone_overdue' not found.");
            return;
        }

        // Prepare notification data
        $notificationData = [
            'milestone_id' => $milestone->id,
            'project_id' => $milestone->project_id,
            'milestone_title' => $milestone->title,
            'project_title' => $milestone->project->title,
            'due_date' => $milestone->due_date->format('Y-m-d'),
            'days_overdue' => $daysOverdue,
            'bde_name' => $milestone->project->user->name,
        ];

        // Create notification for project owner (BDE)
        $this->createNotification(
            $milestone->project->user_id,
            $event->id,
            'Milestone Overdue',
            "Milestone '{$milestone->title}' in project '{$milestone->project->title}' is {$daysOverdue} day(s) overdue.",
            $notificationData
        );

        // Create notification for admin users
        $adminUsers = User::role('super_admin')->get();
        foreach ($adminUsers as $admin) {
            $this->createNotification(
                $admin->id,
                $event->id,
                'Milestone Overdue Alert',
                "Milestone '{$milestone->title}' in project '{$milestone->project->title}' (BDE: {$milestone->project->user->name}) is {$daysOverdue} day(s) overdue.",
                $notificationData
            );
        }

        $this->line("Sent notifications for overdue milestone: {$milestone->title} ({$daysOverdue} days overdue)");
    }

    private function createNotification($userId, $eventId, $title, $message, $data)
    {
        // Check if notification already sent today for this milestone
        $existingNotification = AppNotification::where('user_id', $userId)
            ->where('title', $title)
            ->where('data->milestone_id', $data['milestone_id'])
            ->whereDate('created_at', Carbon::today())
            ->first();

        if (!$existingNotification) {
            AppNotification::create([
                'user_id' => $userId,
                'notification_event_id' => $eventId,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'status' => 'sent',
                'sent_at' => now(),
            ]);
        }
    }
}
