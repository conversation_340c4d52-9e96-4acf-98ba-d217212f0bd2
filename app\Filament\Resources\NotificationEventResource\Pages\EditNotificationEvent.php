<?php

namespace App\Filament\Resources\NotificationEventResource\Pages;

use App\Filament\Resources\NotificationEventResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditNotificationEvent extends EditRecord
{
    protected static string $resource = NotificationEventResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
