<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class CurrencyConverter
{
    protected static array $conversionRates = [
        'INR' => [
            'USD' => 0.012,
            'EUR' => 0.011,
            'GBP' => 0.009,
        ],
        'USD' => [
            'INR' => 82.0,
            'EUR' => 0.92,
            'GBP' => 0.76,
        ],
        'EUR' => [
            'INR' => 89.0,
            'USD' => 1.09,
            'GBP' => 0.83,
        ],
        'GBP' => [
            'INR' => 100.0,
            'USD' => 1.31,
            'EUR' => 1.20,
        ],
    ];

    protected static function fetchLiveRates(): array
    {
        $apiUrl = 'https://api.exchangerate-api.com/v4/latest/INR'; // Example API URL

        try {
            $response = file_get_contents($apiUrl);
            $data = json_decode($response, true);

            return $data['rates'] ?? [];
        } catch (\Exception $e) {
            // Log the error and fall back to static rates
            Log::error('Failed to fetch live currency rates: ' . $e->getMessage());
            return self::$conversionRates['INR'];
        }
    }

    public static function convert(string $fromCurrency, string $toCurrency, float $amount): float
    {
        $liveRates = self::fetchLiveRates();

        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $rate = $liveRates[$toCurrency] ?? (self::$conversionRates[$fromCurrency][$toCurrency] ?? 1);

        return $amount * $rate;
    }
}
