<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MilestoneResource\Pages;
use App\Filament\Resources\MilestoneResource\RelationManagers;
use App\Models\Milestone;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class MilestoneResource extends Resource
{
    protected static ?string $model = Milestone::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // If user has BDE role, only show milestones for their projects
        if (auth()->check() && auth()->user()->hasRole('bde')) {
            $query->whereHas('project', function (Builder $query) {
                $query->where('user_id', auth()->id());
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Milestone Details')
                    ->schema([
                        Forms\Components\Select::make('project_id')
                            ->relationship('project', 'title', function (Builder $query) {
                                // If user has BDE role, only show their projects
                                if (auth()->check() && auth()->user()->hasRole('bde')) {
                                    $query->where('user_id', auth()->id());
                                }
                                return $query;
                            })
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\DatePicker::make('due_date')
                                    ->required()
                                    ->label('Due Date')
                                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                        $projectEndDate = $get('project.end_date');
                                        if ($projectEndDate && $state > $projectEndDate) {
                                            $set('due_date', null);
                                            Notification::make()
                                                ->title('Validation Error')
                                                ->body('Due Date cannot be greater than Project End Date.')
                                                ->danger()
                                                ->send();
                                        }
                                    }),
                                Forms\Components\TextInput::make('percentage')
                                    ->required()
                                    ->numeric()
                                    ->suffix('%')
                                    ->maxValue(100),
                                Forms\Components\TextInput::make('amount')
                                    ->required()
                                    ->numeric()
                                    ->prefix('₹')
                                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                        $projectId = $get('project_id');
                                        $projectTotalPayment = $get('project.total_payment');
                                        $milestoneId = $get('id');
                                        $query = \App\Models\Milestone::where('project_id', $projectId);
                                        if ($milestoneId) {
                                            $query->where('id', '!=', $milestoneId);
                                        }
                                        $totalMilestoneAmount = $query->sum('amount');
                                        $newTotal = $totalMilestoneAmount + ($state ?? 0);
                                        if ($projectTotalPayment && $newTotal > $projectTotalPayment) {
                                            $set('amount', null);
                                            
Filament\Notifications\Notification::make()
                                        ->title('Validation Error')
                                        ->body('Total Milestone Amount cannot exceed Project Total Payment.')
                                        ->danger()
                                        ->send();
                                        }
                                    })
                                    ->rules([
                                        function (
                                            $attribute, $value, $fail
                                        ) {
                                            $projectId = request()->input('project_id');
                                            $milestoneId = request()->route('record');
                                            $project = \App\Models\Project::find($projectId);
                                            if (!$project) return;
                                            $query = \App\Models\Milestone::where('project_id', $projectId);
                                            if ($milestoneId) {
                                                $query->where('id', '!=', $milestoneId);
                                            }
                                            $totalMilestoneAmount = $query->sum('amount');
                                            $newTotal = $totalMilestoneAmount + ($value ?? 0);
                                            if ($project->total_payment && $newTotal > $project->total_payment) {
                                                $fail('Total Milestone Amount cannot exceed Project Total Payment.');
                                            }
                                        }
                                    ]),
                        Forms\Components\Select::make('status')
                            ->options([
                                'pending' => 'Pending',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'delayed' => 'Delayed',
                            ])
                            ->default('pending')
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('project.title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('percentage')
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->money('INR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'gray',
                        'in_progress' => 'info',
                        'completed' => 'success',
                        'delayed' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMilestones::route('/'),
            'create' => Pages\CreateMilestone::route('/create'),
            'edit' => Pages\EditMilestone::route('/{record}/edit'),
        ];
    }

    // public static function deleting(Milestone $milestone)
    // {
    //     // Delete related payments
    //     \App\Models\Payment::where('milestone_id', $milestone->id)->delete();
    //     // Delete related incentives
    //     \App\Models\Incentive::where('milestone_id', $milestone->id)->delete();
    // }
}
