<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MilestoneResource\Pages;
use App\Filament\Resources\MilestoneResource\RelationManagers;
use App\Models\Milestone;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class MilestoneResource extends Resource
{
    protected static ?string $model = Milestone::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // If user has BDE role, only show milestones for their projects
        if (auth()->check() && auth()->user()->hasRole('bde')) {
            $query->whereHas('project', function (Builder $query) {
                $query->where('user_id', auth()->id());
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Milestone Details')
                    ->schema([
                        Forms\Components\Select::make('project_id')
                            ->relationship('project', 'title', function (Builder $query) {
                                // If user has BDE role, only show their projects
                                if (auth()->check() && auth()->user()->hasRole('bde')) {
                                    $query->where('user_id', auth()->id());
                                }
                                return $query;
                            })
                            ->searchable()
                            ->preload()
                            ->required()
                            ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                if (!$state) return;

                                $project = \App\Models\Project::find($state);
                                if (!$project) return;

                                $totalMilestoneAmount = \App\Models\Milestone::where('project_id', $state)->sum('amount');
                                $remainingAmount = $project->total_payment - $totalMilestoneAmount;

                                \Filament\Notifications\Notification::make()
                                    ->title('Project Payment Information')
                                    ->body("Project Total: ₹" . number_format($project->total_payment, 2) . " | Already Allocated: ₹" . number_format($totalMilestoneAmount, 2) . " | Remaining: ₹" . number_format($remainingAmount, 2))
                                    ->info()
                                    ->send();
                            }),
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\DatePicker::make('due_date')
                                    ->required()
                                    ->label('Due Date')
                                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                        if (!$state || !$get('project_id')) return;

                                        $projectId = $get('project_id');
                                        $project = \App\Models\Project::find($projectId);
                                        if (!$project || !$project->end_date) return;

                                        if ($state > $project->end_date) {
                                            $set('due_date', null);
                                            \Filament\Notifications\Notification::make()
                                                ->title('Validation Error')
                                                ->body("Due Date cannot be greater than Project End Date (" . $project->end_date->format('M d, Y') . ").")
                                                ->danger()
                                                ->send();
                                        }
                                    })
                                    ->rules([
                                        function () {
                                            return function (string $attribute, $value, \Closure $fail) {
                                                $data = request()->all();
                                                $projectId = $data['project_id'] ?? null;

                                                if (!$projectId || !$value) return;

                                                $project = \App\Models\Project::find($projectId);
                                                if (!$project || !$project->end_date) return;

                                                if ($value > $project->end_date) {
                                                    $fail("Due Date cannot be greater than Project End Date (" . $project->end_date->format('M d, Y') . ").");
                                                }
                                            };
                                        }
                                    ]),
                                Forms\Components\TextInput::make('percentage')
                                    ->required()
                                    ->numeric()
                                    ->suffix('%')
                                    ->maxValue(100),
                                Forms\Components\TextInput::make('amount')
                                    ->required()
                                    ->numeric()
                                    ->prefix('₹')
                                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                        if (!$state || !$get('project_id')) return;

                                        $projectId = $get('project_id');
                                        $project = \App\Models\Project::find($projectId);
                                        if (!$project) return;

                                        $milestoneId = $get('id');
                                        $query = \App\Models\Milestone::where('project_id', $projectId);
                                        if ($milestoneId) {
                                            $query->where('id', '!=', $milestoneId);
                                        }
                                        $totalMilestoneAmount = $query->sum('amount');
                                        $newTotal = $totalMilestoneAmount + ($state ?? 0);

                                        if ($project->total_payment && $newTotal > $project->total_payment) {
                                            $set('amount', null);
                                            \Filament\Notifications\Notification::make()
                                                ->title('Validation Error')
                                                ->body("Total milestone amount (₹" . number_format($newTotal, 2) . ") cannot exceed project total payment (₹" . number_format($project->total_payment, 2) . ").")
                                                ->danger()
                                                ->send();
                                        }
                                    })
                                    ->rules([
                                        function () {
                                            return function (string $attribute, $value, \Closure $fail) {
                                                $data = request()->all();
                                                $projectId = $data['project_id'] ?? null;
                                                $milestoneId = request()->route('record') ?? null;

                                                if (!$projectId || !$value) return;

                                                $project = \App\Models\Project::find($projectId);
                                                if (!$project || !$project->total_payment) return;

                                                $query = \App\Models\Milestone::where('project_id', $projectId);
                                                if ($milestoneId) {
                                                    $query->where('id', '!=', $milestoneId);
                                                }
                                                $totalMilestoneAmount = $query->sum('amount');
                                                $newTotal = $totalMilestoneAmount + $value;

                                                if ($newTotal > $project->total_payment) {
                                                    $fail("Total milestone amount (₹" . number_format($newTotal, 2) . ") cannot exceed project total payment (₹" . number_format($project->total_payment, 2) . ").");
                                                }
                                            };
                                        }
                                    ]),
                                Forms\Components\Select::make('status')
                                    ->options([
                                        'pending' => 'Pending',
                                        'in_progress' => 'In Progress',
                                        'completed' => 'Completed',
                                        'delayed' => 'Delayed',
                                    ])
                                    ->default('pending')
                                    ->required(),
                            ]),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('project.title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable()
                    ->color(function ($record) {
                        return $record->due_date < now() && $record->status !== 'completed' ? 'danger' : null;
                    })
                    ->description(function ($record) {
                        if ($record->due_date < now() && $record->status !== 'completed') {
                            $daysOverdue = \Carbon\Carbon::parse($record->due_date)->diffInDays(now());
                            return "Overdue by {$daysOverdue} day(s)";
                        }
                        return null;
                    }),
                Tables\Columns\TextColumn::make('percentage')
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->money('INR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'gray',
                        'in_progress' => 'info',
                        'completed' => 'success',
                        'delayed' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('overdue')
                    ->label('Overdue Milestones')
                    ->query(fn($query) => $query->where('due_date', '<', now())->whereNotIn('status', ['completed']))
                    ->toggle(),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'delayed' => 'Delayed',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMilestones::route('/'),
            'create' => Pages\CreateMilestone::route('/create'),
            'edit' => Pages\EditMilestone::route('/{record}/edit'),
        ];
    }

    // public static function deleting(Milestone $milestone)
    // {
    //     // Delete related payments
    //     \App\Models\Payment::where('milestone_id', $milestone->id)->delete();
    //     // Delete related incentives
    //     \App\Models\Incentive::where('milestone_id', $milestone->id)->delete();
    // }
}
