{"__meta": {"id": "01JXB8PPR680F18B519X201GMW", "datetime": "2025-06-09 21:14:22", "utime": **********.854829, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749503657.929018, "end": **********.85485, "duration": 4.925832033157349, "duration_str": "4.93s", "measures": [{"label": "Booting", "start": 1749503657.929018, "relative_start": 0, "end": **********.373931, "relative_end": **********.373931, "duration": 1.****************, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.373946, "relative_start": 1.****************, "end": **********.854853, "relative_end": 2.86102294921875e-06, "duration": 3.****************, "duration_str": "3.48s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.149674, "relative_start": 2.***************, "end": **********.197582, "relative_end": **********.197582, "duration": 0.*****************, "duration_str": "47.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.78859, "relative_start": 3.***************, "end": **********.78859, "relative_end": **********.78859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.810385, "relative_start": 3.****************, "end": **********.810385, "relative_end": **********.810385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.856457, "relative_start": 3.927438974380493, "end": **********.856457, "relative_end": **********.856457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.903428, "relative_start": 3.974410057067871, "end": **********.903428, "relative_end": **********.903428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.929893, "relative_start": 4.000874996185303, "end": **********.929893, "relative_end": **********.929893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.942477, "relative_start": 4.013458967208862, "end": **********.942477, "relative_end": **********.942477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.952552, "relative_start": 4.023534059524536, "end": **********.952552, "relative_end": **********.952552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.977353, "relative_start": 4.048335075378418, "end": **********.977353, "relative_end": **********.977353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.078162, "relative_start": 4.149143934249878, "end": **********.078162, "relative_end": **********.078162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.091874, "relative_start": 4.162855863571167, "end": **********.091874, "relative_end": **********.091874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.166653, "relative_start": 4.237634897232056, "end": **********.166653, "relative_end": **********.166653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.175243, "relative_start": 4.246224880218506, "end": **********.175243, "relative_end": **********.175243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.241759, "relative_start": 4.312741041183472, "end": **********.241759, "relative_end": **********.241759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.264728, "relative_start": 4.335710048675537, "end": **********.264728, "relative_end": **********.264728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.370821, "relative_start": 4.441802978515625, "end": **********.370821, "relative_end": **********.370821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.378928, "relative_start": 4.449909925460815, "end": **********.378928, "relative_end": **********.378928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.422948, "relative_start": 4.493929862976074, "end": **********.422948, "relative_end": **********.422948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.43239, "relative_start": 4.503371953964233, "end": **********.43239, "relative_end": **********.43239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.473309, "relative_start": 4.544291019439697, "end": **********.473309, "relative_end": **********.473309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.480446, "relative_start": 4.5514280796051025, "end": **********.480446, "relative_end": **********.480446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.539758, "relative_start": 4.6107399463653564, "end": **********.539758, "relative_end": **********.539758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.546383, "relative_start": 4.617364883422852, "end": **********.546383, "relative_end": **********.546383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.604907, "relative_start": 4.675889015197754, "end": **********.604907, "relative_end": **********.604907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.611273, "relative_start": 4.682255029678345, "end": **********.611273, "relative_end": **********.611273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.662289, "relative_start": 4.733270883560181, "end": **********.662289, "relative_end": **********.662289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.668967, "relative_start": 4.739948987960815, "end": **********.668967, "relative_end": **********.668967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.706887, "relative_start": 4.777868986129761, "end": **********.706887, "relative_end": **********.706887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.712657, "relative_start": 4.783638954162598, "end": **********.712657, "relative_end": **********.712657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.753495, "relative_start": 4.824476957321167, "end": **********.753495, "relative_end": **********.753495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.760057, "relative_start": 4.831038951873779, "end": **********.760057, "relative_end": **********.760057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.800537, "relative_start": 4.871519088745117, "end": **********.800537, "relative_end": **********.800537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.806685, "relative_start": 4.87766695022583, "end": **********.806685, "relative_end": **********.806685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.841214, "relative_start": 4.912195920944214, "end": **********.841214, "relative_end": **********.841214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.849669, "relative_start": 4.920650959014893, "end": **********.852311, "relative_end": **********.852311, "duration": 0.002641916275024414, "duration_str": "2.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 52114328, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 33, "nb_templates": 33, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.78856, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.810359, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.856421, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.903407, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.92987, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.942456, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.952529, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.977329, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.078136, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.091852, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.166616, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.175219, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.241738, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.260063, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.3708, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.378908, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.422922, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.43225, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.47329, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.480053, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.539728, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.546352, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.604888, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.611255, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.662268, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.668946, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.706868, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.712637, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.753476, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.760034, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.800518, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.806667, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.841194, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05183, "accumulated_duration_str": "51.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'jxkhZdGMROOSU4U80hX5Jk115sFIUKaX39FwYZ6k' limit 1", "type": "query", "params": [], "bindings": ["jxkhZdGMROOSU4U80hX5Jk115sFIUKaX39FwYZ6k"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.327033, "duration": 0.00617, "duration_str": "6.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 11.904}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.343416, "duration": 0.03657, "duration_str": "36.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.904, "width_percent": 70.558}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.418068, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.462, "width_percent": 2.778}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.422885, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.24, "width_percent": 2.045}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.425107, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.285, "width_percent": 1.235}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.4667962, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.52, "width_percent": 6.29}, {"sql": "select count(*) as aggregate from `notification_events`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.766138, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.81, "width_percent": 2.72}, {"sql": "select * from `notification_events` order by `notification_events`.`id` asc limit 12 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.7692199, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.53, "width_percent": 2.47}]}, "models": {"data": {"App\\Models\\NotificationEvent": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 14, "is_counter": true}, "livewire": {"data": {"app.filament.resources.notification-event-resource.pages.list-notification-events #wHuji8u3tRKjasVT4ox9": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:3 [\n      \"module\" => array:1 [\n        \"value\" => null\n      ]\n      \"is_active\" => array:1 [\n        \"value\" => null\n      ]\n      \"is_hierarchical\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => \"all\"\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:1 [\n      \"created_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.notification-event-resource.pages.list-notification-events\"\n  \"component\" => \"App\\Filament\\Resources\\NotificationEventResource\\Pages\\ListNotificationEvents\"\n  \"id\" => \"wHuji8u3tRKjasVT4ox9\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 75, "messages": [{"message": "[\n  ability => create,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2139105700 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2139105700\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.523587, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-167814566 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167814566\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.156328, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2044143028 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044143028\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.643131, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-185795962 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185795962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.036602, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-942814912 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942814912\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.041797, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1535657938 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1535657938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.069042, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-504343680 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504343680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.071022, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1257268710 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1257268710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.074675, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1821396643 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821396643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.091328, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-87514298 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87514298\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.11215, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-6858228 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6858228\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115509, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1838846499 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838846499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.141501, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1376364217 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376364217\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.144136, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1598732993 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598732993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.160119, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1483226848 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483226848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.174685, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-351492680 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351492680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.186428, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-724037995 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724037995\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.189348, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-663694340 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663694340\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.227494, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-193190632 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193190632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.229697, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1439158226 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439158226\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.237121, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-416817381 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416817381\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.259564, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1008349737 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008349737\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296404, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-911950664 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911950664\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303993, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-517742079 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517742079\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.363492, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1978044050 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978044050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.366137, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-367049805 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367049805\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.368637, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-200396197 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-200396197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.378457, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-853090999 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853090999\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.390628, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1061664068 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061664068\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.393648, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-123525423 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123525423\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414159, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-713469668 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713469668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.417103, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1595907182 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595907182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.419861, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1946879905 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946879905\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.431232, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2072105799 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072105799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.442643, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1950408982 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950408982\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.446649, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1394630144 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394630144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.466801, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1818883929 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818883929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.46865, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1367902500 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367902500\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.471111, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1780521207 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780521207\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.478736, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-128978475 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128978475\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.491092, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1751274964 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751274964\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.497876, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-954774386 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954774386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53223, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-223272281 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223272281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.534421, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1541979101 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541979101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.537569, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1665024348 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665024348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.545909, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1668403827 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668403827\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.56639, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1167669890 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167669890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.571061, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1320142984 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320142984\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596552, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1917344448 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917344448\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.599202, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2121211719 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121211719\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602087, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-599868871 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599868871\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.610825, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1960019256 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960019256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621367, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1207116287 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207116287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.624358, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-606501352 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606501352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.654512, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1031818420 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031818420\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.656457, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-354194097 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354194097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.659049, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-83403746 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83403746\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.668578, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-21042023 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21042023\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.67569, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1816717667 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816717667\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.679791, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-226064126 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226064126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.699029, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-261393306 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261393306\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.700945, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2097241677 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097241677\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.704214, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1902030321 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902030321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.71225, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1531913326 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1531913326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720476, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-809702794 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809702794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.723782, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-441753465 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441753465\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.744265, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1899609717 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1899609717\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.745986, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-391117323 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391117323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.750024, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-485972698 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485972698\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.759634, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-827131703 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827131703\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.767427, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-5641735 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5641735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770584, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1065115484 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065115484\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.792255, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1821069394 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821069394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.794143, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1367157435 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367157435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.79774, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1053644729 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053644729\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.806339, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "4.93s", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1300407514 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1300407514\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-271619226 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r9Dfs9TcuqRE1y6MZcwU85w9mRu26VZYgo3tbzPl</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1815 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;module&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;is_active&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;is_hierarchical&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[{&quot;created_at&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;wHuji8u3tRKjasVT4ox9&quot;,&quot;name&quot;:&quot;app.filament.resources.notification-event-resource.pages.list-notification-events&quot;,&quot;path&quot;:&quot;admin\\/notification-events&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;2a73250358dd1aad27129e820852445cd997c2ea8e1c730f190206eb40fe9142&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>tableRecordsPerPage</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271619226\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2169</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://localhost:8000/admin/notification-events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ik0rNHk4U1d2V2J0aGFhRzhwWHNaV3c9PSIsInZhbHVlIjoiMUNUbkFkWDNWbVVWdmhjbDhpWVdBblAvd3RpbWcxWXZiaFg1SS9FK2FXUklSZFdUUktiZFFadGh1U2UwT21pTDBEbFVEK0hCRWRneTl6bkEwenB4SThpVElhL1hrVzZoRGZCS3lhMUxkZTM1by80WTdsVmRuWEEvU3VzTExMZHBRTkRmSXZEdmZiY01nM21sbHBVcU4wcWhKMGkyK2MwbGhBTmdsUlRuaVUxd1FtL3dCUFBUYUY3bklRTE1kdUUyR2E4d0tuVzE4NEVwRE5oVEJSMnFLa2h2SE5RZFVsK2FHdkdWZC9uT0tqbz0iLCJtYWMiOiI3ZmE1NGZkZTQ4MDUyMzFmYTNiMjBlZDdmYjM1MDQwMzBhNmM0YTA5NjI0YWNmYjY0YjFhMjczMzhhMTg5YjZkIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InRkMWhjS21Wa1ZQcFVjaks1OHNYQmc9PSIsInZhbHVlIjoiV3RRL3ROcXpiT0tWZUtjOEF4TTFobzBhK1lrWFUrOSs2SEtwOTkwYUVEM0FjZVo5eW0zS0d6RnZuYTN4b2lvY2Y5cnQ4NjdCRktaRExpMjVhSUtFQm9GRVBIcDB6UTYrVjdLdkFScHVPYTNyUXYxMUt5SmUrZ3RuL1BvRTMzU08iLCJtYWMiOiJiNTcwZTk4NTBiNjAwYjNiM2QzOWY5NDMwYTExNjQ3ODUxNDNjMGUxZGU1ZTZiYTM3Y2E5YjI1MWE5NmVhNDEwIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6InBYWjlGUHBmK2poOGVZZ1dWT1dpNGc9PSIsInZhbHVlIjoieVBPOW5uT1BKRWVrN21FWDBnVE9UMHkvbzNIc2x3MEp4R0dXRHpOUFUxeW56VmNwWnVZMkhIRDRZTlRodlBQYkMwRTVINkVQT1gzcWU4b2VydlpSYnp6NHBaaHZWQnNRVnNGRWx4MFRCVFFwTFNXVEsySTJsMlAyaEdIUjkvbWEiLCJtYWMiOiJhMWE5NWEwMzE1MTY1ZWY3MjAzYTk4YzkxNDM2MmY4ZDNiOTQ5ZmI3ZGU1Yzk4OWJhNTRiZmFkNzdhMjFiOWZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2030242195 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IaoVXF77cb5deCAtwKpLOWhQkSz6v8KjZyt6VaEz3KMxX2I2xibuZaQaf80m|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r9Dfs9TcuqRE1y6MZcwU85w9mRu26VZYgo3tbzPl</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jxkhZdGMROOSU4U80hX5Jk115sFIUKaX39FwYZ6k</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030242195\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-451828075 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 09 Jun 2025 21:14:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-451828075\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-155830109 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r9Dfs9TcuqRE1y6MZcwU85w9mRu26VZYgo3tbzPl</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/milestones/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>8252cfa560838efc0039628341f3a46f_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155830109\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}