<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\Client::create([
            'name' => 'Acme Corporation',
            'email' => '<EMAIL>',
            'phone' => '+91 9876543210',
            'address' => '123 Business Park, Mumbai, India',
            'contact_person' => '<PERSON>',
            'status' => 'active',
        ]);

        \App\Models\Client::create([
            'name' => 'TechSolutions Inc.',
            'email' => '<EMAIL>',
            'phone' => '+91 8765432109',
            'address' => '456 Tech Hub, Bangalore, India',
            'contact_person' => '<PERSON>',
            'status' => 'active',
        ]);

        \App\Models\Client::create([
            'name' => 'Global Enterprises',
            'email' => '<EMAIL>',
            'phone' => '+91 7654321098',
            'address' => '789 Business Center, Delhi, India',
            'contact_person' => '<PERSON>',
            'status' => 'active',
        ]);

        \App\Models\Client::create([
            'name' => 'Innovate Solutions',
            'email' => '<EMAIL>',
            'phone' => '+91 6543210987',
            'address' => '321 Innovation Park, Hyderabad, India',
            'contact_person' => 'Emily Brown',
            'status' => 'active',
        ]);

        \App\Models\Client::create([
            'name' => 'NextGen Systems',
            'email' => '<EMAIL>',
            'phone' => '+91 5432109876',
            'address' => '654 Tech Valley, Pune, India',
            'contact_person' => 'Michael Wilson',
            'status' => 'lead',
        ]);
    }
}
