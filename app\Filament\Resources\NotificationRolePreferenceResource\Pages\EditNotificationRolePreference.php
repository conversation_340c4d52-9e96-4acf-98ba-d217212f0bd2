<?php

namespace App\Filament\Resources\NotificationRolePreferenceResource\Pages;

use App\Filament\Resources\NotificationRolePreferenceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditNotificationRolePreference extends EditRecord
{
    protected static string $resource = NotificationRolePreferenceResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
