<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Client;
use App\Models\Project;
use App\Models\ProjectType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Carbon;
use JMac\Testing\Traits\AdditionalAssertions;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * @see \App\Http\Controllers\ProjectController
 */
final class ProjectControllerTest extends TestCase
{
    use AdditionalAssertions, RefreshDatabase, WithFaker;

    #[Test]
    public function index_displays_view(): void
    {
        $projects = Project::factory()->count(3)->create();

        $response = $this->get(route('projects.index'));

        $response->assertOk();
        $response->assertViewIs('project.index');
        $response->assertViewHas('projects');
    }


    #[Test]
    public function create_displays_view(): void
    {
        $response = $this->get(route('projects.create'));

        $response->assertOk();
        $response->assertViewIs('project.create');
    }


    #[Test]
    public function store_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\ProjectController::class,
            'store',
            \App\Http\Requests\ProjectControllerStoreRequest::class
        );
    }

    #[Test]
    public function store_saves_and_redirects(): void
    {
        $client = Client::factory()->create();
        $user = User::factory()->create();
        $project_type = ProjectType::factory()->create();
        $title = fake()->sentence(4);
        $won_date = Carbon::parse(fake()->date());
        $start_date = Carbon::parse(fake()->date());
        $total_payment = fake()->randomFloat(/** decimal_attributes **/);
        $duration = fake()->numberBetween(-10000, 10000);
        $duration_unit = fake()->word();
        $payment_cycle = fake()->word();
        $status = fake()->word();

        $response = $this->post(route('projects.store'), [
            'client_id' => $client->id,
            'user_id' => $user->id,
            'project_type_id' => $project_type->id,
            'title' => $title,
            'won_date' => $won_date->toDateString(),
            'start_date' => $start_date->toDateString(),
            'total_payment' => $total_payment,
            'duration' => $duration,
            'duration_unit' => $duration_unit,
            'payment_cycle' => $payment_cycle,
            'status' => $status,
        ]);

        $projects = Project::query()
            ->where('client_id', $client->id)
            ->where('user_id', $user->id)
            ->where('project_type_id', $project_type->id)
            ->where('title', $title)
            ->where('won_date', $won_date)
            ->where('start_date', $start_date)
            ->where('total_payment', $total_payment)
            ->where('duration', $duration)
            ->where('duration_unit', $duration_unit)
            ->where('payment_cycle', $payment_cycle)
            ->where('status', $status)
            ->get();
        $this->assertCount(1, $projects);
        $project = $projects->first();

        $response->assertRedirect(route('projects.index'));
        $response->assertSessionHas('project.id', $project->id);
    }


    #[Test]
    public function show_displays_view(): void
    {
        $project = Project::factory()->create();

        $response = $this->get(route('projects.show', $project));

        $response->assertOk();
        $response->assertViewIs('project.show');
        $response->assertViewHas('project');
    }


    #[Test]
    public function edit_displays_view(): void
    {
        $project = Project::factory()->create();

        $response = $this->get(route('projects.edit', $project));

        $response->assertOk();
        $response->assertViewIs('project.edit');
        $response->assertViewHas('project');
    }


    #[Test]
    public function update_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\ProjectController::class,
            'update',
            \App\Http\Requests\ProjectControllerUpdateRequest::class
        );
    }

    #[Test]
    public function update_redirects(): void
    {
        $project = Project::factory()->create();
        $client = Client::factory()->create();
        $user = User::factory()->create();
        $project_type = ProjectType::factory()->create();
        $title = fake()->sentence(4);
        $won_date = Carbon::parse(fake()->date());
        $start_date = Carbon::parse(fake()->date());
        $total_payment = fake()->randomFloat(/** decimal_attributes **/);
        $duration = fake()->numberBetween(-10000, 10000);
        $duration_unit = fake()->word();
        $payment_cycle = fake()->word();
        $status = fake()->word();

        $response = $this->put(route('projects.update', $project), [
            'client_id' => $client->id,
            'user_id' => $user->id,
            'project_type_id' => $project_type->id,
            'title' => $title,
            'won_date' => $won_date->toDateString(),
            'start_date' => $start_date->toDateString(),
            'total_payment' => $total_payment,
            'duration' => $duration,
            'duration_unit' => $duration_unit,
            'payment_cycle' => $payment_cycle,
            'status' => $status,
        ]);

        $project->refresh();

        $response->assertRedirect(route('projects.index'));
        $response->assertSessionHas('project.id', $project->id);

        $this->assertEquals($client->id, $project->client_id);
        $this->assertEquals($user->id, $project->user_id);
        $this->assertEquals($project_type->id, $project->project_type_id);
        $this->assertEquals($title, $project->title);
        $this->assertEquals($won_date, $project->won_date);
        $this->assertEquals($start_date, $project->start_date);
        $this->assertEquals($total_payment, $project->total_payment);
        $this->assertEquals($duration, $project->duration);
        $this->assertEquals($duration_unit, $project->duration_unit);
        $this->assertEquals($payment_cycle, $project->payment_cycle);
        $this->assertEquals($status, $project->status);
    }


    #[Test]
    public function destroy_deletes_and_redirects(): void
    {
        $project = Project::factory()->create();

        $response = $this->delete(route('projects.destroy', $project));

        $response->assertRedirect(route('projects.index'));

        $this->assertModelMissing($project);
    }
}
