<?php

namespace App\Filament\Resources\ClientResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class IncentivesRelationManager extends RelationManager
{
    protected static string $relationship = 'incentives';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->default(fn() => auth()->id())
                            ->disabled(fn() => auth()->user()->hasRole('bde_team'))
                            ->required()
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $projectId = $get('project_id');
                                $milestoneId = $get('milestone_id');
                                $userId = $state;
                                if (!$projectId || !$milestoneId || !$userId) {
                                    $set('amount', '');
                                    return;
                                }
                                $project = \App\Models\Project::find($projectId);
                                $milestone = \App\Models\Milestone::find($milestoneId);
                                $user = \App\Models\User::find($userId);
                                if (!$project || !$milestone || !$user) {
                                    $set('amount', '');
                                    return;
                                }
                                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                                    ->where('model_type', 'App\\Models\\User')
                                    ->where('model_id', $user->id)
                                    ->value('role_id');
                                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                                    ->where('role_id', $roleId)
                                    ->where('duration_percentage', '<=', $milestone->percentage)
                                    ->orderByDesc('duration_percentage')
                                    ->first();
                                if (!$rule) {
                                    $set('amount', 0);
                                    return;
                                }
                                $amount = 0;
                                $projectCurrency = $project->currency;
                                $ruleCurrency = $rule->currency;
                                $convertedAmount = \App\Services\CurrencyConverter::convert($projectCurrency, $ruleCurrency, $project->total_payment ?? 0);
                                switch ($rule->calculation_type) {
                                    case 'percentage':
                                    case 'Percentage of Contract Value':
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                        break;
                                    case 'fixed':
                                    case 'Fixed Amount':
                                        $amount = $rule->fixed_amount;
                                        break;
                                    case 'hybrid':
                                    case 'Hybrid (Percentage + Fixed)':
                                        $amount = ($convertedAmount * ($rule->percentage / 100)) + $rule->fixed_amount;
                                        break;
                                    case 'time_based':
                                    case 'Time-based (Monthly)':
                                        $months = max(1, $project->duration ?? 1);
                                        $amount = $rule->fixed_amount * $months;
                                        break;
                                    default:
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                }
                                $set('amount', round($amount, 2));
                            }),
                        Forms\Components\Select::make('project_id')
                            ->relationship('project', 'title', function (Builder $query) use ($form) {
                                // Only show projects for this client
                                $clientId = $form->getLivewire()->ownerRecord->id ?? null;
                                if ($clientId) {
                                    $query->where('client_id', $clientId);
                                }
                                return $query;
                            })
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $set('milestone_id', null);
                                $userId = $get('user_id');
                                $milestoneId = $get('milestone_id');
                                $projectId = $state;
                                if (!$projectId || !$milestoneId || !$userId) {
                                    $set('amount', '');
                                    return;
                                }
                                $project = \App\Models\Project::find($projectId);
                                $milestone = \App\Models\Milestone::find($milestoneId);
                                $user = \App\Models\User::find($userId);
                                if (!$project || !$milestone || !$user) {
                                    $set('amount', '');
                                    return;
                                }
                                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                                    ->where('model_type', 'App\\Models\\User')
                                    ->where('model_id', $user->id)
                                    ->value('role_id');
                                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                                    ->where('role_id', $roleId)
                                    ->where('duration_percentage', '<=', $milestone->percentage)
                                    ->orderByDesc('duration_percentage')
                                    ->first();
                                if (!$rule) {
                                    $set('amount', 0);
                                    return;
                                }
                                $amount = 0;
                                $projectCurrency = $project->currency;
                                $ruleCurrency = $rule->currency;
                                $convertedAmount = \App\Services\CurrencyConverter::convert($projectCurrency, $ruleCurrency, $project->total_payment ?? 0);
                                switch ($rule->calculation_type) {
                                    case 'percentage':
                                    case 'Percentage of Contract Value':
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                        break;
                                    case 'fixed':
                                    case 'Fixed Amount':
                                        $amount = $rule->fixed_amount;
                                        break;
                                    case 'hybrid':
                                    case 'Hybrid (Percentage + Fixed)':
                                        $amount = ($convertedAmount * ($rule->percentage / 100)) + $rule->fixed_amount;
                                        break;
                                    case 'time_based':
                                    case 'Time-based (Monthly)':
                                        $months = max(1, $project->duration ?? 1);
                                        $amount = $rule->fixed_amount * $months;
                                        break;
                                    default:
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                }
                                $set('amount', round($amount, 2));
                            }),
                        Forms\Components\Select::make('milestone_id')
                            ->label('Milestone')
                            ->options(function (callable $get) {
                                $projectId = $get('project_id');
                                if (!$projectId) {
                                    return [];
                                }
                                $paidMilestoneIds = \App\Models\Payment::where('project_id', $projectId)
                                    ->where('status', 'paid')
                                    ->pluck('milestone_id')
                                    ->filter(fn($id) => !is_null($id) && $id !== '')
                                    ->unique()
                                    ->values()
                                    ->toArray();
                                if (empty($paidMilestoneIds)) {
                                    return [];
                                }
                                return \App\Models\Milestone::where('project_id', $projectId)
                                    ->whereIn('id', $paidMilestoneIds)
                                    ->pluck('title', 'id')
                                    ->toArray();
                            })
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $projectId = $get('project_id');
                                $milestoneId = $state;
                                $userId = $get('user_id');
                                if (!$projectId || !$milestoneId || !$userId) {
                                    $set('amount', '');
                                    return;
                                }
                                $project = \App\Models\Project::find($projectId);
                                $milestone = \App\Models\Milestone::find($milestoneId);
                                $user = \App\Models\User::find($userId);
                                if (!$project || !$milestone || !$user) {
                                    $set('amount', '');
                                    return;
                                }
                                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                                    ->where('model_type', 'App\\Models\\User')
                                    ->where('model_id', $user->id)
                                    ->value('role_id');
                                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                                    ->where('role_id', $roleId)
                                    ->where('duration_percentage', '<=', $milestone->percentage)
                                    ->orderByDesc('duration_percentage')
                                    ->first();
                                if (!$rule) {
                                    $set('amount', 0);
                                    return;
                                }
                                $amount = 0;
                                $projectCurrency = $project->currency;
                                $ruleCurrency = $rule->currency;
                                $convertedAmount = \App\Services\CurrencyConverter::convert($projectCurrency, $ruleCurrency, $project->total_payment ?? 0);
                                switch ($rule->calculation_type) {
                                    case 'percentage':
                                    case 'Percentage of Contract Value':
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                        break;
                                    case 'fixed':
                                    case 'Fixed Amount':
                                        $amount = $rule->fixed_amount;
                                        break;
                                    case 'hybrid':
                                    case 'Hybrid (Percentage + Fixed)':
                                        $amount = ($convertedAmount * ($rule->percentage / 100)) + $rule->fixed_amount;
                                        break;
                                    case 'time_based':
                                    case 'Time-based (Monthly)':
                                        $months = max(1, $project->duration ?? 1);
                                        $amount = $rule->fixed_amount * $months;
                                        break;
                                    default:
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                }
                                $set('amount', round($amount, 2));
                            }),
                        Forms\Components\TextInput::make('amount')
                            ->required()
                            ->numeric()
                            ->readOnly()
                            ->prefix(function (callable $get) {
                                $projectId = $get('project_id');
                                $milestoneId = $get('milestone_id');
                                $userId = $get('user_id');
                                if (!$projectId || !$milestoneId || !$userId) {
                                    return '';
                                }
                                $project = \App\Models\Project::find($projectId);
                                $milestone = \App\Models\Milestone::find($milestoneId);
                                $user = \App\Models\User::find($userId);
                                if (!$project || !$milestone || !$user) {
                                    return '';
                                }
                                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                                    ->where('model_type', 'App\\Models\\User')
                                    ->where('model_id', $user->id)
                                    ->value('role_id');
                                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                                    ->where('role_id', $roleId)
                                    ->where('duration_percentage', '<=', $milestone->percentage)
                                    ->orderByDesc('duration_percentage')
                                    ->first();
                                if (!$rule) {
                                    return '';
                                }
                                return match ($rule->currency) {
                                    'INR' => '₹',
                                    'USD' => '$',
                                    'EUR' => '€',
                                    'GBP' => '£',
                                    default => $rule->currency,
                                };
                            })
                            ->reactive(),
                        Forms\Components\Hidden::make('incentive_rule_id')
                            ->default(function (callable $get) {
                                $projectId = $get('project_id');
                                $milestoneId = $get('milestone_id');
                                $userId = $get('user_id');
                                if (!$projectId || !$milestoneId || !$userId) {
                                    return null;
                                }
                                $project = \App\Models\Project::find($projectId);
                                $milestone = \App\Models\Milestone::find($milestoneId);
                                $user = \App\Models\User::find($userId);
                                if (!$project || !$milestone || !$user) {
                                    return null;
                                }
                                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                                    ->where('model_type', 'App\\Models\\User')
                                    ->where('model_id', $user->id)
                                    ->value('role_id');
                                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                                    ->where('role_id', $roleId)
                                    ->where('duration_percentage', '<=', $milestone->percentage)
                                    ->orderByDesc('duration_percentage')
                                    ->first();
                                return $rule?->id;
                            })
                            ->reactive(),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\DatePicker::make('calculation_date')
                                    ->required()
                                    ->default(now())
                                    ->label('Calculation Date'),
                                Forms\Components\DatePicker::make('payment_date')
                                    ->label('Payment Date'),
                                Forms\Components\Select::make('status')
                                    ->options([
                                        'pending' => 'Pending',
                                        'approved' => 'Approved',
                                        'paid' => 'Paid',
                                        'rejected' => 'Rejected',
                                    ])
                                    ->default('pending')
                                    ->required()
                                    ->label('Status'),
                            ]),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\Select::make('approved_by')
                            ->relationship('approver', 'name')
                            ->searchable()
                            ->preload()
                            ->visible(fn() => !auth()->user()->hasRole('bde_team')),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('BDE')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('project.title')
                    ->label('Project')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->formatStateUsing(function ($state, $record) {
                        $currency = $record->incentiveRule?->currency ?? $record->currency ?? 'INR';
                        $currencySymbol = match ($currency) {
                            'INR' => '₹',
                            'USD' => '$',
                            'EUR' => '€',
                            'GBP' => '£',
                            default => '',
                        };
                        return $currencySymbol . number_format($state, 2);
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('calculation_date')
                    ->label('Calculation date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Payment date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'gray',
                        'approved' => 'success',
                        'paid' => 'info',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('approver.name')
                    ->label('Approved By')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function mutateFormDataBeforeCreate(array $data): array
    {
        \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeCreate input', $data);
        if (empty($data['incentive_rule_id'])) {
            $project = \App\Models\Project::find($data['project_id'] ?? null);
            $milestone = \App\Models\Milestone::find($data['milestone_id'] ?? null);
            $user = \App\Models\User::find($data['user_id'] ?? null);
            \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeCreate lookup', [
                'project_id' => $data['project_id'] ?? null,
                'milestone_id' => $data['milestone_id'] ?? null,
                'user_id' => $data['user_id'] ?? null,
                'project' => $project?->id,
                'milestone' => $milestone?->id,
                'user' => $user?->id,
            ]);
            if ($project && $milestone && $user) {
                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                    ->where('model_type', 'App\\Models\\User')
                    ->where('model_id', $user->id)
                    ->value('role_id');
                \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeCreate roleId', ['role_id' => $roleId]);
                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                    ->where('role_id', $roleId)
                    ->where('duration_percentage', '<=', $milestone->percentage)
                    ->orderByDesc('duration_percentage')
                    ->first();
                \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeCreate rule', ['rule_id' => $rule?->id]);
                if ($rule) {
                    $data['incentive_rule_id'] = $rule->id;
                }
            }
        }
        \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeCreate output', $data);
        return $data;
    }

    public static function mutateFormDataBeforeUpdate(array $data): array
    {
        \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeUpdate input', $data);
        if (empty($data['incentive_rule_id'])) {
            $project = \App\Models\Project::find($data['project_id'] ?? null);
            $milestone = \App\Models\Milestone::find($data['milestone_id'] ?? null);
            $user = \App\Models\User::find($data['user_id'] ?? null);
            \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeUpdate lookup', [
                'project_id' => $data['project_id'] ?? null,
                'milestone_id' => $data['milestone_id'] ?? null,
                'user_id' => $data['user_id'] ?? null,
                'project' => $project?->id,
                'milestone' => $milestone?->id,
                'user' => $user?->id,
            ]);
            if ($project && $milestone && $user) {
                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                    ->where('model_type', 'App\\Models\\User')
                    ->where('model_id', $user->id)
                    ->value('role_id');
                \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeUpdate roleId', ['role_id' => $roleId]);
                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                    ->where('role_id', $roleId)
                    ->where('duration_percentage', '<=', $milestone->percentage)
                    ->orderByDesc('duration_percentage')
                    ->first();
                \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeUpdate rule', ['rule_id' => $rule?->id]);
                if ($rule) {
                    $data['incentive_rule_id'] = $rule->id;
                }
            }
        }
        \Log::info('[INCENTIVE_FORM] mutateFormDataBeforeUpdate output', $data);
        return $data;
    }

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        \Log::info('[INCENTIVE_FORM] handleRecordCreation input', $data);
        if (empty($data['incentive_rule_id'])) {
            $project = \App\Models\Project::find($data['project_id'] ?? null);
            $milestone = \App\Models\Milestone::find($data['milestone_id'] ?? null);
            $user = \App\Models\User::find($data['user_id'] ?? null);
            \Log::info('[INCENTIVE_FORM] handleRecordCreation lookup', [
                'project_id' => $data['project_id'] ?? null,
                'milestone_id' => $data['milestone_id'] ?? null,
                'user_id' => $data['user_id'] ?? null,
                'project' => $project?->id,
                'milestone' => $milestone?->id,
                'user' => $user?->id,
            ]);
            if ($project && $milestone && $user) {
                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                    ->where('model_type', 'App\\Models\\User')
                    ->where('model_id', $user->id)
                    ->value('role_id');
                \Log::info('[INCENTIVE_FORM] handleRecordCreation roleId', ['role_id' => $roleId]);
                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                    ->where('role_id', $roleId)
                    ->where('duration_percentage', '<=', $milestone->percentage)
                    ->orderByDesc('duration_percentage')
                    ->first();
                \Log::info('[INCENTIVE_FORM] handleRecordCreation rule', ['rule_id' => $rule?->id]);
                if ($rule) {
                    $data['incentive_rule_id'] = $rule->id;
                }
            }
        }
        \Log::info('[INCENTIVE_FORM] handleRecordCreation output', $data);
        return parent::handleRecordCreation($data);
    }

    protected function handleRecordUpdate(\Illuminate\Database\Eloquent\Model $record, array $data): \Illuminate\Database\Eloquent\Model
    {
        \Log::info('[INCENTIVE_FORM] handleRecordUpdate input', $data);
        if (empty($data['incentive_rule_id'])) {
            $project = \App\Models\Project::find($data['project_id'] ?? null);
            $milestone = \App\Models\Milestone::find($data['milestone_id'] ?? null);
            $user = \App\Models\User::find($data['user_id'] ?? null);
            \Log::info('[INCENTIVE_FORM] handleRecordUpdate lookup', [
                'project_id' => $data['project_id'] ?? null,
                'milestone_id' => $data['milestone_id'] ?? null,
                'user_id' => $data['user_id'] ?? null,
                'project' => $project?->id,
                'milestone' => $milestone?->id,
                'user' => $user?->id,
            ]);
            if ($project && $milestone && $user) {
                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                    ->where('model_type', 'App\\Models\\User')
                    ->where('model_id', $user->id)
                    ->value('role_id');
                \Log::info('[INCENTIVE_FORM] handleRecordUpdate roleId', ['role_id' => $roleId]);
                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                    ->where('role_id', $roleId)
                    ->where('duration_percentage', '<=', $milestone->percentage)
                    ->orderByDesc('duration_percentage')
                    ->first();
                \Log::info('[INCENTIVE_FORM] handleRecordUpdate rule', ['rule_id' => $rule?->id]);
                if ($rule) {
                    $data['incentive_rule_id'] = $rule->id;
                }
            }
        }
        \Log::info('[INCENTIVE_FORM] handleRecordUpdate output', $data);
        return parent::handleRecordUpdate($record, $data);
    }
}
