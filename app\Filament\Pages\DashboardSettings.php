<?php

namespace App\Filament\Pages;

use App\Models\DashboardConfig;
use App\Models\DashboardWidget;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DashboardSettings extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Dashboard Settings';

    protected static ?string $navigationGroup = 'Settings';

    protected static ?int $navigationSort = 10;

    protected static string $view = 'filament.pages.dashboard-settings';

    public static function canAccess(): bool
    {
        // Only allow users with specific roles to access the dashboard settings
        // Use hasRole instead of hasAnyRole for compatibility
        // @phpstan-ignore-next-line
        return Auth::check() && Auth::user() && (
            Auth::user()->hasRole('super_admin') ||
            Auth::user()->hasRole('admin') ||
            Auth::user()->hasRole('manager')
        );
    }

    public ?array $data = [];
    public $widgets = [];

    public function mount(): void
    {
        $userId = Filament::auth()->id();
        $widgets = DashboardWidget::all();

        // Initialize both $data['widgets'] and $widgets for Livewire entanglement
        $this->data['widgets'] = [];
        $this->widgets = [];

        foreach ($widgets as $widget) {
            $config = DashboardConfig::where('user_id', $userId)
                ->where('widget_id', $widget->id)
                ->first();

            $widgetArr = [
                'enabled' => $config ? $config->is_enabled : true,
                'position' => $config ? $config->position : $widget->id,
                'width' => $config ? $config->width : $widget->default_width,
                'height' => $config ? $config->height : $widget->default_height,
            ];
            $this->data['widgets'][$widget->id] = $widgetArr;
            $this->widgets[$widget->id] = $widgetArr;
        }
    }

    public function updatedWidgets($value, $key)
    {
        // Sync $widgets changes to $data['widgets'] for saving
        $this->data['widgets'] = $this->widgets;
        Log::info('[DASHBOARD_SETTINGS] updatedWidgets', ['key' => $key, 'value' => $value]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->statePath('widgets')
            ->schema([
                Section::make('Dashboard Widgets')
                    ->description('Configure which widgets appear on your dashboard and their order.')
                    ->schema([
                        Grid::make()
                            ->schema(function () {
                                $widgets = DashboardWidget::orderBy('name')->get();
                                $schema = [];

                                foreach ($widgets as $widget) {
                                    $schema[] = Section::make($widget->name)
                                        ->key("widget-section-{$widget->id}")
                                        ->description($widget->description)
                                        ->schema([
                                            Toggle::make("{$widget->id}.enabled")
                                                ->label('Enabled')
                                                ->default(true),
                                            Select::make("{$widget->id}.width")
                                                ->label('Width')
                                                ->options([
                                                    3 => 'Small (25%)',
                                                    4 => 'Medium (33%)',
                                                    6 => 'Half Width (50%)',
                                                    8 => 'Three-Quarter Width (75%)',
                                                    12 => 'Full Width (100%)',
                                                ])
                                                ->default($widget->default_width)
                                                ->reactive()
                                                ->afterStateUpdated(fn ($state) => Log::info('[DASHBOARD_SETTINGS] Width changed', ['widget_id' => $widget->id, 'value' => $state])),
                                            Select::make("{$widget->id}.height")
                                                ->label('Height')
                                                ->options([
                                                    1 => 'Small',
                                                    2 => 'Medium',
                                                    3 => 'Large',
                                                ])
                                                ->default($widget->default_height)
                                                ->reactive()
                                                ->live(onBlur: true)
                                                ->afterStateUpdated(fn ($state) => Log::info('[DASHBOARD_SETTINGS] Height changed', ['widget_id' => $widget->id, 'value' => $state]))
                                                ->live(),
                                        ])
                                        ->columns(3);
                                }

                                return $schema;
                            }),
                        // Minimal test: add a simple Select field outside the loop to test reactivity
                        Select::make('test_select')
                            ->label('Test Select')
                            ->options([
                                1 => 'Option 1',
                                2 => 'Option 2',
                                3 => 'Option 3',
                            ])
                            ->reactive()
                            ->afterStateUpdated(fn ($state) => Log::info('[DASHBOARD_SETTINGS] Test Select changed', ['value' => $state]))
                            ->live(),
                    ]),
            ]);
    }

    public function save(): void
    {
        // Use $this->widgets for saving
        Log::info('[DASHBOARD_SETTINGS] RAW $this->widgets before save', $this->widgets);
        $userId = Filament::auth()->id();
        $widgetData = $this->widgets ?? [];

        Log::info('[DASHBOARD_SETTINGS] Saving dashboard config', [
            'user_id' => $userId,
            'widget_data' => $widgetData,
        ]);

        foreach ($widgetData as $widgetId => $config) {
            // Only process numeric widget IDs that correspond to real widgets
            if (!is_numeric($widgetId) || !DashboardWidget::find($widgetId)) {
                continue;
            }
            Log::info('[DASHBOARD_SETTINGS] Saving widget', [
                'widget_id' => $widgetId,
                'config' => $config,
            ]);
            $saved = DashboardConfig::updateOrCreate(
                [
                    'user_id' => $userId,
                    'widget_id' => $widgetId,
                ],
                [
                    'is_enabled' => $config['enabled'] ?? true,
                    'position' => $config['position'] ?? 0,
                    'width' => $config['width'] ?? null,
                    'height' => $config['height'] ?? null,
                ]
            );
            Log::info('[DASHBOARD_SETTINGS] Saved config', [
                'user_id' => $userId,
                'widget_id' => $widgetId,
                'config' => $saved->toArray(),
            ]);
        }

        Notification::make()
            ->title('Dashboard settings saved successfully')
            ->success()
            ->send();
    }
}
