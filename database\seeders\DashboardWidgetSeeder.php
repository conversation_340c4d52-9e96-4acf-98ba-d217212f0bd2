<?php

namespace Database\Seeders;

use App\Models\DashboardWidget;
use Illuminate\Database\Seeder;

class DashboardWidgetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define default widgets
        $widgets = [
            [
                'name' => 'Total Projects',
                'slug' => 'total-projects',
                'description' => 'Shows the total number of projects',
                'component' => 'TotalProjectsWidget',
                'default_width' => 3,
                'default_height' => 1,
                'default_settings' => json_encode(['icon' => 'heroicon-o-briefcase']),
                'is_available' => true,
            ],
            [
                'name' => 'Total Clients',
                'slug' => 'total-clients',
                'description' => 'Shows the total number of clients',
                'component' => 'TotalClientsWidget',
                'default_width' => 3,
                'default_height' => 1,
                'default_settings' => json_encode(['icon' => 'heroicon-o-users']),
                'is_available' => true,
            ],
            [
                'name' => 'Pending Payments',
                'slug' => 'pending-payments',
                'description' => 'Shows the total amount of pending payments',
                'component' => 'PendingPaymentsWidget',
                'default_width' => 3,
                'default_height' => 1,
                'default_settings' => json_encode(['icon' => 'heroicon-o-credit-card']),
                'is_available' => true,
            ],
            [
                'name' => 'Approved Incentives',
                'slug' => 'approved-incentives',
                'description' => 'Shows the total amount of approved incentives',
                'component' => 'ApprovedIncentivesWidget',
                'default_width' => 3,
                'default_height' => 1,
                'default_settings' => json_encode(['icon' => 'heroicon-o-banknotes']),
                'is_available' => true,
            ],
            [
                'name' => 'Recent Projects',
                'slug' => 'recent-projects',
                'description' => 'Shows a list of recent projects',
                'component' => 'RecentProjectsWidget',
                'default_width' => 12, // Full width
                'default_height' => 2,
                'default_settings' => json_encode(['limit' => 5]),
                'is_available' => true,
            ],
            [
                'name' => 'Upcoming Payments',
                'slug' => 'upcoming-payments',
                'description' => 'Shows a list of upcoming payments',
                'component' => 'UpcomingPaymentsWidget',
                'default_width' => 12, // Full width
                'default_height' => 2,
                'default_settings' => json_encode(['limit' => 5]),
                'is_available' => true,
            ],
            [
                'name' => 'Project Status',
                'slug' => 'project-status',
                'description' => 'Shows a chart of project statuses',
                'component' => 'ProjectStatusWidget',
                'default_width' => 6, // Half width
                'default_height' => 2,
                'default_settings' => json_encode(['chart_type' => 'pie']),
                'is_available' => true,
            ],
            [
                'name' => 'Monthly Revenue',
                'slug' => 'monthly-revenue',
                'description' => 'Shows a chart of monthly revenue',
                'component' => 'MonthlyRevenueWidget',
                'default_width' => 6, // Half width
                'default_height' => 2,
                'default_settings' => json_encode(['chart_type' => 'bar', 'months' => 6]),
                'is_available' => true,
            ],
        ];

        // Insert widgets
        foreach ($widgets as $widget) {
            DashboardWidget::updateOrCreate(
                ['slug' => $widget['slug']],
                $widget
            );
        }
    }
}
