<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIncentiveRuleIdToIncentivesTable extends Migration
{
    public function up(): void
    {
        Schema::table('incentives', function (Blueprint $table) {
            $table->unsignedBigInteger('incentive_rule_id')->nullable()->after('milestone_id');
            $table->foreign('incentive_rule_id')->references('id')->on('incentive_rules')->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('incentives', function (Blueprint $table) {
            $table->dropForeign(['incentive_rule_id']);
            $table->dropColumn('incentive_rule_id');
        });
    }
}
