<?php

namespace App\Filament\Resources\ProjectTypeResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class IncentiveRulesRelationManager extends RelationManager
{
    protected static string $relationship = 'incentiveRules';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('role_id')
                    ->relationship('role', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\TextInput::make('percentage')
                    ->required()
                    ->numeric()
                    ->suffix('%')
                    ->maxValue(100),
                Forms\Components\TextInput::make('duration_percentage')
                    ->required()
                    ->numeric()
                    ->suffix('%')
                    ->maxValue(100)
                    ->label('Duration Percentage (% of contract length)'),
                Forms\Components\TextInput::make('fixed_amount')
                    ->numeric()
                    ->prefix('₹'),
                Forms\Components\Select::make('currency')
                    ->options([
                        'INR' => 'Indian Rupee (₹)',
                        'USD' => 'US Dollar ($)',
                        'EUR' => 'Euro (€)',
                        'GBP' => 'British Pound (£)',
                    ])
                    ->default('INR')
                    ->required(),
                Forms\Components\Select::make('calculation_type')
                    ->options([
                        'percentage' => 'Percentage of Contract Value',
                        'fixed' => 'Fixed Amount',
                        'hybrid' => 'Hybrid (Percentage + Fixed)',
                        'time_based' => 'Time-based (Monthly)',
                    ])
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('role.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('percentage')
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('duration_percentage')
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('fixed_amount')
                    ->money('INR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('calculation_type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'percentage' => 'success',
                        'fixed' => 'info',
                        'hybrid' => 'warning',
                        'time_based' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
