<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Permission\Traits\HasRoles;
use Jeffgreco13\FilamentBreezy\Traits\TwoFactorAuthenticatable;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'email_verified_at',
        'password',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'remember_token',
        // Always store avatar_url as /storage/filename.jpg
        'avatar_url',
        'theme',
        'theme_color',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'two_factor_confirmed_at' => 'datetime',
    ];

    // Using Spatie Permission's HasRoles trait for role management

    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    // public function notifications(): HasMany
    // {
    //     return $this->hasMany(Notification::class);
    // }

    public function appNotifications(): HasMany
    {
        return $this->hasMany(AppNotification::class);
    }

    public function notificationPreferences(): HasMany
    {
        return $this->hasMany(NotificationPreference::class);
    }

    public function incentives(): HasMany
    {
        return $this->hasMany(Incentive::class);
    }

    public function setAvatarUrlAttribute($value)
    {
        $prefixes = ['/storage/', 'http://', 'https://'];
        $hasPrefix = false;
        foreach ($prefixes as $prefix) {
            if ($value && str_starts_with($value, $prefix)) {
                $hasPrefix = true;
                break;
            }
        }
        if ($value && !$hasPrefix) {
            $this->attributes['avatar_url'] = '/storage/' . ltrim($value, '/');
        } else {
            $this->attributes['avatar_url'] = $value;
        }
    }
}
