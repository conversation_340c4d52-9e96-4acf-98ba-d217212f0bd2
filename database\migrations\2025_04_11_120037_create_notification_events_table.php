<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_events', function (Blueprint $table) {
            $table->id();
            $table->string('name');                  // Name of the event (e.g., 'project_created')
            $table->string('display_name');          // Display name (e.g., 'Project Created')
            $table->string('module');                // Module this event belongs to (e.g., 'projects')
            $table->text('description')->nullable(); // Description of when this notification is triggered
            $table->boolean('is_active')->default(true); // Whether this event is active
            $table->boolean('is_hierarchical')->default(false); // Whether this notification flows through hierarchy
            $table->json('data_schema')->nullable(); // Schema for additional data this notification requires
            $table->timestamps();

            // Unique constraint on name
            $table->unique('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_events');
    }
};
