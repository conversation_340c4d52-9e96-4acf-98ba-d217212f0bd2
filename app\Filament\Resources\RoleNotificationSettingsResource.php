<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoleNotificationSettingsResource\Pages;

use App\Models\RoleNotificationSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Models\NotificationEvent;
use App\Models\Role;

class RoleNotificationSettingsResource extends Resource
{
    protected static ?string $model = RoleNotificationSettings::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell-alert';
    protected static ?string $navigationGroup = 'Notifications';
    protected static ?int $navigationSort = 2;

    protected static ?string $modelLabel = 'Role Notification Settings';
    protected static ?string $pluralModelLabel = 'Role Notification Settings';

    public static function form(Form $form): Form
    {
        // Get all notification events
        $events = NotificationEvent::orderBy('module')->orderBy('display_name')->get();

        // Group events by module
        $eventsByModule = $events->groupBy('module');

        // Create form schema
        $schema = [
            Forms\Components\Select::make('role_id')
                ->label('Role')
                ->options(Role::pluck('name', 'id'))
                ->required()
                ->searchable()
                ->unique(ignoreRecord: true)
                ->columnSpanFull(),
        ];

        // Add a section for each module
        foreach ($eventsByModule as $module => $moduleEvents) {
            $moduleFields = [];

            // Add a card for each event in this module
            foreach ($moduleEvents as $event) {
                $moduleFields[] = Forms\Components\Section::make($event->display_name)
                    ->description($event->description)
                    ->schema([
                        Forms\Components\Toggle::make("notification_settings.{$event->id}.is_enabled")
                            ->label('Enable Notifications')
                            ->helperText('Enable notifications for this event')
                            ->default(true),

                        Forms\Components\Toggle::make("notification_settings.{$event->id}.email")
                            ->label('Email Notifications')
                            ->helperText('Send email notifications')
                            ->default(true),

                        Forms\Components\Toggle::make("notification_settings.{$event->id}.in_app")
                            ->label('In-App Notifications')
                            ->helperText('Send in-app notifications')
                            ->default(true),
                    ])
                    ->columns(3);
            }

            // Add the module section to the schema
            $schema[] = Forms\Components\Section::make(ucfirst($module))
                ->schema($moduleFields)
                ->collapsible()
                ->columnSpanFull();
        }

        return $form->schema($schema);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('role.name')
                    ->label('Role')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->relationship('role', 'name'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoleNotificationSettings::route('/'),
            'create' => Pages\CreateRoleNotificationSettings::route('/create'),
            'edit' => Pages\EditRoleNotificationSettings::route('/{record}/edit'),
        ];
    }
}
