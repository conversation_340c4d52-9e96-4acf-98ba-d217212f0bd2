<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Models\Incentive;
use App\Models\Milestone;
use App\Models\Project;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class VerifyRoleFiltering extends Command
{
    protected $signature = 'verify:role-filtering {email}';
    protected $description = 'Verify role-based filtering for a user';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Verifying role-based filtering for user with email {$email}...");
        
        // Find the user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found");
            return 1;
        }
        
        // Get user roles
        $roles = $user->roles()->pluck('name')->toArray();
        $this->info("User roles: " . implode(', ', $roles));
        
        // Check if user has BDE role
        $isBde = $user->hasRole('bde');
        $this->info("Is BDE: " . ($isBde ? 'Yes' : 'No'));
        
        if ($isBde) {
            // Get projects assigned to this user
            $userProjects = Project::where('user_id', $user->id)->get();
            $this->info("Projects assigned to this user: " . $userProjects->count());
            
            // Get all projects
            $allProjects = Project::all();
            $this->info("Total projects in system: " . $allProjects->count());
            
            // Get clients associated with this user's projects
            $userClients = Client::whereHas('projects', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->get();
            $this->info("Clients associated with this user's projects: " . $userClients->count());
            
            // Get all clients
            $allClients = Client::all();
            $this->info("Total clients in system: " . $allClients->count());
            
            // Get milestones for this user's projects
            $userMilestones = Milestone::whereHas('project', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->get();
            $this->info("Milestones for this user's projects: " . $userMilestones->count());
            
            // Get all milestones
            $allMilestones = Milestone::all();
            $this->info("Total milestones in system: " . $allMilestones->count());
            
            // Get incentives for this user
            $userIncentives = Incentive::where('user_id', $user->id)->get();
            $this->info("Incentives for this user: " . $userIncentives->count());
            
            // Get all incentives
            $allIncentives = Incentive::all();
            $this->info("Total incentives in system: " . $allIncentives->count());
            
            // Verify database queries used by resources
            $this->info("\nVerifying database queries that would be used by resources:");
            
            // Project resource query
            $projectQuery = Project::query();
            if ($user->hasRole('bde')) {
                $projectQuery->where('user_id', $user->id);
            }
            $filteredProjects = $projectQuery->get();
            $this->info("Projects after filtering: " . $filteredProjects->count());
            
            // Client resource query
            $clientQuery = Client::query();
            if ($user->hasRole('bde')) {
                $clientQuery->whereHas('projects', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                });
            }
            $filteredClients = $clientQuery->get();
            $this->info("Clients after filtering: " . $filteredClients->count());
            
            // Milestone resource query
            $milestoneQuery = Milestone::query();
            if ($user->hasRole('bde')) {
                $milestoneQuery->whereHas('project', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                });
            }
            $filteredMilestones = $milestoneQuery->get();
            $this->info("Milestones after filtering: " . $filteredMilestones->count());
            
            // Incentive resource query
            $incentiveQuery = Incentive::query();
            if ($user->hasRole('bde')) {
                $incentiveQuery->where('user_id', $user->id);
            }
            $filteredIncentives = $incentiveQuery->get();
            $this->info("Incentives after filtering: " . $filteredIncentives->count());
        } else {
            $this->info("User does not have BDE role, so they should see all data.");
        }
        
        $this->info('Verification complete!');
        return 0;
    }
}
