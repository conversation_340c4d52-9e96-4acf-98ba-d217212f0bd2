<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('notification_role_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('role_id')->constrained('roles')->onDelete('cascade');
            $table->foreignId('notification_event_id')->constrained('notification_events')->onDelete('cascade');
            $table->boolean('email')->default(true);       // Whether to send email notifications
            $table->boolean('in_app')->default(true);      // Whether to send in-app notifications
            $table->boolean('is_enabled')->default(true);  // Whether this notification is enabled for this role
            $table->timestamps();

            // Unique constraint on role_id and notification_event_id
            $table->unique(['role_id', 'notification_event_id'], 'role_notification_event_unique');
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_role_preferences');
    }
};
