<?php

namespace App\Filament\Resources\IncentiveRuleResource\Pages;

use App\Filament\Resources\IncentiveRuleResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateIncentiveRule extends CreateRecord
{
    protected static string $resource = IncentiveRuleResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
