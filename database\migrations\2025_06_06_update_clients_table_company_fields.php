<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Remove the old 'name' field
            if (Schema::hasColumn('clients', 'name')) {
                $table->dropColumn('name');
            }
            // Rename 'email' to 'company_email'
            if (Schema::hasColumn('clients', 'email')) {
                $table->renameColumn('email', 'company_email');
            }
            // Rename 'contact_number' to 'company_number'
            if (Schema::hasColumn('clients', 'contact_number')) {
                $table->renameColumn('contact_number', 'company_number');
            }
        });
    }
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Add back 'name' as string nullable (cannot restore data)
            if (!Schema::hasColumn('clients', 'name')) {
                $table->string('name')->nullable();
            }
            // Rename 'company_email' back to 'email'
            if (Schema::hasColumn('clients', 'company_email')) {
                $table->renameColumn('company_email', 'email');
            }
            // Rename 'company_number' back to 'contact_number'
            if (Schema::hasColumn('clients', 'company_number')) {
                $table->renameColumn('company_number', 'contact_number');
            }
        });
    }
};
