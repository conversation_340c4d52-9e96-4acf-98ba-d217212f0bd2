<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NotificationEventResource\Pages;
use App\Filament\Resources\NotificationEventResource\RelationManagers;
use App\Models\NotificationEvent;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;


class NotificationEventResource extends Resource
{
    protected static ?string $model = NotificationEvent::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell-alert';
    protected static ?string $navigationGroup = 'Notifications';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Event Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->helperText('Unique identifier for this event (e.g., project_created)')
                            ->placeholder('project_created'),

                        Forms\Components\TextInput::make('display_name')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Human-readable name for this event')
                            ->placeholder('Project Created'),

                        Forms\Components\Select::make('module')
                            ->required()
                            ->options([
                                'projects' => 'Projects',
                                'clients' => 'Clients',
                                'payments' => 'Payments',
                                'milestones' => 'Milestones',
                                'incentives' => 'Incentives',
                                'users' => 'Users',
                                'system' => 'System',
                            ])
                            ->searchable(),

                        Forms\Components\Textarea::make('description')
                            ->nullable()
                            ->maxLength(65535)
                            ->helperText('Description of when this notification is triggered')
                            ->placeholder('Sent when a new project is created'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Event Configuration')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->helperText('Whether this event is active')
                            ->default(true),

                        Forms\Components\Toggle::make('is_hierarchical')
                            ->label('Hierarchical')
                            ->helperText('Whether this notification flows through hierarchy (Admin → Manager → Employee)')
                            ->default(false),

                        Forms\Components\KeyValue::make('data_schema')
                            ->nullable()
                            ->helperText('Define the schema for additional data this notification requires')
                            ->keyLabel('Field')
                            ->valueLabel('Description')
                            ->keyPlaceholder('project_id')
                            ->valuePlaceholder('ID of the project'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('display_name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('module')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'projects' => 'primary',
                        'clients' => 'success',
                        'payments' => 'warning',
                        'milestones' => 'info',
                        'incentives' => 'danger',
                        'users' => 'gray',
                        'system' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_hierarchical')
                    ->label('Hierarchical')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('module')
                    ->options([
                        'projects' => 'Projects',
                        'clients' => 'Clients',
                        'payments' => 'Payments',
                        'milestones' => 'Milestones',
                        'incentives' => 'Incentives',
                        'users' => 'Users',
                        'system' => 'System',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),

                Tables\Filters\TernaryFilter::make('is_hierarchical')
                    ->label('Hierarchical'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    // public static function getRelations(): array
    // {
    //     return [
    //         RelationManagers\RolePreferencesRelationManager::class,
    //     ];
    // }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotificationEvents::route('/'),
            'create' => Pages\CreateNotificationEvent::route('/create'),
            'edit' => Pages\EditNotificationEvent::route('/{record}/edit'),
        ];
    }
}
