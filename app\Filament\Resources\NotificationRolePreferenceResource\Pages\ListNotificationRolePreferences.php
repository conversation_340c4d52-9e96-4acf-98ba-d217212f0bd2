<?php

namespace App\Filament\Resources\NotificationRolePreferenceResource\Pages;

use App\Filament\Resources\NotificationRolePreferenceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNotificationRolePreferences extends ListRecords
{
    protected static string $resource = NotificationRolePreferenceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
