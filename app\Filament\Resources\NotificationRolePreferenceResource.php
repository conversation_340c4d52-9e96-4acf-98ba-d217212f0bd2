<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NotificationRolePreferenceResource\Pages;

use App\Models\NotificationRolePreference;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class NotificationRolePreferenceResource extends Resource
{
    protected static ?string $model = NotificationRolePreference::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationGroup = 'Notifications';
    protected static ?int $navigationSort = 2;

    protected static ?string $modelLabel = 'Role Notification Settings';
    protected static ?string $pluralModelLabel = 'Role Notification Settings';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Role Notification Settings')
                    ->schema([
                        Forms\Components\Select::make('role_id')
                            ->relationship('role', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('notification_event_id')
                            ->relationship('notificationEvent', 'display_name')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\Toggle::make('is_enabled')
                            ->label('Enabled')
                            ->helperText('Whether this notification is enabled for this role')
                            ->default(true),

                        Forms\Components\Toggle::make('email')
                            ->label('Email Notifications')
                            ->helperText('Send email notifications to users with this role')
                            ->default(true),

                        Forms\Components\Toggle::make('in_app')
                            ->label('In-App Notifications')
                            ->helperText('Send in-app notifications to users with this role')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('role.name')
                    ->label('Role')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('notificationEvent.display_name')
                    ->label('Notification Event')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('notificationEvent.module')
                    ->label('Module')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'projects' => 'primary',
                        'clients' => 'success',
                        'payments' => 'warning',
                        'milestones' => 'info',
                        'incentives' => 'danger',
                        'users' => 'gray',
                        'system' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_enabled')
                    ->label('Enabled')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('email')
                    ->label('Email')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('in_app')
                    ->label('In-App')
                    ->boolean()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->relationship('role', 'name'),

                Tables\Filters\SelectFilter::make('module')
                    ->label('Module')
                    ->relationship('notificationEvent', 'module')
                    ->options([
                        'projects' => 'Projects',
                        'clients' => 'Clients',
                        'payments' => 'Payments',
                        'milestones' => 'Milestones',
                        'incentives' => 'Incentives',
                        'users' => 'Users',
                        'system' => 'System',
                    ]),

                Tables\Filters\TernaryFilter::make('is_enabled')
                    ->label('Enabled'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotificationRolePreferences::route('/'),
            'create' => Pages\CreateNotificationRolePreference::route('/create'),
            'edit' => Pages\EditNotificationRolePreference::route('/{record}/edit'),
        ];
    }
}
