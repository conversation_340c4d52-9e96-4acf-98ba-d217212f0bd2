<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class IncentiveRuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get project types
        $projectTypes = \App\Models\ProjectType::all();

        // Create roles for BDE, Marketing, and Management if they don't exist
        $bdeRole = \App\Models\Role::firstOrCreate(['name' => 'bde_team', 'guard_name' => 'web']);
        $marketingRole = \App\Models\Role::firstOrCreate(['name' => 'marketing', 'guard_name' => 'web']);
        $managementRole = \App\Models\Role::firstOrCreate(['name' => 'management', 'guard_name' => 'web']);

        // Create incentive rules for each project type
        foreach ($projectTypes as $index => $projectType) {
            // BDE incentive rules
            \App\Models\IncentiveRule::create([
                'project_type_id' => $projectType->id,
                'role_id' => $bdeRole->id,
                'percentage' => 5 + ($index % 3), // 5-7% based on project type
                'duration_percentage' => 100, // 100% of contract length
                'fixed_amount' => null,
                'currency' => 'INR',
                'calculation_type' => 'percentage',
            ]);

            // Marketing incentive rules
            \App\Models\IncentiveRule::create([
                'project_type_id' => $projectType->id,
                'role_id' => $marketingRole->id,
                'percentage' => 2, // 2% for all project types
                'duration_percentage' => 100, // 100% of contract length
                'fixed_amount' => null,
                'currency' => 'INR',
                'calculation_type' => 'percentage',
            ]);

            // Management incentive rules
            \App\Models\IncentiveRule::create([
                'project_type_id' => $projectType->id,
                'role_id' => $managementRole->id,
                'percentage' => 1, // 1% for all project types
                'duration_percentage' => 100, // 100% of contract length
                'fixed_amount' => null,
                'currency' => 'INR',
                'calculation_type' => 'percentage',
            ]);
        }

        // Create some fixed amount incentive rules
        \App\Models\IncentiveRule::create([
            'project_type_id' => $projectTypes[0]->id, // Website Development
            'role_id' => $bdeRole->id,
            'percentage' => 0,
            'duration_percentage' => 100,
            'fixed_amount' => 10000, // ₹10,000 fixed bonus
            'currency' => 'INR',
            'calculation_type' => 'fixed',
        ]);

        // Create hybrid incentive rule
        \App\Models\IncentiveRule::create([
            'project_type_id' => $projectTypes[2]->id, // Mobile App
            'role_id' => $bdeRole->id,
            'percentage' => 3,
            'duration_percentage' => 100,
            'fixed_amount' => 5000, // ₹5,000 fixed bonus + 3%
            'currency' => 'INR',
            'calculation_type' => 'hybrid',
        ]);
    }
}
