{"__meta": {"id": "01JXC31CRSHZEJS46D2880NTHM", "datetime": "2025-06-10 04:54:36", "utime": **********.058899, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749531271.51141, "end": **********.058918, "duration": 4.547508001327515, "duration_str": "4.55s", "measures": [{"label": "Booting", "start": 1749531271.51141, "relative_start": 0, "end": **********.232788, "relative_end": **********.232788, "duration": 0.****************, "duration_str": "721ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.232806, "relative_start": 0.****************, "end": **********.058921, "relative_end": 3.0994415283203125e-06, "duration": 3.***************, "duration_str": "3.83s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.934614, "relative_start": 2.***************, "end": **********.940122, "relative_end": **********.940122, "duration": 0.005507946014404297, "duration_str": "5.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::99503e4f2086f10483464823e7fe02ed", "start": **********.112733, "relative_start": 2.***************, "end": **********.112733, "relative_end": **********.112733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.05361, "relative_start": 4.***************, "end": **********.055047, "relative_end": **********.055047, "duration": 0.0014369487762451172, "duration_str": "1.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "__components::99503e4f2086f10483464823e7fe02ed", "param_count": null, "params": [], "start": **********.112709, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/99503e4f2086f10483464823e7fe02ed.blade.php__components::99503e4f2086f10483464823e7fe02ed", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F99503e4f2086f10483464823e7fe02ed.blade.php&line=1", "ajax": false, "filename": "99503e4f2086f10483464823e7fe02ed.blade.php", "line": "?"}}]}, "queries": {"count": 26, "nb_statements": 24, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0581, "accumulated_duration_str": "58.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'l9J2SElbQzqP8xK93n4Bdsjh9GGB4Rgpe0DlqIYz' limit 1", "type": "query", "params": [], "bindings": ["l9J2SElbQzqP8xK93n4Bdsjh9GGB4Rgpe0DlqIYz"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.0294611, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 7.47}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:')", "type": "query", "params": [], "bindings": ["filament-excel:exports:"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0469, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.47, "width_percent": 1.566}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:', 'illuminate:cache:flexible:created:filament-excel:exports:')", "type": "query", "params": [], "bindings": ["filament-excel:exports:", "illuminate:cache:flexible:created:filament-excel:exports:"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.049241, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.036, "width_percent": 2.41}, {"sql": "select * from `cache` where `key` in ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5')", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}], "start": **********.200574, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.446, "width_percent": 5.731}, {"sql": "delete from `cache` where `key` in ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5', 'illuminate:cache:flexible:created:livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5", "illuminate:cache:flexible:created:livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 409}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 144}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 206}], "start": **********.2051592, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:409", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 409}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=409", "ajax": false, "filename": "DatabaseStore.php", "line": "409"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.177, "width_percent": 4.131}, {"sql": "select * from `cache` where `key` in ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer')", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 202}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 164}], "start": **********.2084732, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.308, "width_percent": 0.792}, {"sql": "delete from `cache` where `key` in ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer', 'illuminate:cache:flexible:created:livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer", "illuminate:cache:flexible:created:livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 409}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 144}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 202}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}], "start": **********.209666, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:409", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 409}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=409", "ajax": false, "filename": "DatabaseStore.php", "line": "409"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.1, "width_percent": 0.654}, {"sql": "insert ignore into `cache` (`key`, `value`, `expiration`) values ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer', 'i:1749531334;', 1749531334)", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer", "i:1749531334;", 1749531334], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 164}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}, {"index": 15, "namespace": null, "name": "vendor/danharrin/livewire-rate-limiting/src/WithRateLimiting.php", "file": "D:\\wamp64\\www\\smms\\vendor\\danharrin\\livewire-rate-limiting\\src\\WithRateLimiting.php", "line": 38}], "start": **********.2107391, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:211", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=211", "ajax": false, "filename": "DatabaseStore.php", "line": "211"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.754, "width_percent": 3.821}, {"sql": "select * from `cache` where `key` in ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5')", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 202}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 169}], "start": **********.213712, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.575, "width_percent": 0.568}, {"sql": "insert ignore into `cache` (`key`, `value`, `expiration`) values ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5', 'i:0;', 1749531334)", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5", "i:0;", 1749531334], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 169}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 168}], "start": **********.214709, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:211", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=211", "ajax": false, "filename": "DatabaseStore.php", "line": "211"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.143, "width_percent": 1.532}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}], "start": **********.220872, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:261", "source": {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=261", "ajax": false, "filename": "DatabaseStore.php", "line": "261"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.675, "width_percent": 0}, {"sql": "select * from `cache` where `key` = 'livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5' limit 1 for update", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}], "start": **********.22103, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:265", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=265", "ajax": false, "filename": "DatabaseStore.php", "line": "265"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.675, "width_percent": 1.91}, {"sql": "update `cache` set `value` = 'i:1;' where `key` = 'livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5'", "type": "query", "params": [], "bindings": ["i:1;", "livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 290}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}], "start": **********.222882, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:290", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=290", "ajax": false, "filename": "DatabaseStore.php", "line": "290"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.585, "width_percent": 0.706}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}], "start": **********.227187, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:261", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=261", "ajax": false, "filename": "DatabaseStore.php", "line": "261"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.291, "width_percent": 0}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.5529711, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "Login.php:32", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=32", "ajax": false, "filename": "Login.php", "line": "32"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.291, "width_percent": 6.558}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 32}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.561265, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "Login.php:32", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=32", "ajax": false, "filename": "Login.php", "line": "32"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.849, "width_percent": 3.597}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 414}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 411}, {"index": 20, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.567925, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:138", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=138", "ajax": false, "filename": "EloquentUserProvider.php", "line": "138"}, "connection": "local_kit_db", "explain": null, "start_percent": 41.446, "width_percent": 1.015}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 414}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 411}, {"index": 25, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 39}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.56979, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:138", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=138", "ajax": false, "filename": "EloquentUserProvider.php", "line": "138"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.461, "width_percent": 0.688}, {"sql": "delete from `sessions` where `id` = 'l9J2SElbQzqP8xK93n4Bdsjh9GGB4Rgpe0DlqIYz'", "type": "query", "params": [], "bindings": ["l9J2SElbQzqP8xK93n4Bdsjh9GGB4Rgpe0DlqIYz"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 608}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 578}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 549}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 422}], "start": 1749531275.41572, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:268", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=268", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "268"}, "connection": "local_kit_db", "explain": null, "start_percent": 43.15, "width_percent": 2.375}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (4) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPageShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPageShield.php", "line": 61}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Pages/Page.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\Page.php", "line": 82}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 137}], "start": 1749531275.4338, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.525, "width_percent": 4.286}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": 1749531275.560777, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.811, "width_percent": 0.929}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": 1749531275.5633929, "duration": 0.00904, "duration_str": "9.04ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.74, "width_percent": 15.559}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": 1749531275.5850441, "duration": 0.01102, "duration_str": "11.02ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.299, "width_percent": 18.967}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1749617675, 'spatie.permission.cache', 'a:3:{s:5:\\\"alias\\\";a:4:{s:1:\\\"a\\\";s:2:\\\"id\\\";s:1:\\\"b\\\";s:4:\\\"name\\\";s:1:\\\"c\\\";s:10:\\\"guard_name\\\";s:1:\\\"r\\\";s:5:\\\"roles\\\";}s:11:\\\"permissions\\\";a:217:{i:0;a:4:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:22:\\\"view_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:1;a:4:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:26:\\\"view_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:2;a:4:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:24:\\\"create_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:3;a:4:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:24:\\\"update_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:4;a:4:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:25:\\\"restore_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:5;a:4:{s:1:\\\"a\\\";i:6;s:1:\\\"b\\\";s:29:\\\"restore_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:6;a:4:{s:1:\\\"a\\\";i:7;s:1:\\\"b\\\";s:27:\\\"replicate_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:7;a:4:{s:1:\\\"a\\\";i:8;s:1:\\\"b\\\";s:25:\\\"reorder_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:8;a:4:{s:1:\\\"a\\\";i:9;s:1:\\\"b\\\";s:24:\\\"delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:9;a:4:{s:1:\\\"a\\\";i:10;s:1:\\\"b\\\";s:28:\\\"delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:10;a:4:{s:1:\\\"a\\\";i:11;s:1:\\\"b\\\";s:30:\\\"force_delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:11;a:4:{s:1:\\\"a\\\";i:12;s:1:\\\"b\\\";s:34:\\\"force_delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:12;a:4:{s:1:\\\"a\\\";i:13;s:1:\\\"b\\\";s:11:\\\"view_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:13;a:4:{s:1:\\\"a\\\";i:14;s:1:\\\"b\\\";s:15:\\\"view_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:14;a:4:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:13:\\\"create_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:15;a:4:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:13:\\\"update_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:16;a:4:{s:1:\\\"a\\\";i:17;s:1:\\\"b\\\";s:14:\\\"restore_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:17;a:4:{s:1:\\\"a\\\";i:18;s:1:\\\"b\\\";s:18:\\\"restore_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:18;a:4:{s:1:\\\"a\\\";i:19;s:1:\\\"b\\\";s:16:\\\"replicate_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:19;a:4:{s:1:\\\"a\\\";i:20;s:1:\\\"b\\\";s:14:\\\"reorder_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:20;a:4:{s:1:\\\"a\\\";i:21;s:1:\\\"b\\\";s:13:\\\"delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:21;a:4:{s:1:\\\"a\\\";i:22;s:1:\\\"b\\\";s:17:\\\"delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:22;a:4:{s:1:\\\"a\\\";i:23;s:1:\\\"b\\\";s:19:\\\"force_delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:23;a:4:{s:1:\\\"a\\\";i:24;s:1:\\\"b\\\";s:23:\\\"force_delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:24;a:4:{s:1:\\\"a\\\";i:25;s:1:\\\"b\\\";s:14:\\\"view_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:25;a:4:{s:1:\\\"a\\\";i:26;s:1:\\\"b\\\";s:18:\\\"view_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:26;a:4:{s:1:\\\"a\\\";i:27;s:1:\\\"b\\\";s:16:\\\"create_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:27;a:4:{s:1:\\\"a\\\";i:28;s:1:\\\"b\\\";s:16:\\\"update_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:28;a:4:{s:1:\\\"a\\\";i:29;s:1:\\\"b\\\";s:17:\\\"restore_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:29;a:4:{s:1:\\\"a\\\";i:30;s:1:\\\"b\\\";s:21:\\\"restore_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:30;a:4:{s:1:\\\"a\\\";i:31;s:1:\\\"b\\\";s:19:\\\"replicate_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:31;a:4:{s:1:\\\"a\\\";i:32;s:1:\\\"b\\\";s:17:\\\"reorder_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:32;a:4:{s:1:\\\"a\\\";i:33;s:1:\\\"b\\\";s:16:\\\"delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:33;a:4:{s:1:\\\"a\\\";i:34;s:1:\\\"b\\\";s:20:\\\"delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:34;a:4:{s:1:\\\"a\\\";i:35;s:1:\\\"b\\\";s:22:\\\"force_delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:35;a:4:{s:1:\\\"a\\\";i:36;s:1:\\\"b\\\";s:26:\\\"force_delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:36;a:4:{s:1:\\\"a\\\";i:37;s:1:\\\"b\\\";s:20:\\\"view_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:37;a:4:{s:1:\\\"a\\\";i:38;s:1:\\\"b\\\";s:24:\\\"view_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:38;a:4:{s:1:\\\"a\\\";i:39;s:1:\\\"b\\\";s:22:\\\"create_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:39;a:4:{s:1:\\\"a\\\";i:40;s:1:\\\"b\\\";s:22:\\\"update_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:40;a:4:{s:1:\\\"a\\\";i:41;s:1:\\\"b\\\";s:23:\\\"restore_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:41;a:4:{s:1:\\\"a\\\";i:42;s:1:\\\"b\\\";s:27:\\\"restore_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:42;a:4:{s:1:\\\"a\\\";i:43;s:1:\\\"b\\\";s:25:\\\"replicate_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:43;a:4:{s:1:\\\"a\\\";i:44;s:1:\\\"b\\\";s:23:\\\"reorder_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:44;a:4:{s:1:\\\"a\\\";i:45;s:1:\\\"b\\\";s:22:\\\"delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:45;a:4:{s:1:\\\"a\\\";i:46;s:1:\\\"b\\\";s:26:\\\"delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:46;a:4:{s:1:\\\"a\\\";i:47;s:1:\\\"b\\\";s:28:\\\"force_delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:47;a:4:{s:1:\\\"a\\\";i:48;s:1:\\\"b\\\";s:32:\\\"force_delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:48;a:4:{s:1:\\\"a\\\";i:49;s:1:\\\"b\\\";s:14:\\\"view_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:49;a:4:{s:1:\\\"a\\\";i:50;s:1:\\\"b\\\";s:18:\\\"view_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:50;a:4:{s:1:\\\"a\\\";i:51;s:1:\\\"b\\\";s:16:\\\"create_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:51;a:4:{s:1:\\\"a\\\";i:52;s:1:\\\"b\\\";s:16:\\\"update_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:52;a:4:{s:1:\\\"a\\\";i:53;s:1:\\\"b\\\";s:17:\\\"restore_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:53;a:4:{s:1:\\\"a\\\";i:54;s:1:\\\"b\\\";s:21:\\\"restore_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:54;a:4:{s:1:\\\"a\\\";i:55;s:1:\\\"b\\\";s:19:\\\"replicate_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:55;a:4:{s:1:\\\"a\\\";i:56;s:1:\\\"b\\\";s:17:\\\"reorder_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:56;a:4:{s:1:\\\"a\\\";i:57;s:1:\\\"b\\\";s:16:\\\"delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:57;a:4:{s:1:\\\"a\\\";i:58;s:1:\\\"b\\\";s:20:\\\"delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:58;a:4:{s:1:\\\"a\\\";i:59;s:1:\\\"b\\\";s:22:\\\"force_delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:59;a:4:{s:1:\\\"a\\\";i:60;s:1:\\\"b\\\";s:26:\\\"force_delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:60;a:4:{s:1:\\\"a\\\";i:61;s:1:\\\"b\\\";s:24:\\\"view_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:61;a:4:{s:1:\\\"a\\\";i:62;s:1:\\\"b\\\";s:28:\\\"view_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:62;a:4:{s:1:\\\"a\\\";i:63;s:1:\\\"b\\\";s:26:\\\"create_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:63;a:4:{s:1:\\\"a\\\";i:64;s:1:\\\"b\\\";s:26:\\\"update_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:64;a:4:{s:1:\\\"a\\\";i:65;s:1:\\\"b\\\";s:27:\\\"restore_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:65;a:4:{s:1:\\\"a\\\";i:66;s:1:\\\"b\\\";s:31:\\\"restore_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:66;a:4:{s:1:\\\"a\\\";i:67;s:1:\\\"b\\\";s:29:\\\"replicate_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:67;a:4:{s:1:\\\"a\\\";i:68;s:1:\\\"b\\\";s:27:\\\"reorder_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:68;a:4:{s:1:\\\"a\\\";i:69;s:1:\\\"b\\\";s:26:\\\"delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:69;a:4:{s:1:\\\"a\\\";i:70;s:1:\\\"b\\\";s:30:\\\"delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:70;a:4:{s:1:\\\"a\\\";i:71;s:1:\\\"b\\\";s:32:\\\"force_delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:71;a:4:{s:1:\\\"a\\\";i:72;s:1:\\\"b\\\";s:36:\\\"force_delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:72;a:4:{s:1:\\\"a\\\";i:73;s:1:\\\"b\\\";s:35:\\\"view_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:73;a:4:{s:1:\\\"a\\\";i:74;s:1:\\\"b\\\";s:39:\\\"view_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:74;a:4:{s:1:\\\"a\\\";i:75;s:1:\\\"b\\\";s:37:\\\"create_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:75;a:4:{s:1:\\\"a\\\";i:76;s:1:\\\"b\\\";s:37:\\\"update_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:76;a:4:{s:1:\\\"a\\\";i:77;s:1:\\\"b\\\";s:38:\\\"restore_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:77;a:4:{s:1:\\\"a\\\";i:78;s:1:\\\"b\\\";s:42:\\\"restore_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:78;a:4:{s:1:\\\"a\\\";i:79;s:1:\\\"b\\\";s:40:\\\"replicate_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:79;a:4:{s:1:\\\"a\\\";i:80;s:1:\\\"b\\\";s:38:\\\"reorder_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:80;a:4:{s:1:\\\"a\\\";i:81;s:1:\\\"b\\\";s:37:\\\"delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:81;a:4:{s:1:\\\"a\\\";i:82;s:1:\\\"b\\\";s:41:\\\"delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:82;a:4:{s:1:\\\"a\\\";i:83;s:1:\\\"b\\\";s:43:\\\"force_delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:83;a:4:{s:1:\\\"a\\\";i:84;s:1:\\\"b\\\";s:47:\\\"force_delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:84;a:4:{s:1:\\\"a\\\";i:85;s:1:\\\"b\\\";s:12:\\\"view_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:85;a:4:{s:1:\\\"a\\\";i:86;s:1:\\\"b\\\";s:16:\\\"view_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:86;a:4:{s:1:\\\"a\\\";i:87;s:1:\\\"b\\\";s:14:\\\"create_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:87;a:4:{s:1:\\\"a\\\";i:88;s:1:\\\"b\\\";s:14:\\\"update_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:88;a:4:{s:1:\\\"a\\\";i:89;s:1:\\\"b\\\";s:15:\\\"restore_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:89;a:4:{s:1:\\\"a\\\";i:90;s:1:\\\"b\\\";s:19:\\\"restore_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:90;a:4:{s:1:\\\"a\\\";i:91;s:1:\\\"b\\\";s:17:\\\"replicate_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:91;a:4:{s:1:\\\"a\\\";i:92;s:1:\\\"b\\\";s:15:\\\"reorder_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:92;a:4:{s:1:\\\"a\\\";i:93;s:1:\\\"b\\\";s:14:\\\"delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:93;a:4:{s:1:\\\"a\\\";i:94;s:1:\\\"b\\\";s:18:\\\"delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:94;a:4:{s:1:\\\"a\\\";i:95;s:1:\\\"b\\\";s:20:\\\"force_delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:95;a:4:{s:1:\\\"a\\\";i:96;s:1:\\\"b\\\";s:24:\\\"force_delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:96;a:4:{s:1:\\\"a\\\";i:97;s:1:\\\"b\\\";s:12:\\\"view_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:97;a:4:{s:1:\\\"a\\\";i:98;s:1:\\\"b\\\";s:16:\\\"view_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:98;a:4:{s:1:\\\"a\\\";i:99;s:1:\\\"b\\\";s:14:\\\"create_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:99;a:4:{s:1:\\\"a\\\";i:100;s:1:\\\"b\\\";s:14:\\\"update_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:100;a:4:{s:1:\\\"a\\\";i:101;s:1:\\\"b\\\";s:15:\\\"restore_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:101;a:4:{s:1:\\\"a\\\";i:102;s:1:\\\"b\\\";s:19:\\\"restore_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:102;a:4:{s:1:\\\"a\\\";i:103;s:1:\\\"b\\\";s:17:\\\"replicate_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:103;a:4:{s:1:\\\"a\\\";i:104;s:1:\\\"b\\\";s:15:\\\"reorder_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:104;a:4:{s:1:\\\"a\\\";i:105;s:1:\\\"b\\\";s:14:\\\"delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:105;a:4:{s:1:\\\"a\\\";i:106;s:1:\\\"b\\\";s:18:\\\"delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:106;a:4:{s:1:\\\"a\\\";i:107;s:1:\\\"b\\\";s:20:\\\"force_delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:107;a:4:{s:1:\\\"a\\\";i:108;s:1:\\\"b\\\";s:24:\\\"force_delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:108;a:4:{s:1:\\\"a\\\";i:109;s:1:\\\"b\\\";s:18:\\\"view_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:109;a:4:{s:1:\\\"a\\\";i:110;s:1:\\\"b\\\";s:22:\\\"view_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:110;a:4:{s:1:\\\"a\\\";i:111;s:1:\\\"b\\\";s:20:\\\"create_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:111;a:4:{s:1:\\\"a\\\";i:112;s:1:\\\"b\\\";s:20:\\\"update_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:112;a:4:{s:1:\\\"a\\\";i:113;s:1:\\\"b\\\";s:21:\\\"restore_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:113;a:4:{s:1:\\\"a\\\";i:114;s:1:\\\"b\\\";s:25:\\\"restore_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:114;a:4:{s:1:\\\"a\\\";i:115;s:1:\\\"b\\\";s:23:\\\"replicate_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:115;a:4:{s:1:\\\"a\\\";i:116;s:1:\\\"b\\\";s:21:\\\"reorder_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:116;a:4:{s:1:\\\"a\\\";i:117;s:1:\\\"b\\\";s:20:\\\"delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:117;a:4:{s:1:\\\"a\\\";i:118;s:1:\\\"b\\\";s:24:\\\"delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:118;a:4:{s:1:\\\"a\\\";i:119;s:1:\\\"b\\\";s:26:\\\"force_delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:119;a:4:{s:1:\\\"a\\\";i:120;s:1:\\\"b\\\";s:30:\\\"force_delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:120;a:4:{s:1:\\\"a\\\";i:121;s:1:\\\"b\\\";s:9:\\\"view_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:121;a:4:{s:1:\\\"a\\\";i:122;s:1:\\\"b\\\";s:13:\\\"view_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:122;a:4:{s:1:\\\"a\\\";i:123;s:1:\\\"b\\\";s:11:\\\"create_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:123;a:4:{s:1:\\\"a\\\";i:124;s:1:\\\"b\\\";s:11:\\\"update_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:124;a:4:{s:1:\\\"a\\\";i:125;s:1:\\\"b\\\";s:11:\\\"delete_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:125;a:4:{s:1:\\\"a\\\";i:126;s:1:\\\"b\\\";s:15:\\\"delete_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:126;a:4:{s:1:\\\"a\\\";i:127;s:1:\\\"b\\\";s:33:\\\"view_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:127;a:4:{s:1:\\\"a\\\";i:128;s:1:\\\"b\\\";s:37:\\\"view_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:128;a:4:{s:1:\\\"a\\\";i:129;s:1:\\\"b\\\";s:35:\\\"create_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:129;a:4:{s:1:\\\"a\\\";i:130;s:1:\\\"b\\\";s:35:\\\"update_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:130;a:4:{s:1:\\\"a\\\";i:131;s:1:\\\"b\\\";s:36:\\\"restore_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:131;a:4:{s:1:\\\"a\\\";i:132;s:1:\\\"b\\\";s:40:\\\"restore_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:132;a:4:{s:1:\\\"a\\\";i:133;s:1:\\\"b\\\";s:38:\\\"replicate_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:133;a:4:{s:1:\\\"a\\\";i:134;s:1:\\\"b\\\";s:36:\\\"reorder_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:134;a:4:{s:1:\\\"a\\\";i:135;s:1:\\\"b\\\";s:35:\\\"delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:135;a:4:{s:1:\\\"a\\\";i:136;s:1:\\\"b\\\";s:39:\\\"delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:136;a:4:{s:1:\\\"a\\\";i:137;s:1:\\\"b\\\";s:41:\\\"force_delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:137;a:4:{s:1:\\\"a\\\";i:138;s:1:\\\"b\\\";s:45:\\\"force_delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:138;a:4:{s:1:\\\"a\\\";i:139;s:1:\\\"b\\\";s:10:\\\"view_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:139;a:4:{s:1:\\\"a\\\";i:140;s:1:\\\"b\\\";s:14:\\\"view_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:140;a:4:{s:1:\\\"a\\\";i:141;s:1:\\\"b\\\";s:12:\\\"create_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:141;a:4:{s:1:\\\"a\\\";i:142;s:1:\\\"b\\\";s:12:\\\"update_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:142;a:4:{s:1:\\\"a\\\";i:143;s:1:\\\"b\\\";s:13:\\\"restore_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:143;a:4:{s:1:\\\"a\\\";i:144;s:1:\\\"b\\\";s:17:\\\"restore_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:144;a:4:{s:1:\\\"a\\\";i:145;s:1:\\\"b\\\";s:15:\\\"replicate_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:145;a:4:{s:1:\\\"a\\\";i:146;s:1:\\\"b\\\";s:13:\\\"reorder_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:146;a:4:{s:1:\\\"a\\\";i:147;s:1:\\\"b\\\";s:12:\\\"delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:147;a:4:{s:1:\\\"a\\\";i:148;s:1:\\\"b\\\";s:16:\\\"delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:148;a:4:{s:1:\\\"a\\\";i:149;s:1:\\\"b\\\";s:18:\\\"force_delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:149;a:4:{s:1:\\\"a\\\";i:150;s:1:\\\"b\\\";s:22:\\\"force_delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:150;a:4:{s:1:\\\"a\\\";i:151;s:1:\\\"b\\\";s:9:\\\"view_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:151;a:4:{s:1:\\\"a\\\";i:152;s:1:\\\"b\\\";s:13:\\\"view_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:152;a:4:{s:1:\\\"a\\\";i:153;s:1:\\\"b\\\";s:11:\\\"create_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:153;a:4:{s:1:\\\"a\\\";i:154;s:1:\\\"b\\\";s:11:\\\"update_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:154;a:4:{s:1:\\\"a\\\";i:155;s:1:\\\"b\\\";s:12:\\\"restore_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:155;a:4:{s:1:\\\"a\\\";i:156;s:1:\\\"b\\\";s:16:\\\"restore_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:156;a:4:{s:1:\\\"a\\\";i:157;s:1:\\\"b\\\";s:14:\\\"replicate_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:157;a:4:{s:1:\\\"a\\\";i:158;s:1:\\\"b\\\";s:12:\\\"reorder_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:158;a:4:{s:1:\\\"a\\\";i:159;s:1:\\\"b\\\";s:11:\\\"delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:159;a:4:{s:1:\\\"a\\\";i:160;s:1:\\\"b\\\";s:15:\\\"delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:160;a:4:{s:1:\\\"a\\\";i:161;s:1:\\\"b\\\";s:17:\\\"force_delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:161;a:4:{s:1:\\\"a\\\";i:162;s:1:\\\"b\\\";s:21:\\\"force_delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:162;a:4:{s:1:\\\"a\\\";i:163;s:1:\\\"b\\\";s:17:\\\"page_BdeDashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:163;a:4:{s:1:\\\"a\\\";i:164;s:1:\\\"b\\\";s:22:\\\"page_DashboardSettings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:164;a:4:{s:1:\\\"a\\\";i:165;s:1:\\\"b\\\";s:18:\\\"page_ManageSetting\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:165;a:4:{s:1:\\\"a\\\";i:166;s:1:\\\"b\\\";s:11:\\\"page_Themes\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:166;a:4:{s:1:\\\"a\\\";i:167;s:1:\\\"b\\\";s:18:\\\"page_MyProfilePage\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:167;a:4:{s:1:\\\"a\\\";i:168;s:1:\\\"b\\\";s:26:\\\"widget_NotificationsWidget\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:2;i:1;i:5;}}i:168;a:4:{s:1:\\\"a\\\";i:169;s:1:\\\"b\\\";s:9:\\\"view_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:169;a:4:{s:1:\\\"a\\\";i:170;s:1:\\\"b\\\";s:13:\\\"view_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:170;a:4:{s:1:\\\"a\\\";i:171;s:1:\\\"b\\\";s:11:\\\"create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:171;a:4:{s:1:\\\"a\\\";i:172;s:1:\\\"b\\\";s:11:\\\"update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:172;a:4:{s:1:\\\"a\\\";i:173;s:1:\\\"b\\\";s:12:\\\"restore_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:173;a:4:{s:1:\\\"a\\\";i:174;s:1:\\\"b\\\";s:16:\\\"restore_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:174;a:4:{s:1:\\\"a\\\";i:175;s:1:\\\"b\\\";s:14:\\\"replicate_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:175;a:4:{s:1:\\\"a\\\";i:176;s:1:\\\"b\\\";s:12:\\\"reorder_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:176;a:4:{s:1:\\\"a\\\";i:177;s:1:\\\"b\\\";s:11:\\\"delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:177;a:4:{s:1:\\\"a\\\";i:178;s:1:\\\"b\\\";s:15:\\\"delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:178;a:4:{s:1:\\\"a\\\";i:179;s:1:\\\"b\\\";s:17:\\\"force_delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:179;a:4:{s:1:\\\"a\\\";i:180;s:1:\\\"b\\\";s:21:\\\"force_delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:180;a:4:{s:1:\\\"a\\\";i:181;s:1:\\\"b\\\";s:16:\\\"book:create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:181;a:4:{s:1:\\\"a\\\";i:182;s:1:\\\"b\\\";s:16:\\\"book:update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:182;a:4:{s:1:\\\"a\\\";i:183;s:1:\\\"b\\\";s:16:\\\"book:delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:183;a:4:{s:1:\\\"a\\\";i:184;s:1:\\\"b\\\";s:20:\\\"book:pagination_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:184;a:4:{s:1:\\\"a\\\";i:185;s:1:\\\"b\\\";s:16:\\\"book:detail_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:4;}}i:185;a:3:{s:1:\\\"a\\\";i:186;s:1:\\\"b\\\";s:14:\\\"view_dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:186;a:3:{s:1:\\\"a\\\";i:187;s:1:\\\"b\\\";s:18:\\\"page_Bde_Dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:187;a:3:{s:1:\\\"a\\\";i:188;s:1:\\\"b\\\";s:11:\\\"export_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:188;a:3:{s:1:\\\"a\\\";i:189;s:1:\\\"b\\\";s:12:\\\"view_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:189;a:3:{s:1:\\\"a\\\";i:190;s:1:\\\"b\\\";s:16:\\\"view_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:190;a:3:{s:1:\\\"a\\\";i:191;s:1:\\\"b\\\";s:14:\\\"create_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:191;a:3:{s:1:\\\"a\\\";i:192;s:1:\\\"b\\\";s:14:\\\"update_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:192;a:3:{s:1:\\\"a\\\";i:193;s:1:\\\"b\\\";s:15:\\\"restore_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:193;a:3:{s:1:\\\"a\\\";i:194;s:1:\\\"b\\\";s:19:\\\"restore_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:194;a:3:{s:1:\\\"a\\\";i:195;s:1:\\\"b\\\";s:17:\\\"replicate_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:195;a:3:{s:1:\\\"a\\\";i:196;s:1:\\\"b\\\";s:15:\\\"reorder_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:196;a:3:{s:1:\\\"a\\\";i:197;s:1:\\\"b\\\";s:14:\\\"delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:197;a:3:{s:1:\\\"a\\\";i:198;s:1:\\\"b\\\";s:18:\\\"delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:198;a:3:{s:1:\\\"a\\\";i:199;s:1:\\\"b\\\";s:20:\\\"force_delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:199;a:3:{s:1:\\\"a\\\";i:200;s:1:\\\"b\\\";s:24:\\\"force_delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:200;a:3:{s:1:\\\"a\\\";i:201;s:1:\\\"b\\\";s:9:\\\"view_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:201;a:3:{s:1:\\\"a\\\";i:202;s:1:\\\"b\\\";s:13:\\\"view_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:202;a:3:{s:1:\\\"a\\\";i:203;s:1:\\\"b\\\";s:11:\\\"create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:203;a:3:{s:1:\\\"a\\\";i:204;s:1:\\\"b\\\";s:11:\\\"update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:204;a:3:{s:1:\\\"a\\\";i:205;s:1:\\\"b\\\";s:12:\\\"restore_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:205;a:3:{s:1:\\\"a\\\";i:206;s:1:\\\"b\\\";s:16:\\\"restore_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:206;a:3:{s:1:\\\"a\\\";i:207;s:1:\\\"b\\\";s:14:\\\"replicate_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:207;a:3:{s:1:\\\"a\\\";i:208;s:1:\\\"b\\\";s:12:\\\"reorder_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:208;a:3:{s:1:\\\"a\\\";i:209;s:1:\\\"b\\\";s:11:\\\"delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:209;a:3:{s:1:\\\"a\\\";i:210;s:1:\\\"b\\\";s:15:\\\"delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:210;a:3:{s:1:\\\"a\\\";i:211;s:1:\\\"b\\\";s:17:\\\"force_delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:211;a:3:{s:1:\\\"a\\\";i:212;s:1:\\\"b\\\";s:21:\\\"force_delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:212;a:3:{s:1:\\\"a\\\";i:213;s:1:\\\"b\\\";s:16:\\\"post:create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:213;a:3:{s:1:\\\"a\\\";i:214;s:1:\\\"b\\\";s:16:\\\"post:update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:214;a:3:{s:1:\\\"a\\\";i:215;s:1:\\\"b\\\";s:16:\\\"post:delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:215;a:3:{s:1:\\\"a\\\";i:216;s:1:\\\"b\\\";s:20:\\\"post:pagination_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:216;a:3:{s:1:\\\"a\\\";i:217;s:1:\\\"b\\\";s:16:\\\"post:detail_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}}s:5:\\\"roles\\\";a:4:{i:0;a:3:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:8:\\\"bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:1;a:3:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:9:\\\"marketing\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:2;a:3:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:9:\\\"Moderator\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:3;a:3:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:11:\\\"super_admin\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1749617675, "spatie.permission.cache", "a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:217:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:22:\"view_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:26:\"view_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:24:\"create_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:24:\"update_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:25:\"restore_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:29:\"restore_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:27:\"replicate_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:25:\"reorder_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:24:\"delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:28:\"delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:30:\"force_delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:34:\"force_delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:11:\"view_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:15:\"view_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:13:\"create_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:13:\"update_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:14:\"restore_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:18:\"restore_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:16:\"replicate_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:14:\"reorder_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:13:\"delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:17:\"delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:19:\"force_delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:23:\"force_delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:14:\"view_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:18:\"view_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:16:\"create_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:16:\"update_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:17:\"restore_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:21:\"restore_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:19:\"replicate_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:31;a:4:{s:1:\"a\";i:32;s:1:\"b\";s:17:\"reorder_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:32;a:4:{s:1:\"a\";i:33;s:1:\"b\";s:16:\"delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:33;a:4:{s:1:\"a\";i:34;s:1:\"b\";s:20:\"delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:34;a:4:{s:1:\"a\";i:35;s:1:\"b\";s:22:\"force_delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:35;a:4:{s:1:\"a\";i:36;s:1:\"b\";s:26:\"force_delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:36;a:4:{s:1:\"a\";i:37;s:1:\"b\";s:20:\"view_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:37;a:4:{s:1:\"a\";i:38;s:1:\"b\";s:24:\"view_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:38;a:4:{s:1:\"a\";i:39;s:1:\"b\";s:22:\"create_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:39;a:4:{s:1:\"a\";i:40;s:1:\"b\";s:22:\"update_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:40;a:4:{s:1:\"a\";i:41;s:1:\"b\";s:23:\"restore_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:41;a:4:{s:1:\"a\";i:42;s:1:\"b\";s:27:\"restore_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:42;a:4:{s:1:\"a\";i:43;s:1:\"b\";s:25:\"replicate_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:43;a:4:{s:1:\"a\";i:44;s:1:\"b\";s:23:\"reorder_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:44;a:4:{s:1:\"a\";i:45;s:1:\"b\";s:22:\"delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:45;a:4:{s:1:\"a\";i:46;s:1:\"b\";s:26:\"delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:46;a:4:{s:1:\"a\";i:47;s:1:\"b\";s:28:\"force_delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:47;a:4:{s:1:\"a\";i:48;s:1:\"b\";s:32:\"force_delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:48;a:4:{s:1:\"a\";i:49;s:1:\"b\";s:14:\"view_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:49;a:4:{s:1:\"a\";i:50;s:1:\"b\";s:18:\"view_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:50;a:4:{s:1:\"a\";i:51;s:1:\"b\";s:16:\"create_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:51;a:4:{s:1:\"a\";i:52;s:1:\"b\";s:16:\"update_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:52;a:4:{s:1:\"a\";i:53;s:1:\"b\";s:17:\"restore_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:53;a:4:{s:1:\"a\";i:54;s:1:\"b\";s:21:\"restore_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:54;a:4:{s:1:\"a\";i:55;s:1:\"b\";s:19:\"replicate_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:55;a:4:{s:1:\"a\";i:56;s:1:\"b\";s:17:\"reorder_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:56;a:4:{s:1:\"a\";i:57;s:1:\"b\";s:16:\"delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:57;a:4:{s:1:\"a\";i:58;s:1:\"b\";s:20:\"delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:58;a:4:{s:1:\"a\";i:59;s:1:\"b\";s:22:\"force_delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:59;a:4:{s:1:\"a\";i:60;s:1:\"b\";s:26:\"force_delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:60;a:4:{s:1:\"a\";i:61;s:1:\"b\";s:24:\"view_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:61;a:4:{s:1:\"a\";i:62;s:1:\"b\";s:28:\"view_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:62;a:4:{s:1:\"a\";i:63;s:1:\"b\";s:26:\"create_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:63;a:4:{s:1:\"a\";i:64;s:1:\"b\";s:26:\"update_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:64;a:4:{s:1:\"a\";i:65;s:1:\"b\";s:27:\"restore_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:65;a:4:{s:1:\"a\";i:66;s:1:\"b\";s:31:\"restore_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:66;a:4:{s:1:\"a\";i:67;s:1:\"b\";s:29:\"replicate_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:67;a:4:{s:1:\"a\";i:68;s:1:\"b\";s:27:\"reorder_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:68;a:4:{s:1:\"a\";i:69;s:1:\"b\";s:26:\"delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:69;a:4:{s:1:\"a\";i:70;s:1:\"b\";s:30:\"delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:70;a:4:{s:1:\"a\";i:71;s:1:\"b\";s:32:\"force_delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:71;a:4:{s:1:\"a\";i:72;s:1:\"b\";s:36:\"force_delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:72;a:4:{s:1:\"a\";i:73;s:1:\"b\";s:35:\"view_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:73;a:4:{s:1:\"a\";i:74;s:1:\"b\";s:39:\"view_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:74;a:4:{s:1:\"a\";i:75;s:1:\"b\";s:37:\"create_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:75;a:4:{s:1:\"a\";i:76;s:1:\"b\";s:37:\"update_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:76;a:4:{s:1:\"a\";i:77;s:1:\"b\";s:38:\"restore_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:77;a:4:{s:1:\"a\";i:78;s:1:\"b\";s:42:\"restore_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:78;a:4:{s:1:\"a\";i:79;s:1:\"b\";s:40:\"replicate_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:79;a:4:{s:1:\"a\";i:80;s:1:\"b\";s:38:\"reorder_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:80;a:4:{s:1:\"a\";i:81;s:1:\"b\";s:37:\"delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:81;a:4:{s:1:\"a\";i:82;s:1:\"b\";s:41:\"delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:82;a:4:{s:1:\"a\";i:83;s:1:\"b\";s:43:\"force_delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:83;a:4:{s:1:\"a\";i:84;s:1:\"b\";s:47:\"force_delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:84;a:4:{s:1:\"a\";i:85;s:1:\"b\";s:12:\"view_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:85;a:4:{s:1:\"a\";i:86;s:1:\"b\";s:16:\"view_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:86;a:4:{s:1:\"a\";i:87;s:1:\"b\";s:14:\"create_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:87;a:4:{s:1:\"a\";i:88;s:1:\"b\";s:14:\"update_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:88;a:4:{s:1:\"a\";i:89;s:1:\"b\";s:15:\"restore_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:89;a:4:{s:1:\"a\";i:90;s:1:\"b\";s:19:\"restore_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:90;a:4:{s:1:\"a\";i:91;s:1:\"b\";s:17:\"replicate_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:91;a:4:{s:1:\"a\";i:92;s:1:\"b\";s:15:\"reorder_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:92;a:4:{s:1:\"a\";i:93;s:1:\"b\";s:14:\"delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:93;a:4:{s:1:\"a\";i:94;s:1:\"b\";s:18:\"delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:94;a:4:{s:1:\"a\";i:95;s:1:\"b\";s:20:\"force_delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:95;a:4:{s:1:\"a\";i:96;s:1:\"b\";s:24:\"force_delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:96;a:4:{s:1:\"a\";i:97;s:1:\"b\";s:12:\"view_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:97;a:4:{s:1:\"a\";i:98;s:1:\"b\";s:16:\"view_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:98;a:4:{s:1:\"a\";i:99;s:1:\"b\";s:14:\"create_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:99;a:4:{s:1:\"a\";i:100;s:1:\"b\";s:14:\"update_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:100;a:4:{s:1:\"a\";i:101;s:1:\"b\";s:15:\"restore_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:101;a:4:{s:1:\"a\";i:102;s:1:\"b\";s:19:\"restore_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:102;a:4:{s:1:\"a\";i:103;s:1:\"b\";s:17:\"replicate_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:103;a:4:{s:1:\"a\";i:104;s:1:\"b\";s:15:\"reorder_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:104;a:4:{s:1:\"a\";i:105;s:1:\"b\";s:14:\"delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:105;a:4:{s:1:\"a\";i:106;s:1:\"b\";s:18:\"delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:106;a:4:{s:1:\"a\";i:107;s:1:\"b\";s:20:\"force_delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:107;a:4:{s:1:\"a\";i:108;s:1:\"b\";s:24:\"force_delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:108;a:4:{s:1:\"a\";i:109;s:1:\"b\";s:18:\"view_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:109;a:4:{s:1:\"a\";i:110;s:1:\"b\";s:22:\"view_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:110;a:4:{s:1:\"a\";i:111;s:1:\"b\";s:20:\"create_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:111;a:4:{s:1:\"a\";i:112;s:1:\"b\";s:20:\"update_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:112;a:4:{s:1:\"a\";i:113;s:1:\"b\";s:21:\"restore_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:113;a:4:{s:1:\"a\";i:114;s:1:\"b\";s:25:\"restore_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:114;a:4:{s:1:\"a\";i:115;s:1:\"b\";s:23:\"replicate_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:115;a:4:{s:1:\"a\";i:116;s:1:\"b\";s:21:\"reorder_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:116;a:4:{s:1:\"a\";i:117;s:1:\"b\";s:20:\"delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:117;a:4:{s:1:\"a\";i:118;s:1:\"b\";s:24:\"delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:118;a:4:{s:1:\"a\";i:119;s:1:\"b\";s:26:\"force_delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:119;a:4:{s:1:\"a\";i:120;s:1:\"b\";s:30:\"force_delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:120;a:4:{s:1:\"a\";i:121;s:1:\"b\";s:9:\"view_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:121;a:4:{s:1:\"a\";i:122;s:1:\"b\";s:13:\"view_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:122;a:4:{s:1:\"a\";i:123;s:1:\"b\";s:11:\"create_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:123;a:4:{s:1:\"a\";i:124;s:1:\"b\";s:11:\"update_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:124;a:4:{s:1:\"a\";i:125;s:1:\"b\";s:11:\"delete_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:125;a:4:{s:1:\"a\";i:126;s:1:\"b\";s:15:\"delete_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:126;a:4:{s:1:\"a\";i:127;s:1:\"b\";s:33:\"view_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:127;a:4:{s:1:\"a\";i:128;s:1:\"b\";s:37:\"view_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:128;a:4:{s:1:\"a\";i:129;s:1:\"b\";s:35:\"create_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:129;a:4:{s:1:\"a\";i:130;s:1:\"b\";s:35:\"update_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:130;a:4:{s:1:\"a\";i:131;s:1:\"b\";s:36:\"restore_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:131;a:4:{s:1:\"a\";i:132;s:1:\"b\";s:40:\"restore_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:132;a:4:{s:1:\"a\";i:133;s:1:\"b\";s:38:\"replicate_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:133;a:4:{s:1:\"a\";i:134;s:1:\"b\";s:36:\"reorder_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:134;a:4:{s:1:\"a\";i:135;s:1:\"b\";s:35:\"delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:135;a:4:{s:1:\"a\";i:136;s:1:\"b\";s:39:\"delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:136;a:4:{s:1:\"a\";i:137;s:1:\"b\";s:41:\"force_delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:137;a:4:{s:1:\"a\";i:138;s:1:\"b\";s:45:\"force_delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:138;a:4:{s:1:\"a\";i:139;s:1:\"b\";s:10:\"view_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:139;a:4:{s:1:\"a\";i:140;s:1:\"b\";s:14:\"view_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:140;a:4:{s:1:\"a\";i:141;s:1:\"b\";s:12:\"create_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:141;a:4:{s:1:\"a\";i:142;s:1:\"b\";s:12:\"update_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:142;a:4:{s:1:\"a\";i:143;s:1:\"b\";s:13:\"restore_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:143;a:4:{s:1:\"a\";i:144;s:1:\"b\";s:17:\"restore_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:144;a:4:{s:1:\"a\";i:145;s:1:\"b\";s:15:\"replicate_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:145;a:4:{s:1:\"a\";i:146;s:1:\"b\";s:13:\"reorder_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:146;a:4:{s:1:\"a\";i:147;s:1:\"b\";s:12:\"delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:147;a:4:{s:1:\"a\";i:148;s:1:\"b\";s:16:\"delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:148;a:4:{s:1:\"a\";i:149;s:1:\"b\";s:18:\"force_delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:149;a:4:{s:1:\"a\";i:150;s:1:\"b\";s:22:\"force_delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:2;i:1;i:4;i:2;i:5;}}i:150;a:4:{s:1:\"a\";i:151;s:1:\"b\";s:9:\"view_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:151;a:4:{s:1:\"a\";i:152;s:1:\"b\";s:13:\"view_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:152;a:4:{s:1:\"a\";i:153;s:1:\"b\";s:11:\"create_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:153;a:4:{s:1:\"a\";i:154;s:1:\"b\";s:11:\"update_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:154;a:4:{s:1:\"a\";i:155;s:1:\"b\";s:12:\"restore_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:155;a:4:{s:1:\"a\";i:156;s:1:\"b\";s:16:\"restore_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:156;a:4:{s:1:\"a\";i:157;s:1:\"b\";s:14:\"replicate_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:157;a:4:{s:1:\"a\";i:158;s:1:\"b\";s:12:\"reorder_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:158;a:4:{s:1:\"a\";i:159;s:1:\"b\";s:11:\"delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:159;a:4:{s:1:\"a\";i:160;s:1:\"b\";s:15:\"delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:160;a:4:{s:1:\"a\";i:161;s:1:\"b\";s:17:\"force_delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:161;a:4:{s:1:\"a\";i:162;s:1:\"b\";s:21:\"force_delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:162;a:4:{s:1:\"a\";i:163;s:1:\"b\";s:17:\"page_BdeDashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:163;a:4:{s:1:\"a\";i:164;s:1:\"b\";s:22:\"page_DashboardSettings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:164;a:4:{s:1:\"a\";i:165;s:1:\"b\";s:18:\"page_ManageSetting\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:165;a:4:{s:1:\"a\";i:166;s:1:\"b\";s:11:\"page_Themes\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:166;a:4:{s:1:\"a\";i:167;s:1:\"b\";s:18:\"page_MyProfilePage\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;}}i:167;a:4:{s:1:\"a\";i:168;s:1:\"b\";s:26:\"widget_NotificationsWidget\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:2;i:1;i:5;}}i:168;a:4:{s:1:\"a\";i:169;s:1:\"b\";s:9:\"view_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:169;a:4:{s:1:\"a\";i:170;s:1:\"b\";s:13:\"view_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:170;a:4:{s:1:\"a\";i:171;s:1:\"b\";s:11:\"create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:171;a:4:{s:1:\"a\";i:172;s:1:\"b\";s:11:\"update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:172;a:4:{s:1:\"a\";i:173;s:1:\"b\";s:12:\"restore_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:173;a:4:{s:1:\"a\";i:174;s:1:\"b\";s:16:\"restore_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:174;a:4:{s:1:\"a\";i:175;s:1:\"b\";s:14:\"replicate_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:175;a:4:{s:1:\"a\";i:176;s:1:\"b\";s:12:\"reorder_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:176;a:4:{s:1:\"a\";i:177;s:1:\"b\";s:11:\"delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:177;a:4:{s:1:\"a\";i:178;s:1:\"b\";s:15:\"delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:178;a:4:{s:1:\"a\";i:179;s:1:\"b\";s:17:\"force_delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:179;a:4:{s:1:\"a\";i:180;s:1:\"b\";s:21:\"force_delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:180;a:4:{s:1:\"a\";i:181;s:1:\"b\";s:16:\"book:create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:181;a:4:{s:1:\"a\";i:182;s:1:\"b\";s:16:\"book:update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:182;a:4:{s:1:\"a\";i:183;s:1:\"b\";s:16:\"book:delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:183;a:4:{s:1:\"a\";i:184;s:1:\"b\";s:20:\"book:pagination_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:184;a:4:{s:1:\"a\";i:185;s:1:\"b\";s:16:\"book:detail_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:4;}}i:185;a:3:{s:1:\"a\";i:186;s:1:\"b\";s:14:\"view_dashboard\";s:1:\"c\";s:3:\"web\";}i:186;a:3:{s:1:\"a\";i:187;s:1:\"b\";s:18:\"page_Bde_Dashboard\";s:1:\"c\";s:3:\"web\";}i:187;a:3:{s:1:\"a\";i:188;s:1:\"b\";s:11:\"export_book\";s:1:\"c\";s:3:\"web\";}i:188;a:3:{s:1:\"a\";i:189;s:1:\"b\";s:12:\"view_contact\";s:1:\"c\";s:3:\"web\";}i:189;a:3:{s:1:\"a\";i:190;s:1:\"b\";s:16:\"view_any_contact\";s:1:\"c\";s:3:\"web\";}i:190;a:3:{s:1:\"a\";i:191;s:1:\"b\";s:14:\"create_contact\";s:1:\"c\";s:3:\"web\";}i:191;a:3:{s:1:\"a\";i:192;s:1:\"b\";s:14:\"update_contact\";s:1:\"c\";s:3:\"web\";}i:192;a:3:{s:1:\"a\";i:193;s:1:\"b\";s:15:\"restore_contact\";s:1:\"c\";s:3:\"web\";}i:193;a:3:{s:1:\"a\";i:194;s:1:\"b\";s:19:\"restore_any_contact\";s:1:\"c\";s:3:\"web\";}i:194;a:3:{s:1:\"a\";i:195;s:1:\"b\";s:17:\"replicate_contact\";s:1:\"c\";s:3:\"web\";}i:195;a:3:{s:1:\"a\";i:196;s:1:\"b\";s:15:\"reorder_contact\";s:1:\"c\";s:3:\"web\";}i:196;a:3:{s:1:\"a\";i:197;s:1:\"b\";s:14:\"delete_contact\";s:1:\"c\";s:3:\"web\";}i:197;a:3:{s:1:\"a\";i:198;s:1:\"b\";s:18:\"delete_any_contact\";s:1:\"c\";s:3:\"web\";}i:198;a:3:{s:1:\"a\";i:199;s:1:\"b\";s:20:\"force_delete_contact\";s:1:\"c\";s:3:\"web\";}i:199;a:3:{s:1:\"a\";i:200;s:1:\"b\";s:24:\"force_delete_any_contact\";s:1:\"c\";s:3:\"web\";}i:200;a:3:{s:1:\"a\";i:201;s:1:\"b\";s:9:\"view_post\";s:1:\"c\";s:3:\"web\";}i:201;a:3:{s:1:\"a\";i:202;s:1:\"b\";s:13:\"view_any_post\";s:1:\"c\";s:3:\"web\";}i:202;a:3:{s:1:\"a\";i:203;s:1:\"b\";s:11:\"create_post\";s:1:\"c\";s:3:\"web\";}i:203;a:3:{s:1:\"a\";i:204;s:1:\"b\";s:11:\"update_post\";s:1:\"c\";s:3:\"web\";}i:204;a:3:{s:1:\"a\";i:205;s:1:\"b\";s:12:\"restore_post\";s:1:\"c\";s:3:\"web\";}i:205;a:3:{s:1:\"a\";i:206;s:1:\"b\";s:16:\"restore_any_post\";s:1:\"c\";s:3:\"web\";}i:206;a:3:{s:1:\"a\";i:207;s:1:\"b\";s:14:\"replicate_post\";s:1:\"c\";s:3:\"web\";}i:207;a:3:{s:1:\"a\";i:208;s:1:\"b\";s:12:\"reorder_post\";s:1:\"c\";s:3:\"web\";}i:208;a:3:{s:1:\"a\";i:209;s:1:\"b\";s:11:\"delete_post\";s:1:\"c\";s:3:\"web\";}i:209;a:3:{s:1:\"a\";i:210;s:1:\"b\";s:15:\"delete_any_post\";s:1:\"c\";s:3:\"web\";}i:210;a:3:{s:1:\"a\";i:211;s:1:\"b\";s:17:\"force_delete_post\";s:1:\"c\";s:3:\"web\";}i:211;a:3:{s:1:\"a\";i:212;s:1:\"b\";s:21:\"force_delete_any_post\";s:1:\"c\";s:3:\"web\";}i:212;a:3:{s:1:\"a\";i:213;s:1:\"b\";s:16:\"post:create_post\";s:1:\"c\";s:3:\"web\";}i:213;a:3:{s:1:\"a\";i:214;s:1:\"b\";s:16:\"post:update_post\";s:1:\"c\";s:3:\"web\";}i:214;a:3:{s:1:\"a\";i:215;s:1:\"b\";s:16:\"post:delete_post\";s:1:\"c\";s:3:\"web\";}i:215;a:3:{s:1:\"a\";i:216;s:1:\"b\";s:20:\"post:pagination_post\";s:1:\"c\";s:3:\"web\";}i:216;a:3:{s:1:\"a\";i:217;s:1:\"b\";s:16:\"post:detail_post\";s:1:\"c\";s:3:\"web\";}}s:5:\"roles\";a:4:{i:0;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:8:\"bde_team\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:2;s:1:\"b\";s:9:\"marketing\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:5;s:1:\"b\";s:9:\"Moderator\";s:1:\"c\";s:3:\"web\";}i:3;a:3:{s:1:\"a\";i:4;s:1:\"b\";s:11:\"super_admin\";s:1:\"c\";s:3:\"web\";}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": 1749531275.692837, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.267, "width_percent": 4.32}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (4) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": 1749531275.729068, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.587, "width_percent": 9.604}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1749531275.8235872, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 99.191, "width_percent": 0.809}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 541, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 217, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 760, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 30, "messages": [{"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-409569268 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409569268\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.739882, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1468095141 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468095141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.742035, "xdebug_link": null}, {"message": "[\n  ability => view_any_app::notification,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-333528253 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">view_any_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333528253\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.777063, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-205464122 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205464122\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.777387, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2018206950 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2018206950\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.782337, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1077582295 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077582295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.782504, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1273886582 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273886582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.786231, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1229522553 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229522553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.786416, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive::rule,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1922882691 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive::rule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_incentive::rule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922882691\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.789401, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-871790321 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871790321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.789553, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-240557019 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-240557019\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.791788, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1007858194 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007858194\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.791952, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::event,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-950594755 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">view_any_notification::event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950594755\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.794522, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1220246771 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1220246771\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.794658, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::role::preference,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-717006234 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::role::preference </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"39 characters\">view_any_notification::role::preference</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-717006234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.797961, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1033834060 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1033834060\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.79994, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1742507230 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742507230\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.806033, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1044320705 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044320705\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.806163, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1960240020 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960240020\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.810961, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-708019436 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708019436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.811152, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::type,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-796240721 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::type </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_project::type</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-796240721\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.81376, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-424282379 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424282379\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.813882, "xdebug_link": null}, {"message": "[\n  ability => view_any_role::notification::settings,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1220094710 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role::notification::settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"37 characters\">view_any_role::notification::settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1220094710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.818353, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-358869620 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358869620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.818763, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1908668299 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908668299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.820826, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 4,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1885216255 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885216255\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.820972, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-993562167 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993562167\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.827171, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2145349048 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145349048\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749531275.827344, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-435405115 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435405115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1749531275.97558, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 4,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1353773744 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353773744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1749531275.975831, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Pages\\Login@authenticate<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Filament/Pages/Login.php:19-57</a>", "middleware": "web", "duration": "4.55s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2041325712 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2041325712\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-632589229 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LyOzwunMURDskA2USKVv76mH6IWauOoKG4nN4SqO</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"936 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;password&quot;:&quot;password&quot;,&quot;remember&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;wXRuHFb2zmrS5shuug0E&quot;,&quot;name&quot;:&quot;app.filament.pages.login&quot;,&quot;path&quot;:&quot;admin\\/login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;2548e2df04313837ad9bcd9c0aa683737b01f7665f3d60d6ad01186307d59601&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.email</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><EMAIL></span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632589229\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-942368370 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1243</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6InRpaXdrMmRabGd2SGR4bmZYVG5BdFE9PSIsInZhbHVlIjoiOUpaY0dGaU9vUk1EeElmL0x5anZjeUdYQWxJQzlQVEptM2k1L28vQm5LOEMwbHM3KzRaeXFyeTFTRlVMUUo1WklyYzlxeHhvelZCZ2RKMm5pTjNDelpOeE5QTHZLNEhTeUxVbnFFTy92Z2JJS2ZGZDYvbEVqckdid2JVSjVpbisiLCJtYWMiOiJiZDliYmMwODNiNDMwZjI5YTMwZWJhYTI2N2FkMTdiOGY5MDY1OTQ3MThkNjI5YTI1YjY0N2RiNTA1MjY1M2ZlIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6ImhLL1Y2UmxjOUpIbi92cC9GWkY3V0E9PSIsInZhbHVlIjoicERyVkZoc2JldWhJNWNuRWJRcHpvRytlTjBUQ2o1V0I1eW9zWERCQjhsMklmNGZrZHhOYzNoYXRLblBTNjlBNDAwMWFOTm5IMnplWWI1RUdqUktVUVJwbGcvOHc4SE1IeFZxZW5SZDZyNmNIT2hvR2VRTXF4dDB5V1FjajhMZUciLCJtYWMiOiJkYzk2N2U1NTM4YTBlZWViYWMzMzY2YzAzOWU3MDk3Zjc1MmM0YTdlMWJlZmRjY2NkZmQyODhlNjc2M2JhMWY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942368370\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-84191281 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LyOzwunMURDskA2USKVv76mH6IWauOoKG4nN4SqO</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l9J2SElbQzqP8xK93n4Bdsjh9GGB4Rgpe0DlqIYz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84191281\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1626205768 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 04:54:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626205768\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-531192754 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pIhXU68biCOw5kg0shO4wpcoz2jhwy4nCGAmarvK</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>4</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-531192754\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}