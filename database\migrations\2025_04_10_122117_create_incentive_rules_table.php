<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('incentive_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_type_id')->constrained();
            $table->foreignId('role_id')->constrained();
            $table->decimal('percentage', 5, 2);
            $table->decimal('duration_percentage', 5, 2);
            $table->decimal('fixed_amount', 10, 2)->nullable();
            $table->string('currency')->default('INR');
            $table->string('calculation_type');
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('incentive_rules');
    }
};
