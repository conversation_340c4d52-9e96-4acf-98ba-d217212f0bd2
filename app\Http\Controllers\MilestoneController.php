<?php

namespace App\Http\Controllers;

use App\Http\Requests\MilestoneStoreRequest;
use App\Http\Requests\MilestoneUpdateRequest;
use App\Models\Milestone;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class MilestoneController extends Controller
{
    public function index(Request $request): View
    {
        $milestones = Milestone::all();

        return view('milestone.index', [
            'milestones' => $milestones,
        ]);
    }

    public function create(Request $request): View
    {
        return view('milestone.create');
    }

    public function store(MilestoneStoreRequest $request): RedirectResponse
    {
        $milestone = Milestone::create($request->validated());

        $request->session()->flash('milestone.id', $milestone->id);

        return redirect()->route('milestones.index');
    }

    public function show(Request $request, Milestone $milestone): View
    {
        return view('milestone.show', [
            'milestone' => $milestone,
        ]);
    }

    public function edit(Request $request, Milestone $milestone): View
    {
        return view('milestone.edit', [
            'milestone' => $milestone,
        ]);
    }

    public function update(MilestoneUpdateRequest $request, Milestone $milestone): RedirectResponse
    {
        $milestone->update($request->validated());

        $request->session()->flash('milestone.id', $milestone->id);

        return redirect()->route('milestones.index');
    }

    public function destroy(Request $request, Milestone $milestone): RedirectResponse
    {
        $milestone->delete();

        return redirect()->route('milestones.index');
    }
}
