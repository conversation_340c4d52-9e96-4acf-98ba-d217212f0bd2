# Instructions for creating a System Requirements Specification (SRS)

You are a senior software architect and an expert in creating detailed System Requirements Specifications (SRS) for software development teams.

Your task is to create a comprehensive Software Requirements Specification (SRS) based on the following PRD:

<prd_document>

## WON PROJECT DETAILS (For Sales & Marketing)

| BDE Name | Won Date  | Project Start Date & End Date | Client Name        | Project Type        | Total Payment | Total Duration | Payment Cycle |
| -------- | --------- | ----------------------------- | ------------------ | ------------------- | ------------- | -------------- | ------------- |
| Falak    | 11/1/2024 | 11/4/2024                     | Dr. Abhishek Patil | Website Development | ₹ 10,000.00   | 4 Weeks        | Monthly       |
|          | 12/2/2024 |                               |                    | Milestone1 - 50%    |               |                |               |
|          | 12/2/2024 |                               |                    | Milestone2 - 50%    |               |                |               |
|          | 12/2/2024 | Server                        |                    | ₹ 2,500.00          | 5 Years       | Yearly         |
| Sahil    | 11/2/2024 | 11/4/2024                     | Pizza Junction     | DM - Starter        | ₹ 120,000.00  | 12 Months      | Monthly       |
|          | 11/4/2025 |                               |                    |                     |               |                |               |

---

## INCENTIVE STRUCTURE (For Sales People Only)

| Role                           | Contract/Digital Marketing     | Fixed-USD      | Fixed-INR | T&M-USD        | T&M-INR | Product Incentive |
| ------------------------------ | ------------------------------ | -------------- | --------- | -------------- | ------- | ----------------- |
| **BDE (Business Development)** | 10% for 25% length of contract | USD to INR     | 10%       | USD to INR     | 10%     | ₹1500/- per sale  |
| **Jr. BDE (Lead Generation)**  | 5% for 25% length of contract  | (USD/2) to INR | 5%        | (USD/2) to INR | 5%      | ₹1500/- per sale  |

---

## REPORTS

| Report Type                                | For Management | For BDE Team | For Marketing |
| ------------------------------------------ | -------------- | ------------ | ------------- |
| Total Sales                                | Yes            | Yes          |               |
| Month-wise                                 | Yes            | Yes          |               |
| BDE-wise                                   | Yes            | Yes          |               |
| Incentives                                 | Yes            | Yes          |               |
| Month-wise Incentives                      | Yes            | Yes          |               |
| BDE-wise Incentives                        | Yes            | Yes          |               |
| Revenue Projection for Recurring Contracts | Yes            |              |               |

---

## NOTIFICATIONS

| Notification Type           | For Management | For BDE Team | For Marketing |
| --------------------------- | -------------- | ------------ | ------------- |
| Due Payments                | Yes            |              |               |
| Contract Renewals           | Yes            | Yes          | Yes           |
| Monthly Performance Reports | Yes            |              | Yes           |

</prd_document>

<tech_requirements>

<backend>
Laravel  
TALL Stack (Tailwind, Alpine.js, Laravel, Livewire)  
Filament Admin  
Laravel Permission  
Laravel Impersonate  
</backend>

<frontend>
Blade UI Kit  
Livewire  
Tailwind UI  
</frontend>

<database>
Database requirements: Relational Database (supports migrations, indexing, and relationships)  
Database provider: MySQL  
</database>

</tech_requirements>

<steps>
1. Begin with a brief overview explaining the technical approach and architecture.
2. Use sentence case for all headings except for the title of the document which should be title case and match identically to the title of the PRD.
3. Analyze the PRD to determine which technical features are required:
   - Only include sections that are relevant to the project
   - Skip authentication if not mentioned in PRD
   - Skip API if not needed
   - Skip database if not required
4. Under each main heading include relevant subheadings based on PRD requirements.
5. For each section:
   - Use clear and technical language
   - Provide specific implementation details
   - Maintain traceability to PRD requirements
   - Address all technical considerations
6. When creating functional requirements:
   - List only the necessary requirements based on PRD
   - Assign a unique requirement ID (e.g., FR-001) for traceability
   - Include specific technical constraints and validations
   - Ensure each requirement is testable
7. Format your SRS:
   - Maintain consistent formatting and numbering
   - Do not use dividers or horizontal rules
   - Format in valid Markdown
   - No disclaimers or conclusions
</steps>

<srs_outline>

# SRS: {project_title}

## 1. System overview

### 1.1 Document information

-   SRS: {project_title}
-   Version: {{srs_version}}
-   Related PRD: {prd_version}

### 1.2 Technical approach

-   Overview of the technical architecture and approach
-   Only include components that are needed

<frontend_section>

## 2. Frontend architecture

### 2.1 Technology stack

-   Frontend framework details
-   UI components
-   State management (if needed)

### 2.2 User interface

-   Component structure
-   Responsive design approach
-   UI/UX implementation details
    </frontend_section>

<backend_section>

## 3. Backend architecture

### 3.1 Technology stack

-   Backend framework details
-   Server requirements

### 3.2 Data layer

-   Data storage approach (if needed)
-   Caching strategy (if required)
    </backend_section>

<api_section>

## 4. API specification (if required)

### 4.1 API endpoints

-   List all API endpoints in the following format:
-   **{endpoint}** ({method}) [PRD-REF] - Request/Response format - Validation rules
    </api_section>

<database_section>

## 5. Database design (if required)

### 5.1 Schema design

-   List all database models in the following format:
-   **{model_name}** - Fields and types - Relationships - Indexes
    </database_section>

<auth_section>

## 6. Authentication & authorization (if required)

### 6.1 Authentication system

-   Authentication method
-   User sessions

### 6.2 Authorization rules

-   User roles and permissions
-   Access control
    </auth_section>

## 7. Technical requirements

### 7.1 Functional requirements

-   List each feature with its requirements in the following format:
-   **{feature_name}** [PRD-REF]
    -   Technical implementation details

### 7.2 Non-functional requirements

-   Performance requirements (if specified)
-   Security requirements (if needed)
-   Scalability considerations (if relevant)

## 8. Implementation details

### 8.1 Development approach

-   Development methodology
-   Testing strategy (if required)

### 8.2 Deployment strategy

-   Deployment process
-   Environment requirements
    </srs_outline>

<functional_requirement>

-   ID: FR-XXX
-   Title
-   Related PRD: [PRD-REF]
-   Description
-   Technical details
-   Validation rules (if needed)
-   Error handling
    </functional_requirement>

<use_case>

-   ID: UC-XXX
-   Title
-   Related PRD: [PRD-REF]
-   Actor
-   Preconditions
-   Flow
-   Alternative flows (if any)
-   Error cases
    </use_case>

<instructions>
1. Only include sections that are relevant based on the PRD requirements
2. Skip any sections that don't apply to the project
3. If a section is not needed, exclude it entirely rather than leaving it empty
4. Maintain consistent numbering even when sections are skipped
5. Focus on the technical implementation of features mentioned in the PRD
6. Don't add complexity that isn't required by the PRD
</instructions>
