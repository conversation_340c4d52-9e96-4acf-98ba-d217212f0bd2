<?php

// namespace App\Settings;

// use <PERSON><PERSON><PERSON><PERSON>alleh\FilamentShield\Traits\HasPageShield;
// use Spatie\LaravelSettings\Migrations\SettingsMigration;

// class SiteSetting extends SettingsMigration
// {
//     public string $site_name;

//     public bool $site_active;
//     public bool $registration_enabled;
//     public bool $login_enabled;
//     public bool $password_reset_enabled;
//     public bool $sso_enabled;

//     public static function group(): string
//     {
//         return 'SiteSetting';
//     }

//     public function up(): void
//     {
//         $this->migrator->add('SiteSetting.site_name', 'Default Site Name');
//         $this->migrator->add('SiteSetting.site_active', true);
//         $this->migrator->add('SiteSetting.registration_enabled', true);
//         $this->migrator->add('SiteSetting.login_enabled', true);
//         $this->migrator->add('SiteSetting.password_reset_enabled', true);
//         $this->migrator->add('SiteSetting.sso_enabled', false);
//     }
// }


namespace App\Settings;

use Spatie\LaravelSettings\Settings;

class SiteSetting extends Settings
{
    public string $site_name = 'Default Site Name';
    public bool $site_active = true;
    public bool $registration_enabled = true;
    public bool $login_enabled = true;
    public bool $password_reset_enabled = true;
    public bool $sso_enabled = false;

    public static function group(): string
    {
        return 'SiteSetting';
    }
}
