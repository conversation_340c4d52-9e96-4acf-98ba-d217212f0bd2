<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ClientResource\Pages;
use App\Filament\Resources\ClientResource\RelationManagers;
use App\Models\Client;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ClientResource extends Resource
{
    protected static ?string $model = Client::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // If user has BDE role, only show clients associated with their projects
        if (auth()->check() && auth()->user()->hasRole('bde')) {
            $query->whereHas('projects', function (Builder $query) {
                $query->where('user_id', auth()->id());
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Company Details')
                    ->schema([
                        Forms\Components\TextInput::make('company_name')
                            ->label('Company Name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('tax_id')
                            ->label('Tax ID / GST')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('registered_address')
                            ->label('Registered Address')
                            ->required()
                            ->maxLength(65535),
                        Forms\Components\TextInput::make('company_email')
                            ->label('Company Email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('company_number')
                            ->label('Company Number')
                            ->tel()
                            ->required()
                            ->maxLength(20),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('Personnel Details')
                    ->schema([
                        Forms\Components\Repeater::make('personnel_details')
                            ->label('Personnel')
                            ->schema([
                                Forms\Components\TextInput::make('name')->label('Name')->maxLength(255),
                                Forms\Components\TextInput::make('official_email')->label('Official Email')->email()->maxLength(255),
                                Forms\Components\TextInput::make('mobile_number')->label('Mobile No.')->tel()->maxLength(20),
                                Forms\Components\TextInput::make('whatsapp_number')->label('WhatsApp No.')->tel()->maxLength(20),
                                Forms\Components\TextInput::make('skype')->label('Skype')->maxLength(255),
                                Forms\Components\TextInput::make('designation')->label('Designation')->maxLength(255),
                                Forms\Components\TextInput::make('department')->label('Department')->maxLength(255),
                            ])
                            ->columns(2)
                            ->minItems(1)
                            ->maxItems(3)
                            ->default([
                                ['name' => '', 'official_email' => '', 'mobile_number' => '', 'whatsapp_number' => '', 'skype' => '', 'designation' => '', 'department' => ''],
                            ]),
                    ]),
                Forms\Components\Section::make('Social Media Access')
                    ->schema([
                        Forms\Components\Repeater::make('social_media_access')
                            ->label('Access to Social Media Accounts')
                            ->schema([
                                Forms\Components\Select::make('platform')
                                    ->options([
                                        'instagram' => 'Instagram',
                                        'youtube' => 'YouTube',
                                        'twitter' => 'Twitter',
                                        'facebook' => 'Facebook',
                                        'gmb' => 'GMB',
                                        'linkedin' => 'LinkedIn',
                                        'website_gsa' => 'Website - GSA',
                                        'website_ga' => 'Website - GA',
                                    ])
                                    ->required(),
                                Forms\Components\TextInput::make('username')
                                    ->label('Username / Gmail ID / Status')
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('password')
                                    ->label('Password / Status')
                                    ->maxLength(255),
                            ])
                            ->columns(3)
                            ->default([
                                ['platform' => 'instagram', 'username' => '', 'password' => ''],
                                ['platform' => 'youtube', 'username' => '', 'password' => ''],
                                ['platform' => 'twitter', 'username' => '', 'password' => ''],
                                ['platform' => 'website_gsa', 'username' => '', 'password' => ''],
                                ['platform' => 'website_ga', 'username' => '', 'password' => ''],
                                ['platform' => 'facebook', 'username' => '', 'password' => ''],
                                ['platform' => 'gmb', 'username' => '', 'password' => ''],
                                ['platform' => 'linkedin', 'username' => '', 'password' => ''],
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('company_name')->label('Name')->searchable(),
                Tables\Columns\TextColumn::make('company_email')->label('Email')->searchable(),
                Tables\Columns\TextColumn::make('company_number')->label('Number')->searchable(),
                // Tables\Columns\TextColumn::make('name')
                //     ->searchable()
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('email')
                //     ->searchable(),
                // Tables\Columns\TextColumn::make('phone')
                //     ->searchable(),
                // Tables\Columns\TextColumn::make('contact_person')
                //     ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        'lead' => 'warning',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('projects_count')
                    ->counts('projects')
                    ->label('Projects')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->url(fn($record) => route('filament.resources.clients.view', ['record' => $record->getKey()]))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ProjectsRelationManager::class,
            RelationManagers\MilestonesRelationManager::class,
            RelationManagers\PaymentsRelationManager::class,
            RelationManagers\IncentivesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClients::route('/'),
            'create' => Pages\CreateClient::route('/create'),
            'edit' => Pages\EditClient::route('/{record}/edit'),
            'view' => Pages\ViewClient::route('/{record}/view'),
        ];
    }
}
