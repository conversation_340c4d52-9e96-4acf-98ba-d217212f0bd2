<?php

namespace App\Filament\Widgets\NotificationComponents;

use App\Models\AppNotification;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class NotificationBell extends Component
{
    public $unreadCount = 0;
    
    protected $listeners = [
        'notification-read' => 'updateUnreadCount',
        'all-notifications-read' => 'updateUnreadCount',
    ];
    
    public function mount()
    {
        $this->updateUnreadCount();
    }
    
    public function updateUnreadCount()
    {
        $this->unreadCount = AppNotification::where('user_id', Auth::id())
            ->whereNull('read_at')
            ->count();
    }
    
    public function getNotifications()
    {
        return AppNotification::where('user_id', Auth::id())
            ->whereNull('read_at')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
    }
    
    public function markAsRead($notificationId)
    {
        $notification = AppNotification::find($notificationId);
        
        if ($notification && $notification->user_id === Auth::id()) {
            $notification->markAsRead();
            $this->updateUnreadCount();
        }
    }
    
    public function markAllAsRead()
    {
        AppNotification::where('user_id', Auth::id())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
            
        $this->updateUnreadCount();
    }
    
    public function render()
    {
        return view('filament.widgets.notification-components.notification-bell');
    }
}
