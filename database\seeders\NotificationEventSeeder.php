<?php

namespace Database\Seeders;

use App\Models\NotificationEvent;
use Illuminate\Database\Seeder;

class NotificationEventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Project notifications
        $this->createNotificationEvent(
            'project_created',
            'Project Created',
            'projects',
            'Sent when a new project is created',
            true,
            true,
            [
                'project_id' => 'ID of the project',
                'project_title' => 'Title of the project',
                'client_name' => 'Name of the client',
            ]
        );

        $this->createNotificationEvent(
            'project_updated',
            'Project Updated',
            'projects',
            'Sent when a project is updated',
            true,
            false,
            [
                'project_id' => 'ID of the project',
                'project_title' => 'Title of the project',
            ]
        );

        $this->createNotificationEvent(
            'project_completed',
            'Project Completed',
            'projects',
            'Sent when a project is marked as completed',
            true,
            true,
            [
                'project_id' => 'ID of the project',
                'project_title' => 'Title of the project',
            ]
        );

        // Client notifications
        $this->createNotificationEvent(
            'client_created',
            'Client Created',
            'clients',
            'Sent when a new client is created',
            true,
            true,
            [
                'client_id' => 'ID of the client',
                'client_name' => 'Name of the client',
            ]
        );

        // Payment notifications
        $this->createNotificationEvent(
            'payment_due',
            'Payment Due',
            'payments',
            'Sent when a payment is due',
            true,
            true,
            [
                'payment_id' => 'ID of the payment',
                'payment_amount' => 'Amount of the payment',
                'project_title' => 'Title of the project',
                'due_date' => 'Due date of the payment',
            ]
        );

        $this->createNotificationEvent(
            'payment_received',
            'Payment Received',
            'payments',
            'Sent when a payment is received',
            true,
            true,
            [
                'payment_id' => 'ID of the payment',
                'payment_amount' => 'Amount of the payment',
                'project_title' => 'Title of the project',
            ]
        );

        // Milestone notifications
        $this->createNotificationEvent(
            'milestone_completed',
            'Milestone Completed',
            'milestones',
            'Sent when a milestone is completed',
            true,
            false,
            [
                'milestone_id' => 'ID of the milestone',
                'milestone_title' => 'Title of the milestone',
                'project_title' => 'Title of the project',
            ]
        );

        // Incentive notifications
        $this->createNotificationEvent(
            'incentive_approved',
            'Incentive Approved',
            'incentives',
            'Sent when an incentive is approved',
            true,
            true,
            [
                'incentive_id' => 'ID of the incentive',
                'incentive_amount' => 'Amount of the incentive',
                'project_title' => 'Title of the project',
            ]
        );

        // User notifications
        $this->createNotificationEvent(
            'user_created',
            'User Created',
            'users',
            'Sent when a new user is created',
            true,
            true,
            [
                'user_id' => 'ID of the user',
                'user_name' => 'Name of the user',
                'user_email' => 'Email of the user',
            ]
        );

        // System notifications
        $this->createNotificationEvent(
            'system_backup_completed',
            'System Backup Completed',
            'system',
            'Sent when a system backup is completed',
            true,
            false,
            [
                'backup_size' => 'Size of the backup',
                'backup_date' => 'Date of the backup',
            ]
        );
    }

    /**
     * Create a notification event.
     */
    private function createNotificationEvent(
        string $name,
        string $displayName,
        string $module,
        string $description,
        bool $isActive,
        bool $isHierarchical,
        array $dataSchema = []
    ): void {
        NotificationEvent::create([
            'name' => $name,
            'display_name' => $displayName,
            'module' => $module,
            'description' => $description,
            'is_active' => $isActive,
            'is_hierarchical' => $isHierarchical,
            'data_schema' => $dataSchema,
        ]);
    }
}
