<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('notification_event_id')->constrained('notification_events')->onDelete('cascade');
            $table->boolean('email')->default(true);       // Whether to send email notifications
            $table->boolean('in_app')->default(true);      // Whether to send in-app notifications
            $table->boolean('is_enabled')->default(true);  // Whether this notification is enabled for this user
            $table->boolean('override_role')->default(false); // Whether this overrides role settings
            $table->timestamps();

            // Unique constraint on user_id and notification_event_id
            $table->unique(['user_id', 'notification_event_id'], 'user_notification_event_unique');
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_preferences');
    }
};
