<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProjectTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\ProjectType::create([
            'name' => 'Website Development',
            'description' => 'Full website development projects including design and implementation',
        ]);

        \App\Models\ProjectType::create([
            'name' => 'Digital Marketing',
            'description' => 'Digital marketing services including SEO, SEM, and social media marketing',
        ]);

        \App\Models\ProjectType::create([
            'name' => 'Mobile App',
            'description' => 'Mobile application development for iOS and Android platforms',
        ]);

        \App\Models\ProjectType::create([
            'name' => 'E-commerce',
            'description' => 'E-commerce website development and maintenance',
        ]);

        \App\Models\ProjectType::create([
            'name' => 'Server Management',
            'description' => 'Server setup, configuration, and maintenance services',
        ]);
    }
}
