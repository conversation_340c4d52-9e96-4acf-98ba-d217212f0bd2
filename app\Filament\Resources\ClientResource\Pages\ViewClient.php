<?php

namespace App\Filament\Resources\ClientResource\Pages;

use App\Filament\Resources\ClientResource;
use App\Models\Client;
use Filament\Resources\Pages\Page;

class ViewClient extends Page
{
    protected static string $resource = ClientResource::class;
    protected static string $view = 'filament.resources.client-resource.pages.view-client';

    public $record;

    public function mount($record): void
    {
        $this->record = Client::with(['projects.milestones.incentives'])->findOrFail($record);
    }

    public static function getRouteName(?string $panel = null): string
    {
        return 'filament.resources.clients.view';
    }

    public function render(): \Illuminate\Contracts\View\View
    {
        return view(static::$view, [
            'record' => $this->record,
        ]);
    }
}
