<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all milestones
        $milestones = \App\Models\Milestone::all();

        // Create payments for each milestone
        foreach ($milestones as $milestone) {
            $project = $milestone->project;

            // Create payment for the milestone
            \App\Models\Payment::create([
                'project_id' => $project->id,
                'milestone_id' => $milestone->id,
                'amount' => $milestone->amount,
                'due_date' => $milestone->due_date,
                'paid_date' => $milestone->status === 'completed' ? $milestone->due_date : null,
                'status' => $milestone->status === 'completed' ? 'paid' : 'pending',
                'payment_method' => $milestone->status === 'completed' ? 'bank_transfer' : null,
                'transaction_id' => $milestone->status === 'completed' ? 'TXN' . rand(100000, 999999) : null,
                'notes' => $milestone->status === 'completed' ? 'Payment received on time' : 'Payment pending',
            ]);
        }

        // Create some additional payments without milestones (e.g., maintenance fees)
        $projects = \App\Models\Project::all();

        foreach ($projects as $index => $project) {
            if ($index % 2 === 0) { // Only for some projects
                \App\Models\Payment::create([
                    'project_id' => $project->id,
                    'milestone_id' => null,
                    'amount' => $project->total_payment * 0.1, // 10% maintenance fee
                    'due_date' => now()->addDays(30),
                    'paid_date' => null,
                    'status' => 'pending',
                    'payment_method' => null,
                    'transaction_id' => null,
                    'notes' => 'Additional maintenance fee',
                ]);
            }
        }
    }
}
