<?php

namespace App\Filament\Resources\NotificationEventResource\Pages;

use App\Filament\Resources\NotificationEventResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateNotificationEvent extends CreateRecord
{
    protected static string $resource = NotificationEventResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
