models:
  User:
    name: string
    email: string unique
    password: string
    last_login_at: nullable timestamp
    remember_token: nullable string
    relationships:
      belongsTo: Role
      hasMany: Project, Notification, NotificationPreference, Incentive

  Role:
    name: string
    description: nullable string
    relationships:
      hasMany: User
      belongsToMany: Permission

  Permission:
    name: string
    description: nullable string
    relationships:
      belongsToMany: Role

  Client:
    name: string
    email: string
    phone: nullable string
    address: nullable text
    contact_person: nullable string
    status: string default:active
    relationships:
      hasMany: Project

  Project:
    client_id: id foreign
    user_id: id foreign:users
    project_type_id: id foreign
    title: string
    description: nullable text
    won_date: date
    start_date: date
    end_date: nullable date
    total_payment: decimal:10,2
    duration: integer
    duration_unit: string
    payment_cycle: string
    status: string default:active
    relationships:
      belongsTo: Client, User, ProjectType
      hasMany: Milestone, Payment, Incentive

  ProjectType:
    name: string
    description: nullable text
    relationships:
      hasMany: Project, IncentiveRule

  Milestone:
    project_id: id foreign
    title: string
    description: nullable text
    due_date: date
    percentage: decimal:5,2
    amount: decimal:10,2
    status: string default:pending
    relationships:
      belongsTo: Project

  Payment:
    project_id: id foreign
    milestone_id: nullable id foreign
    amount: decimal:10,2
    due_date: date
    paid_date: nullable date
    status: string default:pending
    payment_method: nullable string
    transaction_id: nullable string
    notes: nullable text
    relationships:
      belongsTo: Project, Milestone

  Incentive:
    user_id: id foreign
    project_id: id foreign
    amount: decimal:10,2
    calculation_date: date
    payment_date: nullable date
    status: string default:pending
    description: nullable text
    approved_by: nullable id foreign:users
    relationships:
      belongsTo: User, Project

  IncentiveRule:
    project_type_id: id foreign
    role_id: id foreign
    percentage: decimal:5,2
    duration_percentage: decimal:5,2
    fixed_amount: nullable decimal:10,2
    currency: string default:INR
    calculation_type: string
    relationships:
      belongsTo: ProjectType, Role

  Notification:
    user_id: id foreign
    title: string
    message: text
    type: string
    read_at: nullable timestamp
    data: nullable json
    relationships:
      belongsTo: User

  NotificationPreference:
    user_id: id foreign
    notification_type: string
    email: boolean default:true
    in_app: boolean default:true
    relationships:
      belongsTo: User

  CurrencyRate:
    from_currency: string
    to_currency: string
    rate: decimal:10,6
    last_updated_at: timestamp

  Report:
    user_id: id foreign
    title: string
    type: string
    parameters: json
    created_at: timestamp
    file_path: nullable string
    relationships:
      belongsTo: User

controllers:
  Dashboard:
    index:
      render: dashboard

  ProjectController:
    index:
      query: Project
      render: project.index

    create:
      render: project.create

    store:
      validate: client_id, user_id, project_type_id, title, won_date, start_date, total_payment, duration, duration_unit, payment_cycle
      save: Project
      fire: ProjectCreated
      redirect: project.show

    show:
      render: project.show with:project

    edit:
      render: project.edit with:project

    update:
      validate: client_id, user_id, project_type_id, title, won_date, start_date, total_payment, duration, duration_unit, payment_cycle
      update: project
      redirect: project.show

  ClientController:
    index:
      query: Client
      render: client.index

    create:
      render: client.create

    store:
      validate: name, email
      save: Client
      redirect: client.index

    show:
      render: client.show with:client

    edit:
      render: client.edit with:client

    update:
      validate: name, email
      update: client
      redirect: client.show

  MilestoneController:
    index:
      query: Milestone
      render: milestone.index

    store:
      validate: project_id, title, due_date, percentage, amount
      save: Milestone
      redirect: project.show

    update:
      validate: title, due_date, percentage, amount, status
      update: milestone
      redirect: project.show

  PaymentController:
    index:
      query: Payment
      render: payment.index

    store:
      validate: project_id, milestone_id, amount, due_date
      save: Payment
      redirect: payment.index

    update:
      validate: amount, due_date, status, paid_date
      update: payment
      redirect: payment.index

  IncentiveController:
    index:
      query: Incentive
      render: incentive.index

    calculate:
      render: incentive.calculate

    processCalculation:
      redirect: incentive.preview

    preview:
      render: incentive.preview

    approve:
      validate: incentive_ids
      update: incentive
      redirect: incentive.index

  ReportController:
    index:
      render: report.index

    totalSales:
      render: report.total_sales

    generateTotalSales:
      validate: start_date, end_date, grouping
      redirect: report.view

    bdePerformance:
      render: report.bde_performance

    generateBDEPerformance:
      validate: user_ids, start_date, end_date
      redirect: report.view

    revenueProjection:
      render: report.revenue_projection

    generateRevenueProjection:
      validate: months, project_types
      redirect: report.view

    view:
      render: report.view

    export:
      download: report

  NotificationController:
    index:
      query: Notification
      render: notification.index

    markAsRead:
      update: notification
      redirect: notification.index

    settings:
      render: notification.settings

    updateSettings:
      validate: notification_preferences
      save: NotificationPreference
      redirect: notification.settings

  UserController:
    index:
      query: User
      render: user.index

    create:
      render: user.create

    store:
      validate: name, email, password, role_id
      save: User
      redirect: user.index

    edit:
      render: user.edit with:user

    update:
      validate: name, email, role_id
      update: user
      redirect: user.index

    impersonate:
      redirect: dashboard
