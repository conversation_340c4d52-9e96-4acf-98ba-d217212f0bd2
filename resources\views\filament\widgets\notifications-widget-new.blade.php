<div>
    <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Notifications</h2>
        
        @if($this->getUnreadCount() > 0)
            <button 
                wire:click="markAllAsRead"
                class="text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200"
            >
                Mark all as read
            </button>
        @endif
    </div>
    
    <div class="space-y-4">
        @forelse($this->getNotifications() as $notification)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border-l-4 border-primary-500 hover:shadow-md transition-shadow duration-200">
                <div class="flex justify-between items-start">
                    <h3 class="font-medium text-gray-900 dark:text-white pr-4">{{ $notification->title }}</h3>
                    <button 
                        wire:click="markAsRead({{ $notification->id }})"
                        class="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-150 flex-shrink-0"
                    >
                        <x-heroicon-o-x-mark class="w-4 h-4" />
                    </button>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-2">{{ $notification->message }}</p>
                <div class="flex justify-between items-center mt-3">
                    <span class="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                        <x-heroicon-o-clock class="w-3 h-3 mr-1" />
                        {{ $notification->created_at->diffForHumans() }}
                    </span>
                    
                    <span class="text-xs px-2 py-1 rounded-full bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300 font-medium">
                        {{ ucfirst($notification->notificationEvent->module) }}
                    </span>
                </div>
            </div>
        @empty
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 text-center">
                <x-heroicon-o-bell-slash class="w-10 h-10 mx-auto mb-3 text-gray-400 dark:text-gray-500 opacity-70" />
                <p class="text-gray-500 dark:text-gray-400">No new notifications</p>
            </div>
        @endforelse
    </div>
    
    @if($this->getUnreadCount() > 0)
        <div class="mt-6 text-center">
            <a 
                href="{{ route('filament.admin.resources.app-notifications.index') }}" 
                class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200"
            >
                View all notifications ({{ $this->getUnreadCount() }})
                <x-heroicon-o-arrow-right class="w-4 h-4 ml-1" />
            </a>
        </div>
    @endif
</div>
