{"__meta": {"id": "01JXBA1SREXRD46VG09XEDHT4G", "datetime": "2025-06-09 21:37:54", "utime": **********.960113, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 17, "messages": [{"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving dashboard config {\n    \"user_id\": 1,\n    \"widget_data\": {\n        \"1\": {\n            \"enabled\": true,\n            \"position\": 1,\n            \"width\": 3,\n            \"height\": 1\n        },\n        \"2\": {\n            \"enabled\": true,\n            \"position\": 2,\n            \"width\": 3,\n            \"height\": 1\n        },\n        \"3\": {\n            \"enabled\": true,\n            \"position\": 3,\n            \"width\": 3,\n            \"height\": 1\n        },\n        \"4\": {\n            \"enabled\": true,\n            \"position\": 4,\n            \"width\": \"12\",\n            \"height\": \"3\"\n        },\n        \"5\": {\n            \"enabled\": true,\n            \"position\": 5,\n            \"width\": 12,\n            \"height\": 2\n        },\n        \"6\": {\n            \"enabled\": true,\n            \"position\": 6,\n            \"width\": 12,\n            \"height\": 2\n        },\n        \"7\": {\n            \"enabled\": true,\n            \"position\": 7,\n            \"width\": 6,\n            \"height\": 2\n        },\n        \"8\": {\n            \"enabled\": true,\n            \"position\": 8,\n            \"width\": \"8\",\n            \"height\": 2\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.343946, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving widget {\n    \"widget_id\": 1,\n    \"config\": {\n        \"enabled\": true,\n        \"position\": 1,\n        \"width\": 3,\n        \"height\": 1\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.346434, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saved config {\n    \"user_id\": 1,\n    \"widget_id\": 1,\n    \"config\": {\n        \"user_id\": 1,\n        \"widget_id\": 1,\n        \"is_enabled\": true,\n        \"position\": 1,\n        \"width\": 3,\n        \"height\": 1,\n        \"updated_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"id\": 17\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.36133, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving widget {\n    \"widget_id\": 2,\n    \"config\": {\n        \"enabled\": true,\n        \"position\": 2,\n        \"width\": 3,\n        \"height\": 1\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.363194, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saved config {\n    \"user_id\": 1,\n    \"widget_id\": 2,\n    \"config\": {\n        \"user_id\": 1,\n        \"widget_id\": 2,\n        \"is_enabled\": true,\n        \"position\": 2,\n        \"width\": 3,\n        \"height\": 1,\n        \"updated_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"id\": 18\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.365996, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving widget {\n    \"widget_id\": 3,\n    \"config\": {\n        \"enabled\": true,\n        \"position\": 3,\n        \"width\": 3,\n        \"height\": 1\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.367561, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saved config {\n    \"user_id\": 1,\n    \"widget_id\": 3,\n    \"config\": {\n        \"user_id\": 1,\n        \"widget_id\": 3,\n        \"is_enabled\": true,\n        \"position\": 3,\n        \"width\": 3,\n        \"height\": 1,\n        \"updated_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"id\": 19\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.370155, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving widget {\n    \"widget_id\": 4,\n    \"config\": {\n        \"enabled\": true,\n        \"position\": 4,\n        \"width\": \"12\",\n        \"height\": \"3\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.371456, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saved config {\n    \"user_id\": 1,\n    \"widget_id\": 4,\n    \"config\": {\n        \"user_id\": 1,\n        \"widget_id\": 4,\n        \"is_enabled\": true,\n        \"position\": 4,\n        \"width\": \"12\",\n        \"height\": \"3\",\n        \"updated_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"id\": 20\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.37456, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving widget {\n    \"widget_id\": 5,\n    \"config\": {\n        \"enabled\": true,\n        \"position\": 5,\n        \"width\": 12,\n        \"height\": 2\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.376052, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saved config {\n    \"user_id\": 1,\n    \"widget_id\": 5,\n    \"config\": {\n        \"user_id\": 1,\n        \"widget_id\": 5,\n        \"is_enabled\": true,\n        \"position\": 5,\n        \"width\": 12,\n        \"height\": 2,\n        \"updated_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"id\": 21\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.378692, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving widget {\n    \"widget_id\": 6,\n    \"config\": {\n        \"enabled\": true,\n        \"position\": 6,\n        \"width\": 12,\n        \"height\": 2\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.380152, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saved config {\n    \"user_id\": 1,\n    \"widget_id\": 6,\n    \"config\": {\n        \"user_id\": 1,\n        \"widget_id\": 6,\n        \"is_enabled\": true,\n        \"position\": 6,\n        \"width\": 12,\n        \"height\": 2,\n        \"updated_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"id\": 22\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.383419, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving widget {\n    \"widget_id\": 7,\n    \"config\": {\n        \"enabled\": true,\n        \"position\": 7,\n        \"width\": 6,\n        \"height\": 2\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.385603, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saved config {\n    \"user_id\": 1,\n    \"widget_id\": 7,\n    \"config\": {\n        \"user_id\": 1,\n        \"widget_id\": 7,\n        \"is_enabled\": true,\n        \"position\": 7,\n        \"width\": 6,\n        \"height\": 2,\n        \"updated_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"id\": 23\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.388549, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saving widget {\n    \"widget_id\": 8,\n    \"config\": {\n        \"enabled\": true,\n        \"position\": 8,\n        \"width\": \"8\",\n        \"height\": 2\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.390074, "xdebug_link": null, "collector": "log"}, {"message": "[21:37:54] LOG.info: [DASHBOARD_SETTINGS] Saved config {\n    \"user_id\": 1,\n    \"widget_id\": 8,\n    \"config\": {\n        \"user_id\": 1,\n        \"widget_id\": 8,\n        \"is_enabled\": true,\n        \"position\": 8,\n        \"width\": \"8\",\n        \"height\": 2,\n        \"updated_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n        \"id\": 24\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.392716, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749505072.819916, "end": **********.960168, "duration": 2.140251874923706, "duration_str": "2.14s", "measures": [{"label": "Booting", "start": 1749505072.819916, "relative_start": 0, "end": **********.789554, "relative_end": **********.789554, "duration": 0.****************, "duration_str": "970ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.789566, "relative_start": 0.****************, "end": **********.960171, "relative_end": 3.0994415283203125e-06, "duration": 1.***************, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.20118, "relative_start": 1.****************, "end": **********.207028, "relative_end": **********.207028, "duration": 0.005847930908203125, "duration_str": "5.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.pages.dashboard-settings", "start": **********.507242, "relative_start": 1.****************, "end": **********.507242, "relative_end": **********.507242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.5339, "relative_start": 1.****************, "end": **********.5339, "relative_end": **********.5339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.562087, "relative_start": 1.742171049118042, "end": **********.562087, "relative_end": **********.562087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.571461, "relative_start": 1.7515449523925781, "end": **********.571461, "relative_end": **********.571461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.590406, "relative_start": 1.7704899311065674, "end": **********.590406, "relative_end": **********.590406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.606128, "relative_start": 1.7862119674682617, "end": **********.606128, "relative_end": **********.606128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.621629, "relative_start": 1.801712989807129, "end": **********.621629, "relative_end": **********.621629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.640832, "relative_start": 1.820915937423706, "end": **********.640832, "relative_end": **********.640832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.653159, "relative_start": 1.8332428932189941, "end": **********.653159, "relative_end": **********.653159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.67643, "relative_start": 1.8565139770507812, "end": **********.67643, "relative_end": **********.67643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.687802, "relative_start": 1.8678860664367676, "end": **********.687802, "relative_end": **********.687802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.715082, "relative_start": 1.8951659202575684, "end": **********.715082, "relative_end": **********.715082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.731481, "relative_start": 1.9115650653839111, "end": **********.731481, "relative_end": **********.731481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.77436, "relative_start": 1.9544439315795898, "end": **********.77436, "relative_end": **********.77436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.779885, "relative_start": 1.9599690437316895, "end": **********.779885, "relative_end": **********.779885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.792571, "relative_start": 1.9726550579071045, "end": **********.792571, "relative_end": **********.792571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.822037, "relative_start": 2.0021209716796875, "end": **********.822037, "relative_end": **********.822037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.828148, "relative_start": 2.0082318782806396, "end": **********.828148, "relative_end": **********.828148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.835766, "relative_start": 2.015850067138672, "end": **********.835766, "relative_end": **********.835766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.841955, "relative_start": 2.0220389366149902, "end": **********.841955, "relative_end": **********.841955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.8483, "relative_start": 2.02838397026062, "end": **********.8483, "relative_end": **********.8483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.880349, "relative_start": 2.0604329109191895, "end": **********.880349, "relative_end": **********.880349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.886806, "relative_start": 2.066890001296997, "end": **********.886806, "relative_end": **********.886806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.896935, "relative_start": 2.077018976211548, "end": **********.896935, "relative_end": **********.896935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.913968, "relative_start": 2.0940520763397217, "end": **********.913968, "relative_end": **********.913968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.953343, "relative_start": 2.1334269046783447, "end": **********.955974, "relative_end": **********.955974, "duration": 0.0026311874389648438, "duration_str": "2.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 51148024, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 25, "nb_templates": 25, "templates": [{"name": "filament.pages.dashboard-settings", "param_count": null, "params": [], "start": **********.507213, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard-settings.blade.phpfilament.pages.dashboard-settings", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard-settings.blade.php&line=1", "ajax": false, "filename": "dashboard-settings.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.53388, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.562041, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.57144, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.590386, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.606102, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.621609, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.640814, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.65314, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.676411, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.687785, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.71506, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.731463, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.774343, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.77987, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.792552, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.822018, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.82813, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.835747, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.841938, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.848284, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.88033, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.886788, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.896917, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.913933, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}]}, "queries": {"count": 36, "nb_statements": 36, "nb_visible_statements": 36, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.040639999999999996, "accumulated_duration_str": "40.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq' limit 1", "type": "query", "params": [], "bindings": ["yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.217984, "duration": 0.0047599999999999995, "duration_str": "4.76ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 11.713}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.2370498, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.713, "width_percent": 10.974}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.245507, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.687, "width_percent": 4.897}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.252023, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.584, "width_percent": 3.396}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.254591, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.979, "width_percent": 6.225}, {"sql": "select * from `dashboard_widgets` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 76}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.300557, "duration": 0.0056500000000000005, "duration_str": "5.65ms", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:72", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=72", "ajax": false, "filename": "DashboardSettings.php", "line": "72"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.205, "width_percent": 13.903}, {"sql": "select * from `dashboard_widgets` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/EntanglesStateWithSingularRelationship.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\EntanglesStateWithSingularRelationship.php", "line": 161}], "start": **********.322363, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:72", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=72", "ajax": false, "filename": "DashboardSettings.php", "line": "72"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.107, "width_percent": 1.87}, {"sql": "select * from `dashboard_widgets` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 76}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.325707, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:72", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=72", "ajax": false, "filename": "DashboardSettings.php", "line": "72"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.977, "width_percent": 1.353}, {"sql": "select * from `dashboard_widgets` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/EntanglesStateWithSingularRelationship.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\EntanglesStateWithSingularRelationship.php", "line": 161}], "start": **********.3282342, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:72", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=72", "ajax": false, "filename": "DashboardSettings.php", "line": "72"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.331, "width_percent": 1.107}, {"sql": "select * from `dashboard_widgets` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 76}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.3306751, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:72", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=72", "ajax": false, "filename": "DashboardSettings.php", "line": "72"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.438, "width_percent": 1.181}, {"sql": "select * from `dashboard_widgets` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/EntanglesStateWithSingularRelationship.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\EntanglesStateWithSingularRelationship.php", "line": 161}], "start": **********.333074, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:72", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=72", "ajax": false, "filename": "DashboardSettings.php", "line": "72"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.619, "width_percent": 1.181}, {"sql": "select * from `dashboard_widgets` where `dashboard_widgets`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.344302, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:122", "source": {"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=122", "ajax": false, "filename": "DashboardSettings.php", "line": "122"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.8, "width_percent": 2.928}, {"sql": "select * from `dashboard_configs` where (`user_id` = 1 and `widget_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.347353, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.728, "width_percent": 3.15}, {"sql": "insert into `dashboard_configs` (`user_id`, `widget_id`, `is_enabled`, `position`, `width`, `height`, `updated_at`, `created_at`) values (1, 1, 1, 1, 3, 1, '2025-06-09 21:37:54', '2025-06-09 21:37:54')", "type": "query", "params": [], "bindings": [1, 1, 1, 1, 3, 1, "2025-06-09 21:37:54", "2025-06-09 21:37:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.349797, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.878, "width_percent": 8.932}, {"sql": "select * from `dashboard_widgets` where `dashboard_widgets`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3616002, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:122", "source": {"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=122", "ajax": false, "filename": "DashboardSettings.php", "line": "122"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.81, "width_percent": 1.649}, {"sql": "select * from `dashboard_configs` where (`user_id` = 1 and `widget_id` = 2) limit 1", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.363433, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.459, "width_percent": 0.96}, {"sql": "insert into `dashboard_configs` (`user_id`, `widget_id`, `is_enabled`, `position`, `width`, `height`, `updated_at`, `created_at`) values (1, 2, 1, 2, 3, 1, '2025-06-09 21:37:54', '2025-06-09 21:37:54')", "type": "query", "params": [], "bindings": [1, 2, 1, 2, 3, 1, "2025-06-09 21:37:54", "2025-06-09 21:37:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.364706, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.418, "width_percent": 0.886}, {"sql": "select * from `dashboard_widgets` where `dashboard_widgets`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.366191, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:122", "source": {"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=122", "ajax": false, "filename": "DashboardSettings.php", "line": "122"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.304, "width_percent": 1.427}, {"sql": "select * from `dashboard_configs` where (`user_id` = 1 and `widget_id` = 3) limit 1", "type": "query", "params": [], "bindings": [1, 3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.367764, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.731, "width_percent": 0.96}, {"sql": "insert into `dashboard_configs` (`user_id`, `widget_id`, `is_enabled`, `position`, `width`, `height`, `updated_at`, `created_at`) values (1, 3, 1, 3, 3, 1, '2025-06-09 21:37:54', '2025-06-09 21:37:54')", "type": "query", "params": [], "bindings": [1, 3, 1, 3, 3, 1, "2025-06-09 21:37:54", "2025-06-09 21:37:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.368954, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.691, "width_percent": 0.812}, {"sql": "select * from `dashboard_widgets` where `dashboard_widgets`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.370334, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:122", "source": {"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=122", "ajax": false, "filename": "DashboardSettings.php", "line": "122"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.503, "width_percent": 0.91}, {"sql": "select * from `dashboard_configs` where (`user_id` = 1 and `widget_id` = 4) limit 1", "type": "query", "params": [], "bindings": [1, 4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.37163, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.413, "width_percent": 0.763}, {"sql": "insert into `dashboard_configs` (`user_id`, `widget_id`, `is_enabled`, `position`, `width`, `height`, `updated_at`, `created_at`) values (1, 4, 1, 4, '12', '3', '2025-06-09 21:37:54', '2025-06-09 21:37:54')", "type": "query", "params": [], "bindings": [1, 4, 1, 4, "12", "3", "2025-06-09 21:37:54", "2025-06-09 21:37:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.372755, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.176, "width_percent": 2.116}, {"sql": "select * from `dashboard_widgets` where `dashboard_widgets`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.374778, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:122", "source": {"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=122", "ajax": false, "filename": "DashboardSettings.php", "line": "122"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.292, "width_percent": 1.156}, {"sql": "select * from `dashboard_configs` where (`user_id` = 1 and `widget_id` = 5) limit 1", "type": "query", "params": [], "bindings": [1, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.376251, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.449, "width_percent": 0.812}, {"sql": "insert into `dashboard_configs` (`user_id`, `widget_id`, `is_enabled`, `position`, `width`, `height`, `updated_at`, `created_at`) values (1, 5, 1, 5, 12, 2, '2025-06-09 21:37:54', '2025-06-09 21:37:54')", "type": "query", "params": [], "bindings": [1, 5, 1, 5, 12, 2, "2025-06-09 21:37:54", "2025-06-09 21:37:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.377454, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.261, "width_percent": 0.861}, {"sql": "select * from `dashboard_widgets` where `dashboard_widgets`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.37888, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:122", "source": {"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=122", "ajax": false, "filename": "DashboardSettings.php", "line": "122"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.122, "width_percent": 1.009}, {"sql": "select * from `dashboard_configs` where (`user_id` = 1 and `widget_id` = 6) limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.380362, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.131, "width_percent": 1.969}, {"sql": "insert into `dashboard_configs` (`user_id`, `widget_id`, `is_enabled`, `position`, `width`, `height`, `updated_at`, `created_at`) values (1, 6, 1, 6, 12, 2, '2025-06-09 21:37:54', '2025-06-09 21:37:54')", "type": "query", "params": [], "bindings": [1, 6, 1, 6, 12, 2, "2025-06-09 21:37:54", "2025-06-09 21:37:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3820379, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.099, "width_percent": 0.984}, {"sql": "select * from `dashboard_widgets` where `dashboard_widgets`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.383639, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:122", "source": {"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=122", "ajax": false, "filename": "DashboardSettings.php", "line": "122"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.084, "width_percent": 1.083}, {"sql": "select * from `dashboard_configs` where (`user_id` = 1 and `widget_id` = 7) limit 1", "type": "query", "params": [], "bindings": [1, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.385839, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.166, "width_percent": 0.935}, {"sql": "insert into `dashboard_configs` (`user_id`, `widget_id`, `is_enabled`, `position`, `width`, `height`, `updated_at`, `created_at`) values (1, 7, 1, 7, 6, 2, '2025-06-09 21:37:54', '2025-06-09 21:37:54')", "type": "query", "params": [], "bindings": [1, 7, 1, 7, 6, 2, "2025-06-09 21:37:54", "2025-06-09 21:37:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.38705, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.101, "width_percent": 1.28}, {"sql": "select * from `dashboard_widgets` where `dashboard_widgets`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.388741, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:122", "source": {"index": 20, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=122", "ajax": false, "filename": "DashboardSettings.php", "line": "122"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.381, "width_percent": 1.206}, {"sql": "select * from `dashboard_configs` where (`user_id` = 1 and `widget_id` = 8) limit 1", "type": "query", "params": [], "bindings": [1, 8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.390277, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.587, "width_percent": 0.837}, {"sql": "insert into `dashboard_configs` (`user_id`, `widget_id`, `is_enabled`, `position`, `width`, `height`, `updated_at`, `created_at`) values (1, 8, 1, 8, '8', 2, '2025-06-09 21:37:54', '2025-06-09 21:37:54')", "type": "query", "params": [], "bindings": [1, 8, 1, 8, "8", 2, "2025-06-09 21:37:54", "2025-06-09 21:37:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.391432, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:131", "source": {"index": 26, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=131", "ajax": false, "filename": "DashboardSettings.php", "line": "131"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.423, "width_percent": 0.861}, {"sql": "select * from `dashboard_widgets` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/EntanglesStateWithSingularRelationship.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\EntanglesStateWithSingularRelationship.php", "line": 161}], "start": **********.521307, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "DashboardSettings.php:72", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/DashboardSettings.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\DashboardSettings.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=72", "ajax": false, "filename": "DashboardSettings.php", "line": "72"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.284, "width_percent": 3.716}]}, "models": {"data": {"App\\Models\\DashboardWidget": {"value": 64, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FDashboardWidget.php&line=1", "ajax": false, "filename": "DashboardWidget.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 65, "is_counter": true}, "livewire": {"data": {"app.filament.pages.dashboard-settings #UJXCkspsCzUoR3c9iS4S": "array:4 [\n  \"data\" => array:15 [\n    \"data\" => array:1 [\n      \"widgets\" => array:8 [\n        1 => array:4 [\n          \"enabled\" => true\n          \"position\" => 1\n          \"width\" => 3\n          \"height\" => 1\n        ]\n        2 => array:4 [\n          \"enabled\" => true\n          \"position\" => 2\n          \"width\" => 3\n          \"height\" => 1\n        ]\n        3 => array:4 [\n          \"enabled\" => true\n          \"position\" => 3\n          \"width\" => 3\n          \"height\" => 1\n        ]\n        4 => array:4 [\n          \"enabled\" => true\n          \"position\" => 4\n          \"width\" => \"12\"\n          \"height\" => \"3\"\n        ]\n        5 => array:4 [\n          \"enabled\" => true\n          \"position\" => 5\n          \"width\" => 12\n          \"height\" => 2\n        ]\n        6 => array:4 [\n          \"enabled\" => true\n          \"position\" => 6\n          \"width\" => 12\n          \"height\" => 2\n        ]\n        7 => array:4 [\n          \"enabled\" => true\n          \"position\" => 7\n          \"width\" => 6\n          \"height\" => 2\n        ]\n        8 => array:4 [\n          \"enabled\" => true\n          \"position\" => 8\n          \"width\" => \"8\"\n          \"height\" => 2\n        ]\n      ]\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.dashboard-settings\"\n  \"component\" => \"App\\Filament\\Pages\\DashboardSettings\"\n  \"id\" => \"UJXCkspsCzUoR3c9iS4S\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Pages\\DashboardSettings@save<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=111\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboardSettings.php&line=111\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Filament/Pages/DashboardSettings.php:111-155</a>", "middleware": "web", "duration": "2.14s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1848422608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1848422608\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2140715682 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XvwSEK2nMJ21GwvXngu8OVdgkuV0g1LmSoHEyXMn</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1476 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;widgets&quot;:[{&quot;1&quot;:[{&quot;enabled&quot;:true,&quot;position&quot;:1,&quot;width&quot;:3,&quot;height&quot;:1},{&quot;s&quot;:&quot;arr&quot;}],&quot;2&quot;:[{&quot;enabled&quot;:true,&quot;position&quot;:2,&quot;width&quot;:3,&quot;height&quot;:1},{&quot;s&quot;:&quot;arr&quot;}],&quot;3&quot;:[{&quot;enabled&quot;:true,&quot;position&quot;:3,&quot;width&quot;:3,&quot;height&quot;:1},{&quot;s&quot;:&quot;arr&quot;}],&quot;4&quot;:[{&quot;enabled&quot;:true,&quot;position&quot;:4,&quot;width&quot;:3,&quot;height&quot;:1},{&quot;s&quot;:&quot;arr&quot;}],&quot;5&quot;:[{&quot;enabled&quot;:true,&quot;position&quot;:5,&quot;width&quot;:12,&quot;height&quot;:2},{&quot;s&quot;:&quot;arr&quot;}],&quot;6&quot;:[{&quot;enabled&quot;:true,&quot;position&quot;:6,&quot;width&quot;:12,&quot;height&quot;:2},{&quot;s&quot;:&quot;arr&quot;}],&quot;7&quot;:[{&quot;enabled&quot;:true,&quot;position&quot;:7,&quot;width&quot;:6,&quot;height&quot;:2},{&quot;s&quot;:&quot;arr&quot;}],&quot;8&quot;:[{&quot;enabled&quot;:true,&quot;position&quot;:8,&quot;width&quot;:6,&quot;height&quot;:2},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;UJXCkspsCzUoR3c9iS4S&quot;,&quot;name&quot;:&quot;app.filament.pages.dashboard-settings&quot;,&quot;path&quot;:&quot;admin\\/dashboard-settings&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;2f3fbcddca871c8c2a3dcafc0a0e38d0ce0165a0c9352cedf6e45b2e3b57f284&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.widgets.4.width</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n        \"<span class=sf-dump-key>data.widgets.4.height</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>data.widgets.8.width</span>\" => \"<span class=sf-dump-str>8</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140715682\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-837932980 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://localhost:8000/admin/dashboard-settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1934</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1269 characters\">sb-updates=3.7.1; kit_session=eyJpdiI6Ik94VUFKK1NoYW5lNUlVaEh5S21lc1E9PSIsInZhbHVlIjoiNWpIdkVYS012UTRNSHR1WnYvb2trWXgrdjhwcmlWWnd2TDRwZ1ErSEFRc01tQXc3emwvNTVGQ1BEU1RkbFlVcXlQMG9TV2Z1UmVEaWg2SGxCQ1Y1cFVXV0RaN0NUNTRnSVVxd3FMTnl0ZjI1bnJsRUZhSG5WQVFnSEtjWVJZeGEiLCJtYWMiOiJiM2ZlYTdkMmU5YTU5YzM4MWUyNzdjNGUyYTJjN2RiZTY3ODc4OTI1ODcxMDkwNzUwMmJiODAzNTgwZjVjMmE2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRlS2JDYWtMOXlCc0pPVWpCem5GQ0E9PSIsInZhbHVlIjoicDY5ay93VjAyNEI3MTNrZHUyckt6L0huUUEzZWxpOFNhTXVOSjNaS2Vjbm9xbHRoa1YzbjlrTG56cktRK3pFY3BUUS9pdE1MZ2RnbFN0Mlc3eFd5Y250VEM1SkY2YlJpODJ1cTY4SFV3YWNRRHp0ZE84N3hwSkJwOHIvQXl0YTciLCJtYWMiOiIyMzhmMTI5NDIzYTM3YWJhYmYyMWNlYjBjMWNmNzM1OTBjZTg4OTZiNzA3MGFkZmMxODFlY2QxZGUzMTQ0ZmU4IiwidGFnIjoiIn0%3D; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ii9BZkRxeXlQaENwK3RNSzFMTkV5Z2c9PSIsInZhbHVlIjoiNnBFelQwSGc4dXN1RXU2VEpXSHJjTVVsQUpucFdjVU9oeGVkUnhJZzBySGxLbHRQTFFHNldrVjhJcHpsUzV0c3ZSOFNlWmxkTUJoWFFaNlM3MTdzc0l4Q1JwV0pnS1JrTVlLdDJ6L0FwUEI4YTFZSkMyNExzTnoxWXJRZk9Wa1pMcHhoVjZiQ0F0WXFlUlU1bm9NaDVjSDJoVVVyWWhVZ0V1b2xMZFRkdXZlWWx6ak9qbjVoS3VXa3RFSmhScEl1UmY0TlZ0a0RIUm16am83MVRzaitxQmxSWHFGc3F2QlZVMW9kclB6UExUND0iLCJtYWMiOiI4NDIzOThjOTA3MWZkN2E0NjgxZjQ1MDQ0NDg4NzgyOTg0NDFjYzg1ZTRlZGNhM2FiNThhZDBmYzIzODNjNjQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=4</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837932980\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-868827943 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sb-updates</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XvwSEK2nMJ21GwvXngu8OVdgkuV0g1LmSoHEyXMn</span>\"\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IaoVXF77cb5deCAtwKpLOWhQkSz6v8KjZyt6VaEz3KMxX2I2xibuZaQaf80m|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868827943\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2050297558 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 09 Jun 2025 21:37:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050297558\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1264853080 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XvwSEK2nMJ21GwvXngu8OVdgkuV0g1LmSoHEyXMn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost:8000/admin/dashboard-settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f1dd6d9-7fa3-4218-9c0f-1f3094d56f0d</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Dashboard settings saved successfully</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264853080\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}