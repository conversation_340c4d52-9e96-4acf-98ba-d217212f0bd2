<?php

namespace App\Filament\Resources\ClientResource\Pages;

use App\Filament\Resources\ClientResource;
use App\Filament\Resources\ClientResource\RelationManagers\ProjectsRelationManager;
use App\Filament\Resources\ClientResource\RelationManagers\MilestonesRelationManager;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditClient extends EditRecord
{
    protected static string $resource = ClientResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getRelations(): array 
    {
        return [
            ProjectsRelationManager::class,
            MilestonesRelationManager::class,
        ];
    }
}
