<?php

namespace App\Filament\Resources\ProjectResource\Pages;

use App\Filament\Resources\ProjectResource;
use Filament\Facades\Filament;
use Filament\Resources\Pages\CreateRecord;

class CreateProject extends CreateRecord
{
    protected static string $resource = ProjectResource::class;
    protected static bool $canCreateAnother = false;

    // Set default values for the form
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // If user_id is not set, use the current authenticated user
        if (!isset($data['user_id']) || empty($data['user_id'])) {
            $data['user_id'] = Filament::auth()->user()->id;
        }

        return $data;
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
