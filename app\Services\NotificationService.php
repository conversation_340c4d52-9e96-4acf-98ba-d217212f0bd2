<?php

namespace App\Services;

use App\Models\AppNotification;
use App\Models\NotificationEvent;
use App\Models\Role;
use App\Models\RoleNotificationSettings;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send a notification to users based on their roles and preferences.
     *
     * @param string $eventName The name of the notification event
     * @param array $data Additional data for the notification
     * @param User|null $specificUser Send to a specific user only
     * @return Collection The created notifications
     */
    public function send(string $eventName, array $data = [], ?User $specificUser = null): Collection
    {
        // Find the notification event
        $event = NotificationEvent::where('name', $eventName)->first();

        if (!$event) {
            \Log::error("[NOTIFICATION_SERVICE] Notification event not found: {$eventName}", [
                'eventName' => $eventName,
                'data' => $data,
                'specificUser' => $specificUser?->id,
            ]);
            return collect();
        }

        if (!$event->is_active) {
            \Log::info("[NOTIFICATION_SERVICE] Notification event is not active: {$eventName}", [
                'eventName' => $eventName,
                'data' => $data,
                'specificUser' => $specificUser?->id,
            ]);
            return collect();
        }

        \Log::info('[NOTIFICATION_SERVICE] Sending notification', [
            'event' => $eventName,
            'user_ids' => $specificUser ? [$specificUser->id] : $this->getUsersToNotify($event, $specificUser)->pluck('id')->toArray(),
            'data' => $data,
        ]);

        // Get the users to notify
        $users = $this->getUsersToNotify($event, $specificUser);

        // Create and send notifications
        $notifications = collect();

        foreach ($users as $user) {
            // Check user preferences
            $preference = $user->notificationPreferences()
                ->where('notification_event_id', $event->id)
                ->first();

            // If user has preferences and they override role settings
            if ($preference && $preference->override_role) {
                // If the notification is disabled for this user, skip
                if (!$preference->is_enabled) {
                    continue;
                }

                // Create the notification based on user preferences
                if ($preference->in_app) {
                    $notification = $this->createNotification($event, $user, $data);
                    $notifications->push($notification);
                }

                // TODO: Send email notification if preference->email is true

            } else {
                // Get role notification settings
                $roleIds = $user->roles()->pluck('id');
                $roleSettings = RoleNotificationSettings::whereIn('role_id', $roleIds)->get();

                // Check if any role has this notification enabled
                $notificationEnabled = false;
                $emailEnabled = false;
                $inAppEnabled = false;

                foreach ($roleSettings as $roleSetting) {
                    if ($roleSetting->isEventEnabled($event->id)) {
                        $notificationEnabled = true;

                        if ($roleSetting->isEmailEnabled($event->id)) {
                            $emailEnabled = true;
                        }

                        if ($roleSetting->isInAppEnabled($event->id)) {
                            $inAppEnabled = true;
                        }
                    }
                }

                // If notification is not enabled for any role, skip
                if (!$notificationEnabled) {
                    continue;
                }

                // Create in-app notification if enabled
                if ($inAppEnabled) {
                    $notification = $this->createNotification($event, $user, $data);
                    $notifications->push($notification);
                }

                // TODO: Send email notification if email is enabled

                // TODO: Send email notification if any role has email notifications enabled
            }
        }

        return $notifications;
    }

    /**
     * Get the users to notify based on the event and hierarchy.
     *
     * @param NotificationEvent $event The notification event
     * @param User|null $specificUser Send to a specific user only
     * @return Collection The users to notify
     */
    private function getUsersToNotify(NotificationEvent $event, ?User $specificUser = null): Collection
    {
        // If a specific user is provided, only notify that user
        if ($specificUser) {
            return collect([$specificUser]);
        }

        // Get all users with roles that have this event enabled
        $roleIds = RoleNotificationSettings::whereRaw("JSON_EXTRACT(notification_settings, '$.\"" . intval($event->id) . "\"') IS NOT NULL")
            ->pluck('role_id');

        // If the event is hierarchical, we need to handle the hierarchy
        if ($event->is_hierarchical) {
            // Define the hierarchy: Admin > Manager > Employee
            $hierarchy = [
                'super_admin' => 3,
                'admin' => 2,
                'manager' => 1,
                'employee' => 0,
            ];

            // Find the highest role level that should receive this notification
            $highestLevel = 0;
            $roles = Role::whereIn('id', $roleIds)->get();

            foreach ($roles as $role) {
                $level = $hierarchy[$role->name] ?? 0;
                $highestLevel = max($highestLevel, $level);
            }

            // Get all roles at or above the highest level
            $hierarchicalRoleNames = array_filter($hierarchy, fn($level) => $level >= $highestLevel);
            $hierarchicalRoleNames = array_keys($hierarchicalRoleNames);

            // Get users with these roles
            return User::whereHas('roles', function ($query) use ($hierarchicalRoleNames) {
                $query->whereIn('name', $hierarchicalRoleNames);
            })->get();
        }

        // If not hierarchical, just get users with the specified roles
        return User::whereHas('roles', function ($query) use ($roleIds) {
            $query->whereIn('id', $roleIds);
        })->get();
    }

    /**
     * Create a notification for a user.
     *
     * @param NotificationEvent $event The notification event
     * @param User $user The user to notify
     * @param array $data Additional data for the notification
     * @return AppNotification The created notification
     */
    private function createNotification(NotificationEvent $event, User $user, array $data = []): AppNotification
    {
        // Generate title and message based on the event and data
        $title = $this->generateTitle($event, $data);
        $message = $this->generateMessage($event, $data);

        // Create the notification
        $notification = new AppNotification([
            'user_id' => $user->id,
            'notification_event_id' => $event->id,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        $notification->save();

        return $notification;
    }

    /**
     * Generate a title for the notification.
     *
     * @param NotificationEvent $event The notification event
     * @param array $data Additional data for the notification
     * @return string The generated title
     */
    private function generateTitle(NotificationEvent $event, array $data = []): string
    {
        // For now, just use the display name of the event
        return $event->display_name;
    }

    /**
     * Generate a message for the notification.
     *
     * @param NotificationEvent $event The notification event
     * @param array $data Additional data for the notification
     * @return string The generated message
     */
    private function generateMessage(NotificationEvent $event, array $data = []): string
    {
        // Generate a message based on the event and data
        switch ($event->name) {
            case 'project_created':
                return "A new project '{$data['project_title']}' has been created for client '{$data['client_name']}'.";

            case 'project_updated':
                return "Project '{$data['project_title']}' has been updated.";

            case 'project_completed':
                return "Project '{$data['project_title']}' has been marked as completed.";

            case 'client_created':
                return "A new client '{$data['client_name']}' has been created.";

            case 'payment_due':
                return "Payment of ₹{$data['payment_amount']} for project '{$data['project_title']}' is due on {$data['due_date']}.";

            case 'payment_received':
                return "Payment of ₹{$data['payment_amount']} for project '{$data['project_title']}' has been received.";

            case 'milestone_completed':
                return "Milestone '{$data['milestone_title']}' for project '{$data['project_title']}' has been completed.";

            case 'incentive_approved':
                return "Incentive of ₹{$data['incentive_amount']} for project '{$data['project_title']}' has been approved.";

            case 'user_created':
                return "A new user '{$data['user_name']}' has been created with email '{$data['user_email']}'.";

            case 'system_backup_completed':
                return "System backup of size {$data['backup_size']} has been completed on {$data['backup_date']}.";

            case 'milestone_overdue':
                return "Milestone '{$data['milestone_title']}' in project '{$data['project_title']}' is {$data['days_overdue']} day(s) overdue.";

            case 'project_deadline_approaching':
                return "Project '{$data['project_title']}' for client '{$data['client_name']}' is approaching its deadline. Only {$data['days_remaining']} day(s) remaining until {$data['end_date']}.";

            default:
                return $event->description;
        }
    }
}
