<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('SiteSetting.site_name', '<PERSON><PERSON>');
        $this->migrator->add('SiteSetting.site_active', true);
        $this->migrator->add('SiteSetting.registration_enabled', true);
        $this->migrator->add('SiteSetting.login_enabled', true);
        $this->migrator->add('SiteSetting.password_reset_enabled', true);
        $this->migrator->add('SiteSetting.sso_enabled', true);
    }
};
