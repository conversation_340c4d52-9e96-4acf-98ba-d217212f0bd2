<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GrantAllAccess extends Command
{
    protected $signature = 'access:grant-all';
    protected $description = 'Grant all access to all users by directly modifying the database';

    public function handle()
    {
        $this->info('Granting all access to all users...');
        
        // Get all users
        $users = User::all();
        $this->info("Found {$users->count()} users");
        
        // Get or create super_admin role
        $roleId = DB::table('roles')->where('name', 'super_admin')->value('id');
        
        if (!$roleId) {
            $roleId = DB::table('roles')->insertGetId([
                'name' => 'super_admin',
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        $this->info("Super admin role ID: {$roleId}");
        
        // Assign super_admin role to all users
        foreach ($users as $user) {
            // Check if user already has the role
            $exists = DB::table('model_has_roles')
                ->where('role_id', $roleId)
                ->where('model_id', $user->id)
                ->where('model_type', User::class)
                ->exists();
                
            if (!$exists) {
                DB::table('model_has_roles')->insert([
                    'role_id' => $roleId,
                    'model_id' => $user->id,
                    'model_type' => User::class,
                ]);
            }
            
            $this->info("Super admin role assigned to user {$user->name}");
        }
        
        // Create a direct database entry to bypass Shield permissions
        $wildcardExists = DB::table('permissions')->where('name', '*')->exists();
        
        if (!$wildcardExists) {
            DB::table('permissions')->insert([
                'name' => '*',
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        // Assign wildcard permission to super_admin role
        $wildcardPermId = DB::table('permissions')->where('name', '*')->value('id');
        
        if ($wildcardPermId) {
            $permExists = DB::table('role_has_permissions')
                ->where('permission_id', $wildcardPermId)
                ->where('role_id', $roleId)
                ->exists();
                
            if (!$permExists) {
                DB::table('role_has_permissions')->insert([
                    'permission_id' => $wildcardPermId,
                    'role_id' => $roleId,
                ]);
            }
            
            $this->info("Wildcard permission assigned to super_admin role");
        }
        
        // Also assign all existing permissions to the super_admin role
        $allPermIds = DB::table('permissions')->pluck('id')->toArray();
        
        foreach ($allPermIds as $permId) {
            $permExists = DB::table('role_has_permissions')
                ->where('permission_id', $permId)
                ->where('role_id', $roleId)
                ->exists();
                
            if (!$permExists) {
                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permId,
                    'role_id' => $roleId,
                ]);
            }
        }
        
        $this->info("All permissions assigned to super_admin role");
        
        // Directly assign all permissions to all users
        foreach ($users as $user) {
            foreach ($allPermIds as $permId) {
                $permExists = DB::table('model_has_permissions')
                    ->where('permission_id', $permId)
                    ->where('model_id', $user->id)
                    ->where('model_type', User::class)
                    ->exists();
                    
                if (!$permExists) {
                    DB::table('model_has_permissions')->insert([
                        'permission_id' => $permId,
                        'model_id' => $user->id,
                        'model_type' => User::class,
                    ]);
                }
            }
            
            $this->info("All permissions directly assigned to user {$user->name}");
        }
        
        $this->info('All access granted successfully!');
        return 0;
    }
}
