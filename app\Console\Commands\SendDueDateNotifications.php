<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Milestone;
use Illuminate\Support\Facades\Notification;
use App\Notifications\DueDateExceededNotification;

class SendDueDateNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'milestones:send-due-date-notifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send notifications for milestones with due dates exceeding project end dates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $milestones = Milestone::whereHas('project', function ($query) {
            $query->whereNotNull('end_date');
        })->whereColumn('due_date', '>', 'project.end_date')->get();

        foreach ($milestones as $milestone) {
            Notification::send($milestone->project->user, new DueDateExceededNotification($milestone));
        }

        $this->info('Notifications sent for milestones exceeding due dates.');
    }
}
