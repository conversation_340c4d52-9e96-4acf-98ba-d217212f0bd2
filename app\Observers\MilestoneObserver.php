<?php

namespace App\Observers;

use App\Models\Milestone;
use App\Models\Payment;
use App\Services\NotificationService;

class MilestoneObserver
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function updated(Milestone $milestone)
    {
        // Only trigger when milestone status changes to 'completed'
        if ($milestone->isDirty('status') && $milestone->status === 'completed') {
            // Check if a payment already exists for this milestone
            $existingPayment = Payment::where('milestone_id', $milestone->id)->first();
            if (!$existingPayment) {
                Payment::create([
                    'project_id' => $milestone->project_id,
                    'milestone_id' => $milestone->id,
                    'amount' => $milestone->amount,
                    'due_date' => $milestone->due_date,
                    'status' => 'pending',
                ]);
            }
            // Notify all users with a specific role (e.g., bde_team) assigned to the project
            $project = $milestone->project;
            if ($project) {
                $roleName = 'bde_team'; // Change as needed
                $users = $project->user ? [$project->user] : [];
                foreach ($users as $user) {
                    $data = [
                        'milestone_id' => $milestone->id,
                        'milestone_title' => $milestone->title,
                        'project_title' => $project->title,
                        'status' => 'completed',
                    ];
                    $this->notificationService->send('milestone_completed', $data, $user);
                }
            }
        }
    }

    public function paymentUpdated(Payment $payment)
    {
        // Only trigger when payment status changes to 'paid'
        if ($payment->isDirty('status') && $payment->status === 'paid') {
            $milestone = $payment->milestone;
            $project = $payment->project;
            if (!$milestone || !$project) {
                return;
            }
            // Check if an incentive already exists for this payment/milestone
            $existingIncentive = \App\Models\Incentive::where('milestone_id', $milestone->id)
                ->where('project_id', $project->id)
                ->where('user_id', $project->user_id)
                ->first();
            if ($existingIncentive) {
                return;
            }
            // Find the best matching incentive rule
            $roleId = $project->user ? ($project->user->role_id ?? \DB::table('model_has_roles')
                ->where('model_type', 'App\\Models\\User')
                ->where('model_id', $project->user->id)
                ->value('role_id')) : null;
            $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                ->where('role_id', $roleId)
                ->where('duration_percentage', '<=', $milestone->percentage)
                ->orderByDesc('duration_percentage')
                ->first();
            if (!$rule) {
                return;
            }
            // Calculate incentive amount
            $amount = 0;
            switch ($rule->calculation_type) {
                case 'percentage':
                case 'Percentage of Contract Value':
                    $amount = ($project->total_payment ?? 0) * ($rule->percentage / 100);
                    break;
                case 'fixed':
                case 'Fixed Amount':
                    $amount = $rule->fixed_amount;
                    break;
                case 'hybrid':
                case 'Hybrid (Percentage + Fixed)':
                    $amount = (($project->total_payment ?? 0) * ($rule->percentage / 100)) + $rule->fixed_amount;
                    break;
                case 'time_based':
                case 'Time-based (Monthly)':
                    $months = max(1, $project->duration ?? 1);
                    $amount = $rule->fixed_amount * $months;
                    break;
                default:
                    $amount = ($project->total_payment ?? 0) * ($rule->percentage / 100);
            }
            // Create the incentive
            \App\Models\Incentive::create([
                'user_id' => $project->user_id,
                'project_id' => $project->id,
                'milestone_id' => $milestone->id,
                'amount' => round($amount, 2),
                'calculation_date' => now(),
                'status' => 'pending',
            ]);
        }
    }

    public function deleting(Milestone $milestone)
    {
        // Delete related payments
        \App\Models\Payment::where('milestone_id', $milestone->id)->delete();
        // Delete related incentives
        \App\Models\Incentive::where('milestone_id', $milestone->id)->delete();
    }
}
