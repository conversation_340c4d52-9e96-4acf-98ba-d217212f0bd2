<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Client;
use App\Models\Project;
use App\Models\ProjectType;
use App\Models\User;

class ProjectFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Project::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'client_id' => Client::factory(),
            'user_id' => User::factory(),
            'project_type_id' => ProjectType::factory(),
            'title' => fake()->sentence(4),
            'description' => fake()->text(),
            'won_date' => fake()->date(),
            'start_date' => fake()->date(),
            'end_date' => fake()->date(),
            'total_payment' => fake()->randomFloat(2, 0, 99999999.99),
            'duration' => fake()->numberBetween(-10000, 10000),
            'duration_unit' => fake()->word(),
            'payment_cycle' => fake()->word(),
            'status' => fake()->word(),
        ];
    }
}
