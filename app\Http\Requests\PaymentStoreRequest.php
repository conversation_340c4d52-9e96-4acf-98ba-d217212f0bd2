<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PaymentStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'project_id' => ['required', 'integer', 'exists:projects,id'],
            'milestone_id' => ['nullable', 'integer', 'exists:milestones,id'],
            'amount' => ['required', 'numeric', 'between:-99999999.99,99999999.99'],
            'due_date' => ['required', 'date'],
            'paid_date' => ['nullable', 'date'],
            'status' => ['required', 'string'],
            'payment_method' => ['nullable', 'string'],
            'transaction_id' => ['nullable', 'string'],
            'notes' => ['nullable', 'string'],
        ];
    }
}
