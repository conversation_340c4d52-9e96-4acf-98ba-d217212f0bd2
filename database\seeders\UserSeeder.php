<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
            ]
        );

        // Create BDE users
        \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON><PERSON>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
            ]
        );

        \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'P<PERSON> Patel',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
            ]
        );

        // Create marketing user
        \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Am<PERSON>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
            ]
        );

        // Note: We're not assigning roles here because the User model doesn't have the Spatie Permission trait
        // and we don't have a role_id column in the users table
    }
}
