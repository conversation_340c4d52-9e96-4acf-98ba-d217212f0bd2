<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\CurrencyRate;

class CurrencyRateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CurrencyRate::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'from_currency' => fake()->word(),
            'to_currency' => fake()->word(),
            'rate' => fake()->randomFloat(6, 0, 9999.999999),
            'last_updated_at' => fake()->dateTime(),
        ];
    }
}
