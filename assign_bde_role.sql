-- Get the user <NAME_EMAIL>
SET @user_id = (SELECT id FROM users WHERE email = '<EMAIL>');

-- Get or create the BDE role
INSERT IGNORE INTO roles (name, guard_name, created_at, updated_at)
VALUES ('bde', 'web', NOW(), NOW());

SET @role_id = (SELECT id FROM roles WHERE name = 'bde');

-- Remove any existing roles for this user
DELETE FROM model_has_roles WHERE model_id = @user_id AND model_type = 'App\\Models\\User';

-- Assign BDE role to the user
INSERT INTO model_has_roles (role_id, model_id, model_type)
VALUES (@role_id, @user_id, 'App\\Models\\User');
