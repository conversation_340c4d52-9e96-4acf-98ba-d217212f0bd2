/* Fix for sidebar navigation links */
.fi-sidebar-nav {
    z-index: 10;
    position: relative;
}

.fi-sidebar-item {
    display: flex !important;
    width: 100%;
}

.fi-sidebar-item-button {
    width: 100%;
    display: flex !important;
}

.fi-sidebar-group {
    display: block !important;
    width: 100%;
}

/* Fix for notification dropdown */
.notification-dropdown {
    position: absolute;
    top: calc(100% + 10px); /* Add spacing below the bell icon */
    left: 50%; /* Center align below the bell icon */
    transform: translateX(-50%);
    z-index: 9999;
    background-color: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 10px;
    width: 300px;
}

/* Ensure proper stacking context */
.fi-layout {
    isolation: isolate;
}

/* Fix for sidebar collapsible groups */
.fi-sidebar-group-items {
    display: block !important;
    width: 100%;
}

/* Fix for sidebar item label */
.fi-sidebar-item-label {
    display: block !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Fix for notification bell */
.notification-bell-wrapper {
    position: relative !important;
    z-index: 50 !important;
}

/* Ensure notification dropdown appears correctly */
.notification-bell-wrapper > div[x-show="open"] {
    position: absolute !important;
    right: 0 !important;
    top: 100% !important;
    margin-top: 0.5rem !important;
    z-index: 9999 !important;
}

/* Fix for Filament user menu area */
.fi-topbar-end {
    position: relative !important;
}

/* Ensure proper stacking context */
[data-slot="topbar"] {
    position: relative !important;
    z-index: 40 !important;
}
