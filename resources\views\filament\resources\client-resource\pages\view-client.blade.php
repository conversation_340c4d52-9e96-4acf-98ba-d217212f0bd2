@extends('filament::page')

@section('title', 'Client Details')

@section('content')
    <div class="space-y-6">
        <div class="bg-white dark:bg-gray-900 shadow rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-2">{{ $record->company_name }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <div class="text-gray-500">Email:</div>
                    <div class="font-medium">{{ $record->company_email }}</div>
                </div>
                <div>
                    <div class="text-gray-500">Number:</div>
                    <div class="font-medium">{{ $record->company_number }}</div>
                </div>
                <div>
                    <div class="text-gray-500">Status:</div>
                    <span class="inline-block px-2 py-1 rounded text-xs font-semibold {{ $record->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-200 text-gray-800' }}">{{ ucfirst($record->status) }}</span>
                </div>
                <div>
                    <div class="text-gray-500">Tax ID / GST:</div>
                    <div class="font-medium">{{ $record->tax_id }}</div>
                </div>
                <div class="col-span-2">
                    <div class="text-gray-500">Registered Address:</div>
                    <div class="font-medium">{{ $record->registered_address }}</div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-900 shadow rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4">Projects</h3>
            @forelse($record->projects as $project)
                <div class="mb-6 border-b pb-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="font-bold text-lg">{{ $project->title }}</div>
                        <span class="text-xs text-gray-500">{{ $project->status }}</span>
                    </div>
                    <div class="text-gray-500 mb-2">Type: {{ $project->projectType->name ?? '-' }}</div>
                    <div class="text-gray-500 mb-2">Total Payment: <span class="font-medium">{{ $project->total_payment }}</span> {{ $project->currency }}</div>
                    <div class="ml-4">
                        <h4 class="font-semibold mt-2 mb-1">Milestones</h4>
                        <ul class="list-disc ml-6">
                            @forelse($project->milestones as $milestone)
                                <li class="mb-2">
                                    <div class="flex flex-col md:flex-row md:items-center md:gap-4">
                                        <span class="font-medium">{{ $milestone->title }}</span>
                                        <span class="text-xs text-gray-500">({{ $milestone->percentage }}%)</span>
                                        <span class="text-xs text-gray-500">Due: {{ $milestone->due_date }}</span>
                                    </div>
                                    <div class="ml-4 mt-1">
                                        <h5 class="font-semibold text-sm mb-1">Incentives</h5>
                                        <ul class="list-disc ml-6">
                                            @forelse($milestone->incentives as $incentive)
                                                <li class="mb-1">
                                                    <span class="font-medium">User:</span> {{ $incentive->user->name ?? '-' }} |
                                                    <span class="font-medium">Amount:</span> {{ $incentive->amount }} {{ $incentive->incentiveRule->currency ?? '' }} |
                                                    <span class="font-medium">Status:</span> <span class="inline-block px-2 py-1 rounded text-xs font-semibold {{ $incentive->status === 'approved' ? 'bg-green-100 text-green-800' : ($incentive->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-200 text-gray-800') }}">{{ ucfirst($incentive->status) }}</span>
                                                </li>
                                            @empty
                                                <li class="text-gray-400">No incentives</li>
                                            @endforelse
                                        </ul>
                                    </div>
                                </li>
                            @empty
                                <li class="text-gray-400">No milestones</li>
                            @endforelse
                        </ul>
                    </div>
                </div>
            @empty
                <div class="text-gray-400">No projects found for this client.</div>
            @endforelse
        </div>
    </div>
@endsection
