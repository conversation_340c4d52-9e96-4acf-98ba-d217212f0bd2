{"__meta": {"id": "01JXBB09DKY9R5A4CETHRM9TZ0", "datetime": "2025-06-09 21:54:34", "utime": **********.040969, "method": "GET", "uri": "/admin/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 9, "messages": [{"message": "[21:54:30] LOG.info: [DASHBOARD] User widget configs {\n    \"user_id\": 1,\n    \"userConfigs\": {\n        \"1\": {\n            \"id\": 17,\n            \"user_id\": 1,\n            \"widget_id\": 1,\n            \"position\": 1,\n            \"width\": 12,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"2\": {\n            \"id\": 18,\n            \"user_id\": 1,\n            \"widget_id\": 2,\n            \"position\": 2,\n            \"width\": 3,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"3\": {\n            \"id\": 19,\n            \"user_id\": 1,\n            \"widget_id\": 3,\n            \"position\": 3,\n            \"width\": 6,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"4\": {\n            \"id\": 20,\n            \"user_id\": 1,\n            \"widget_id\": 4,\n            \"position\": 4,\n            \"width\": 3,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": false,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"5\": {\n            \"id\": 21,\n            \"user_id\": 1,\n            \"widget_id\": 5,\n            \"position\": 5,\n            \"width\": 12,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:44:00.000000Z\"\n        },\n        \"6\": {\n            \"id\": 22,\n            \"user_id\": 1,\n            \"widget_id\": 6,\n            \"position\": 6,\n            \"width\": 12,\n            \"height\": 2,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:37:54.000000Z\"\n        },\n        \"7\": {\n            \"id\": 23,\n            \"user_id\": 1,\n            \"widget_id\": 7,\n            \"position\": 7,\n            \"width\": 12,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:38:18.000000Z\"\n        },\n        \"8\": {\n            \"id\": 24,\n            \"user_id\": 1,\n            \"widget_id\": 8,\n            \"position\": 8,\n            \"width\": 8,\n            \"height\": 2,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T21:37:54.000000Z\",\n            \"updated_at\": \"2025-06-09T21:37:54.000000Z\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.27389, "xdebug_link": null, "collector": "log"}, {"message": "[21:54:30] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 1,\n    \"name\": \"Total Projects\",\n    \"component\": \"TotalProjectsWidget\",\n    \"width\": 12,\n    \"height\": 1,\n    \"position\": 1,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.274629, "xdebug_link": null, "collector": "log"}, {"message": "[21:54:30] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 2,\n    \"name\": \"Total Clients\",\n    \"component\": \"TotalClientsWidget\",\n    \"width\": 3,\n    \"height\": 1,\n    \"position\": 2,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.275054, "xdebug_link": null, "collector": "log"}, {"message": "[21:54:30] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 3,\n    \"name\": \"Pending Payments\",\n    \"component\": \"PendingPaymentsWidget\",\n    \"width\": 6,\n    \"height\": 1,\n    \"position\": 3,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.275358, "xdebug_link": null, "collector": "log"}, {"message": "[21:54:30] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 5,\n    \"name\": \"Recent Projects\",\n    \"component\": \"RecentProjectsWidget\",\n    \"width\": 12,\n    \"height\": 3,\n    \"position\": 5,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.275592, "xdebug_link": null, "collector": "log"}, {"message": "[21:54:30] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 6,\n    \"name\": \"Upcoming Payments\",\n    \"component\": \"UpcomingPaymentsWidget\",\n    \"width\": 12,\n    \"height\": 2,\n    \"position\": 6,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.275795, "xdebug_link": null, "collector": "log"}, {"message": "[21:54:30] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 7,\n    \"name\": \"Project Status\",\n    \"component\": \"ProjectStatusWidget\",\n    \"width\": 12,\n    \"height\": 3,\n    \"position\": 7,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.275991, "xdebug_link": null, "collector": "log"}, {"message": "[21:54:30] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 8,\n    \"name\": \"Monthly Revenue\",\n    \"component\": \"MonthlyRevenueWidget\",\n    \"width\": 8,\n    \"height\": 2,\n    \"position\": 8,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.276191, "xdebug_link": null, "collector": "log"}, {"message": "[21:54:30] LOG.info: [DASHBOARD] Final widgets for view [\n    {\n        \"id\": 1,\n        \"name\": \"Total Projects\",\n        \"component\": \"TotalProjectsWidget\",\n        \"width\": 12,\n        \"height\": 1,\n        \"position\": 1,\n        \"settings\": null\n    },\n    {\n        \"id\": 2,\n        \"name\": \"Total Clients\",\n        \"component\": \"TotalClientsWidget\",\n        \"width\": 3,\n        \"height\": 1,\n        \"position\": 2,\n        \"settings\": null\n    },\n    {\n        \"id\": 3,\n        \"name\": \"Pending Payments\",\n        \"component\": \"PendingPaymentsWidget\",\n        \"width\": 6,\n        \"height\": 1,\n        \"position\": 3,\n        \"settings\": null\n    },\n    {\n        \"id\": 5,\n        \"name\": \"Recent Projects\",\n        \"component\": \"RecentProjectsWidget\",\n        \"width\": 12,\n        \"height\": 3,\n        \"position\": 5,\n        \"settings\": null\n    },\n    {\n        \"id\": 6,\n        \"name\": \"Upcoming Payments\",\n        \"component\": \"UpcomingPaymentsWidget\",\n        \"width\": 12,\n        \"height\": 2,\n        \"position\": 6,\n        \"settings\": null\n    },\n    {\n        \"id\": 7,\n        \"name\": \"Project Status\",\n        \"component\": \"ProjectStatusWidget\",\n        \"width\": 12,\n        \"height\": 3,\n        \"position\": 7,\n        \"settings\": null\n    },\n    {\n        \"id\": 8,\n        \"name\": \"Monthly Revenue\",\n        \"component\": \"MonthlyRevenueWidget\",\n        \"width\": 8,\n        \"height\": 2,\n        \"position\": 8,\n        \"settings\": null\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.276366, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749506066.601791, "end": **********.041006, "duration": 7.439215183258057, "duration_str": "7.44s", "measures": [{"label": "Booting", "start": 1749506066.601791, "relative_start": 0, "end": **********.157958, "relative_end": **********.157958, "duration": 2.****************, "duration_str": "2.56s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.157977, "relative_start": 2.****************, "end": **********.041008, "relative_end": 1.9073486328125e-06, "duration": 4.***************, "duration_str": "4.88s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.163537, "relative_start": 3.***************, "end": **********.166518, "relative_end": **********.166518, "duration": 0.002980947494506836, "duration_str": "2.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.pages.dashboard", "start": **********.295971, "relative_start": 3.****************, "end": **********.295971, "relative_end": **********.295971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f2f44b54be1e6b3a1bd77567b74ada6e", "start": **********.714698, "relative_start": 7.**************, "end": **********.714698, "relative_end": **********.714698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::543962272ccef1b0087ff8de93e25a12", "start": **********.716784, "relative_start": 7.114993095397949, "end": **********.716784, "relative_end": **********.716784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dbc0ef3ed3193e87849b3ea3fba36a2f", "start": **********.72121, "relative_start": 7.119419097900391, "end": **********.72121, "relative_end": **********.72121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b528d29c191f34648130e512c6c14c42", "start": **********.730237, "relative_start": 7.128446102142334, "end": **********.730237, "relative_end": **********.730237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d691f9b9b8ea42c9565d0eae88a8a7a", "start": **********.736355, "relative_start": 7.134564161300659, "end": **********.736355, "relative_end": **********.736355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.761188, "relative_start": 7.159397125244141, "end": **********.761188, "relative_end": **********.761188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.768939, "relative_start": 7.167148113250732, "end": **********.768939, "relative_end": **********.768939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.769917, "relative_start": 7.168126106262207, "end": **********.769917, "relative_end": **********.769917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.770339, "relative_start": 7.168548107147217, "end": **********.770339, "relative_end": **********.770339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.77057, "relative_start": 7.168779134750366, "end": **********.77057, "relative_end": **********.77057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.770938, "relative_start": 7.16914701461792, "end": **********.770938, "relative_end": **********.770938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.771166, "relative_start": 7.16937518119812, "end": **********.771166, "relative_end": **********.771166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.771501, "relative_start": 7.169710159301758, "end": **********.771501, "relative_end": **********.771501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::25bf90774d7761d7a1ccd3a4e9e7a410", "start": **********.771765, "relative_start": 7.169974088668823, "end": **********.771765, "relative_end": **********.771765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.776642, "relative_start": 7.174851179122925, "end": **********.776642, "relative_end": **********.776642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.78451, "relative_start": 7.182718992233276, "end": **********.78451, "relative_end": **********.78451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.78542, "relative_start": 7.183629035949707, "end": **********.78542, "relative_end": **********.78542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.786162, "relative_start": 7.184370994567871, "end": **********.786162, "relative_end": **********.786162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.786908, "relative_start": 7.18511700630188, "end": **********.786908, "relative_end": **********.786908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5ad0a0bb5e11ca8c3120627bd444a583", "start": **********.787633, "relative_start": 7.185842037200928, "end": **********.787633, "relative_end": **********.787633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6793bd8c17ae551c989e7d8702e297bd", "start": **********.78841, "relative_start": 7.18661904335022, "end": **********.78841, "relative_end": **********.78841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::669f45ea592e06d462edef0dc7991631", "start": **********.796371, "relative_start": 7.194580078125, "end": **********.796371, "relative_end": **********.796371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::920886b33da20750c767a455729f43a3", "start": **********.798086, "relative_start": 7.1962950229644775, "end": **********.798086, "relative_end": **********.798086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::580d32f5fef9ea18b4f700add0d02b13", "start": **********.799518, "relative_start": 7.197727203369141, "end": **********.799518, "relative_end": **********.799518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::09b71e88617c7cb829e8c2982af46b24", "start": **********.80177, "relative_start": 7.199979066848755, "end": **********.80177, "relative_end": **********.80177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.837351, "relative_start": 7.235560178756714, "end": **********.837351, "relative_end": **********.837351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.892133, "relative_start": 7.290342092514038, "end": **********.892133, "relative_end": **********.892133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.898389, "relative_start": 7.296598196029663, "end": **********.898389, "relative_end": **********.898389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.899581, "relative_start": 7.297790050506592, "end": **********.899581, "relative_end": **********.899581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.902788, "relative_start": 7.300997018814087, "end": **********.902788, "relative_end": **********.902788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.903696, "relative_start": 7.301905155181885, "end": **********.903696, "relative_end": **********.903696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.913672, "relative_start": 7.311881065368652, "end": **********.913672, "relative_end": **********.913672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.914009, "relative_start": 7.312218189239502, "end": **********.914009, "relative_end": **********.914009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.916091, "relative_start": 7.314300060272217, "end": **********.916091, "relative_end": **********.916091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.916361, "relative_start": 7.314570188522339, "end": **********.916361, "relative_end": **********.916361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.918335, "relative_start": 7.316544055938721, "end": **********.918335, "relative_end": **********.918335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.918592, "relative_start": 7.316801071166992, "end": **********.918592, "relative_end": **********.918592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.920455, "relative_start": 7.318664073944092, "end": **********.920455, "relative_end": **********.920455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.920715, "relative_start": 7.318924188613892, "end": **********.920715, "relative_end": **********.920715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.999416, "relative_start": 7.397625207901001, "end": **********.999416, "relative_end": **********.999416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.000762, "relative_start": 7.398971080780029, "end": **********.000762, "relative_end": **********.000762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.013954, "relative_start": 7.412163019180298, "end": **********.013954, "relative_end": **********.013954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.016869, "relative_start": 7.415078163146973, "end": **********.016869, "relative_end": **********.016869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.018214, "relative_start": 7.416423082351685, "end": **********.018214, "relative_end": **********.018214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.026626, "relative_start": 7.424835205078125, "end": **********.026799, "relative_end": **********.026799, "duration": 0.0001728534698486328, "duration_str": "173μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.033833, "relative_start": 7.432042121887207, "end": **********.033903, "relative_end": **********.033903, "duration": 6.985664367675781e-05, "duration_str": "70μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 48810256, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 45, "nb_templates": 45, "templates": [{"name": "filament.pages.dashboard", "param_count": null, "params": [], "start": **********.295946, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.phpfilament.pages.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "__components::f2f44b54be1e6b3a1bd77567b74ada6e", "param_count": null, "params": [], "start": **********.714673, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/f2f44b54be1e6b3a1bd77567b74ada6e.blade.php__components::f2f44b54be1e6b3a1bd77567b74ada6e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Ff2f44b54be1e6b3a1bd77567b74ada6e.blade.php&line=1", "ajax": false, "filename": "f2f44b54be1e6b3a1bd77567b74ada6e.blade.php", "line": "?"}}, {"name": "__components::543962272ccef1b0087ff8de93e25a12", "param_count": null, "params": [], "start": **********.716765, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/543962272ccef1b0087ff8de93e25a12.blade.php__components::543962272ccef1b0087ff8de93e25a12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F543962272ccef1b0087ff8de93e25a12.blade.php&line=1", "ajax": false, "filename": "543962272ccef1b0087ff8de93e25a12.blade.php", "line": "?"}}, {"name": "__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "param_count": null, "params": [], "start": **********.72119, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fdbc0ef3ed3193e87849b3ea3fba36a2f.blade.php&line=1", "ajax": false, "filename": "dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php", "line": "?"}}, {"name": "__components::b528d29c191f34648130e512c6c14c42", "param_count": null, "params": [], "start": **********.730218, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b528d29c191f34648130e512c6c14c42.blade.php__components::b528d29c191f34648130e512c6c14c42", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb528d29c191f34648130e512c6c14c42.blade.php&line=1", "ajax": false, "filename": "b528d29c191f34648130e512c6c14c42.blade.php", "line": "?"}}, {"name": "__components::1d691f9b9b8ea42c9565d0eae88a8a7a", "param_count": null, "params": [], "start": **********.736334, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php__components::1d691f9b9b8ea42c9565d0eae88a8a7a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php&line=1", "ajax": false, "filename": "1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.761167, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.768921, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.769899, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.770326, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.770556, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.770924, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.771153, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.771487, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::25bf90774d7761d7a1ccd3a4e9e7a410", "param_count": null, "params": [], "start": **********.771752, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/25bf90774d7761d7a1ccd3a4e9e7a410.blade.php__components::25bf90774d7761d7a1ccd3a4e9e7a410", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F25bf90774d7761d7a1ccd3a4e9e7a410.blade.php&line=1", "ajax": false, "filename": "25bf90774d7761d7a1ccd3a4e9e7a410.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.776624, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.784493, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.785406, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.786148, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.786895, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::5ad0a0bb5e11ca8c3120627bd444a583", "param_count": null, "params": [], "start": **********.787617, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/5ad0a0bb5e11ca8c3120627bd444a583.blade.php__components::5ad0a0bb5e11ca8c3120627bd444a583", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F5ad0a0bb5e11ca8c3120627bd444a583.blade.php&line=1", "ajax": false, "filename": "5ad0a0bb5e11ca8c3120627bd444a583.blade.php", "line": "?"}}, {"name": "__components::6793bd8c17ae551c989e7d8702e297bd", "param_count": null, "params": [], "start": **********.788393, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/6793bd8c17ae551c989e7d8702e297bd.blade.php__components::6793bd8c17ae551c989e7d8702e297bd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F6793bd8c17ae551c989e7d8702e297bd.blade.php&line=1", "ajax": false, "filename": "6793bd8c17ae551c989e7d8702e297bd.blade.php", "line": "?"}}, {"name": "__components::669f45ea592e06d462edef0dc7991631", "param_count": null, "params": [], "start": **********.796351, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/669f45ea592e06d462edef0dc7991631.blade.php__components::669f45ea592e06d462edef0dc7991631", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F669f45ea592e06d462edef0dc7991631.blade.php&line=1", "ajax": false, "filename": "669f45ea592e06d462edef0dc7991631.blade.php", "line": "?"}}, {"name": "__components::920886b33da20750c767a455729f43a3", "param_count": null, "params": [], "start": **********.798069, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/920886b33da20750c767a455729f43a3.blade.php__components::920886b33da20750c767a455729f43a3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F920886b33da20750c767a455729f43a3.blade.php&line=1", "ajax": false, "filename": "920886b33da20750c767a455729f43a3.blade.php", "line": "?"}}, {"name": "__components::580d32f5fef9ea18b4f700add0d02b13", "param_count": null, "params": [], "start": **********.799501, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/580d32f5fef9ea18b4f700add0d02b13.blade.php__components::580d32f5fef9ea18b4f700add0d02b13", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F580d32f5fef9ea18b4f700add0d02b13.blade.php&line=1", "ajax": false, "filename": "580d32f5fef9ea18b4f700add0d02b13.blade.php", "line": "?"}}, {"name": "__components::09b71e88617c7cb829e8c2982af46b24", "param_count": null, "params": [], "start": **********.801744, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/09b71e88617c7cb829e8c2982af46b24.blade.php__components::09b71e88617c7cb829e8c2982af46b24", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F09b71e88617c7cb829e8c2982af46b24.blade.php&line=1", "ajax": false, "filename": "09b71e88617c7cb829e8c2982af46b24.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.837332, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.892116, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}}, {"name": "filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.898372, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}}, {"name": "__components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.899566, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.902774, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.903683, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.913656, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.913996, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.916076, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.916348, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.918322, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.918581, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.920442, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.920703, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.999396, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}}, {"name": "filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.000745, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.013938, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}, {"name": "__components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.016851, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}}, {"name": "components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.018184, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}}]}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.058559999999999994, "accumulated_duration_str": "58.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq' limit 1", "type": "query", "params": [], "bindings": ["yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.201961, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 4.679}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.207612, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.679, "width_percent": 3.21}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.212484, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.889, "width_percent": 2.135}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.217994, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.024, "width_percent": 1.93}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.2203941, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.954, "width_percent": 0.786}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.230949, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.739, "width_percent": 0.871}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.232359, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.61, "width_percent": 1.622}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.234486, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.232, "width_percent": 0.751}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.2358031, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.984, "width_percent": 0.581}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.236943, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.564, "width_percent": 0.461}, {"sql": "select * from `dashboard_widgets` where `is_available` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 75}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.244544, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:75", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=75", "ajax": false, "filename": "Dashboard.php", "line": "75"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.025, "width_percent": 4.064}, {"sql": "select * from `dashboard_configs` where `user_id` = 1 order by `position` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2489622, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:80", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=80", "ajax": false, "filename": "Dashboard.php", "line": "80"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.089, "width_percent": 3.176}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanAuthorizeAccess.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanAuthorizeAccess.php", "line": 9}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.2837272, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.266, "width_percent": 3.808}, {"sql": "select count(*) as aggregate from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 170}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.72489, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:170", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 170}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=170", "ajax": false, "filename": "dashboard.blade.php", "line": "170"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.074, "width_percent": 5.806}, {"sql": "select count(*) as aggregate from `clients`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.73194, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:208", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=208", "ajax": false, "filename": "dashboard.blade.php", "line": "208"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.88, "width_percent": 4.491}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 246}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.73753, "duration": 0.00737, "duration_str": "7.37ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:246", "source": {"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 246}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=246", "ajax": false, "filename": "dashboard.blade.php", "line": "246"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.371, "width_percent": 12.585}, {"sql": "select * from `projects` order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 320}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.746014, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:320", "source": {"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 320}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=320", "ajax": false, "filename": "dashboard.blade.php", "line": "320"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.956, "width_percent": 4.474}, {"sql": "select * from `clients` where `clients`.`id` in (1, 6, 7, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 320}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.751067, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:320", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 320}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=320", "ajax": false, "filename": "dashboard.blade.php", "line": "320"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.43, "width_percent": 4.559}, {"sql": "select * from `users` where `users`.`id` in (1, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 320}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.755027, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:320", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 320}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=320", "ajax": false, "filename": "dashboard.blade.php", "line": "320"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.99, "width_percent": 5.123}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1, 4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 320}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.759041, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:320", "source": {"index": 25, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 320}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=320", "ajax": false, "filename": "dashboard.blade.php", "line": "320"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.113, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 395}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.777024, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:395", "source": {"index": 19, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 395}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=395", "ajax": false, "filename": "dashboard.blade.php", "line": "395"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.898, "width_percent": 0.837}, {"sql": "select * from `payments` where `status` = 'pending' order by `due_date` asc limit 5", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 439}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.778442, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:439", "source": {"index": 15, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=439", "ajax": false, "filename": "dashboard.blade.php", "line": "439"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.735, "width_percent": 2.783}, {"sql": "select * from `projects` where `projects`.`id` in (1, 3, 5, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 439}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.781361, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "filament.pages.dashboard:439", "source": {"index": 20, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=439", "ajax": false, "filename": "dashboard.blade.php", "line": "439"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.518, "width_percent": 0.888}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.8644652, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.406, "width_percent": 0.751}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.894501, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.158, "width_percent": 4.525}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 64}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.900376, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.683, "width_percent": 2.032}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.906545, "duration": 0.00614, "duration_str": "6.14ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:124", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=124", "ajax": false, "filename": "notification-bell.blade.php", "line": "124"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.715, "width_percent": 10.485}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.914619, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:124", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=124", "ajax": false, "filename": "notification-bell.blade.php", "line": "124"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.2, "width_percent": 0.99}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.916964, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:124", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=124", "ajax": false, "filename": "notification-bell.blade.php", "line": "124"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.191, "width_percent": 0.905}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.919169, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:124", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=124", "ajax": false, "filename": "notification-bell.blade.php", "line": "124"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.096, "width_percent": 0.837}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.921936, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:124", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=124", "ajax": false, "filename": "notification-bell.blade.php", "line": "124"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.932, "width_percent": 1.127}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiWHZ3U0VLMm5NSjIxR3d2WG5ndThPVmRna3VWMGcxTG1Tb0hFeVhNbiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYWRtaW4vZGFzaGJvYXJkIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjg6ImZpbGFtZW50IjthOjA6e319', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0' where `id` = 'yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiWHZ3U0VLMm5NSjIxR3d2WG5ndThPVmRna3VWMGcxTG1Tb0hFeVhNbiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYWRtaW4vZGFzaGJvYXJkIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjg6ImZpbGFtZW50IjthOjA6e319", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.0283399, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.059, "width_percent": 7.941}]}, "models": {"data": {"App\\Models\\Project": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\DashboardWidget": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FDashboardWidget.php&line=1", "ajax": false, "filename": "DashboardWidget.php", "line": "?"}}, "App\\Models\\DashboardConfig": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FDashboardConfig.php&line=1", "ajax": false, "filename": "DashboardConfig.php", "line": "?"}}, "App\\Models\\Payment": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\Client": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 48, "is_counter": true}, "livewire": {"data": {"app.filament.pages.dashboard #F4LniSo64TBgXFLrFWvK": "array:4 [\n  \"data\" => array:15 [\n    \"widgets\" => array:7 [\n      0 => array:7 [\n        \"id\" => 1\n        \"name\" => \"Total Projects\"\n        \"component\" => \"TotalProjectsWidget\"\n        \"width\" => 12\n        \"height\" => 1\n        \"position\" => 1\n        \"settings\" => null\n      ]\n      1 => array:7 [\n        \"id\" => 2\n        \"name\" => \"Total Clients\"\n        \"component\" => \"TotalClientsWidget\"\n        \"width\" => 3\n        \"height\" => 1\n        \"position\" => 2\n        \"settings\" => null\n      ]\n      2 => array:7 [\n        \"id\" => 3\n        \"name\" => \"Pending Payments\"\n        \"component\" => \"PendingPaymentsWidget\"\n        \"width\" => 6\n        \"height\" => 1\n        \"position\" => 3\n        \"settings\" => null\n      ]\n      3 => array:7 [\n        \"id\" => 5\n        \"name\" => \"Recent Projects\"\n        \"component\" => \"RecentProjectsWidget\"\n        \"width\" => 12\n        \"height\" => 3\n        \"position\" => 5\n        \"settings\" => null\n      ]\n      4 => array:7 [\n        \"id\" => 6\n        \"name\" => \"Upcoming Payments\"\n        \"component\" => \"UpcomingPaymentsWidget\"\n        \"width\" => 12\n        \"height\" => 2\n        \"position\" => 6\n        \"settings\" => null\n      ]\n      5 => array:7 [\n        \"id\" => 7\n        \"name\" => \"Project Status\"\n        \"component\" => \"ProjectStatusWidget\"\n        \"width\" => 12\n        \"height\" => 3\n        \"position\" => 7\n        \"settings\" => null\n      ]\n      6 => array:7 [\n        \"id\" => 8\n        \"name\" => \"Monthly Revenue\"\n        \"component\" => \"MonthlyRevenueWidget\"\n        \"width\" => 8\n        \"height\" => 2\n        \"position\" => 8\n        \"settings\" => null\n      ]\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.dashboard\"\n  \"component\" => \"App\\Filament\\Pages\\Dashboard\"\n  \"id\" => \"F4LniSo64TBgXFLrFWvK\"\n]", "filament.livewire.global-search #mDoF0z5bL9oTZwRaTbri": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"mDoF0z5bL9oTZwRaTbri\"\n]", "app.filament.widgets.notification-components.notification-bell #0jP6UMxATJoIVrXcTTsg": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 10\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"0jP6UMxATJoIVrXcTTsg\"\n]", "filament.livewire.notifications #S119KOr9IYMY1CLs8dcQ": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3574\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"S119KOr9IYMY1CLs8dcQ\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 17, "messages": [{"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-411068128 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411068128\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.845854, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-799759832 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799759832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.846866, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1294598495 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294598495\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.850186, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-977522297 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977522297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.851149, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-487234536 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487234536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.852844, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1745633852 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745633852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.853771, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1255084041 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255084041\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.854647, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-607608810 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-607608810\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.857489, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-830681396 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-830681396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.858679, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-333070782 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333070782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.859604, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1119409441 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119409441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.860562, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-380968245 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380968245\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.861437, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1233194694 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233194694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862363, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1651770929 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651770929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.863195, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1695892482 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695892482\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.86653, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-352526969 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352526969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867668, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1710714592 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710714592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880491, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard", "uri": "GET admin/dashboard", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "7.44s", "peak_memory": "54MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1269 characters\">sb-updates=3.7.1; kit_session=eyJpdiI6InJBSUI3R21Fdy9LK0dPQzNia1c1M2c9PSIsInZhbHVlIjoiZjBqTkcrQkFndjlIbUJMQ1d4UytYSE9yZlhkMDNoU1NYaG1OVm1UVTRBZjVNcVJ1VWVWbFRxa08yOUQ5alZyZlNjd1lXWUJRSFk3VUZycEhtYUltRWZDam5YTWx5OWQ4S0pOdjNFVXdmMnJQRHRtbkhPSXNlaVQxaEhIeVJpYTUiLCJtYWMiOiJmZTQyZjkzMjJhZDFmZjFiNDU4YTI3YTA4MjQ4MmY2MjA1YTA1OGUzZjdhZmYxODFjYjU1ODY2MmZkMGRmNTEzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkFCQVJmN284cjRHUEoxZFZKMUNzeGc9PSIsInZhbHVlIjoiNFhOQXpSUVRia2J6cEltd2NQS1Myd21HMXlSQmRmWDdyZm81eXlRU1B1dzhadGsyd1MvUkZGR1NPUW4vS2NOWW5GTjRFaEFidkJyTkZMVEZzQlp3VCt4YThRYlVBUldxLzhvOVBKcFlxT3YxTmdIZmNrOUtQajkySDh3TWRIZ1UiLCJtYWMiOiJlMzJiMTAzYzgwNDI2ZjViNDUyM2RkY2JjYTljNTg3NjcwNzMzMjgwMGU3ZTYyYTk3MDViNGFkOTJkZjY0NWU4IiwidGFnIjoiIn0%3D; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ii9BZkRxeXlQaENwK3RNSzFMTkV5Z2c9PSIsInZhbHVlIjoiNnBFelQwSGc4dXN1RXU2VEpXSHJjTVVsQUpucFdjVU9oeGVkUnhJZzBySGxLbHRQTFFHNldrVjhJcHpsUzV0c3ZSOFNlWmxkTUJoWFFaNlM3MTdzc0l4Q1JwV0pnS1JrTVlLdDJ6L0FwUEI4YTFZSkMyNExzTnoxWXJRZk9Wa1pMcHhoVjZiQ0F0WXFlUlU1bm9NaDVjSDJoVVVyWWhVZ0V1b2xMZFRkdXZlWWx6ak9qbjVoS3VXa3RFSmhScEl1UmY0TlZ0a0RIUm16am83MVRzaitxQmxSWHFGc3F2QlZVMW9kclB6UExUND0iLCJtYWMiOiI4NDIzOThjOTA3MWZkN2E0NjgxZjQ1MDQ0NDg4NzgyOTg0NDFjYzg1ZTRlZGNhM2FiNThhZDBmYzIzODNjNjQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1058843870 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sb-updates</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yCLAQcf1ibNZEJL6tCbbVwRkxWkKqvKaAztmHWoq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XvwSEK2nMJ21GwvXngu8OVdgkuV0g1LmSoHEyXMn</span>\"\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IaoVXF77cb5deCAtwKpLOWhQkSz6v8KjZyt6VaEz3KMxX2I2xibuZaQaf80m|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058843870\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1068908787 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 09 Jun 2025 21:54:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InhnNU8vZU0vZWhIcGdhRTF5bmpTaHc9PSIsInZhbHVlIjoiOEdraXRVU0k5Ykc4MFZLM216aDc5SEp6a2VGOW45WTNkZXp5MkIvemRoR05qTUhoUC9mcWJKSDhXMXRQZUVRSHJPYWVDdVIvRUoxVmk1OE1UT041elNBcWVkVzY1K0tWblk1NGxXdzltVnY1YWFUTmFCVUZ4VXV2L2FmTXk0dU8iLCJtYWMiOiJhNjE2ZDczMDdjMDI3Y2JkNTEyZTgyNDY5ZWE3N2M1Njc4NjY1NDE2Y2IzNTk2NzhlMDcxMWUxYjBkNDU2YTA4IiwidGFnIjoiIn0%3D; expires=Mon, 09 Jun 2025 23:54:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6ImlTVUxsbnVoUmIycmtYOENXSXh4V1E9PSIsInZhbHVlIjoiQjFXOHBka1JXNUlaTTk3NEdTTE1wNDhiTUhDM3FPM2VtMWJ2Y3M0NFVqZXF3UjZmOGRvQjgrWkRzZmQrNnk3LytJeXNwL0lQQzBaSkM4SExUSlBYRGZ1NlVrNytqUDRseWFkZnB6N0ZpN29OMWhZUFViVWgrUDFveHE1aXBCWjMiLCJtYWMiOiJmNTZmZTM2OTEyZDQ1ODhlMzExYmMyMTIwMDc1MmY5NmZlMDRmM2I1MTAxMDFjNTA0N2Q5NjgyODQyZTM0ZGFiIiwidGFnIjoiIn0%3D; expires=Mon, 09 Jun 2025 23:54:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InhnNU8vZU0vZWhIcGdhRTF5bmpTaHc9PSIsInZhbHVlIjoiOEdraXRVU0k5Ykc4MFZLM216aDc5SEp6a2VGOW45WTNkZXp5MkIvemRoR05qTUhoUC9mcWJKSDhXMXRQZUVRSHJPYWVDdVIvRUoxVmk1OE1UT041elNBcWVkVzY1K0tWblk1NGxXdzltVnY1YWFUTmFCVUZ4VXV2L2FmTXk0dU8iLCJtYWMiOiJhNjE2ZDczMDdjMDI3Y2JkNTEyZTgyNDY5ZWE3N2M1Njc4NjY1NDE2Y2IzNTk2NzhlMDcxMWUxYjBkNDU2YTA4IiwidGFnIjoiIn0%3D; expires=Mon, 09-Jun-2025 23:54:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6ImlTVUxsbnVoUmIycmtYOENXSXh4V1E9PSIsInZhbHVlIjoiQjFXOHBka1JXNUlaTTk3NEdTTE1wNDhiTUhDM3FPM2VtMWJ2Y3M0NFVqZXF3UjZmOGRvQjgrWkRzZmQrNnk3LytJeXNwL0lQQzBaSkM4SExUSlBYRGZ1NlVrNytqUDRseWFkZnB6N0ZpN29OMWhZUFViVWgrUDFveHE1aXBCWjMiLCJtYWMiOiJmNTZmZTM2OTEyZDQ1ODhlMzExYmMyMTIwMDc1MmY5NmZlMDRmM2I1MTAxMDFjNTA0N2Q5NjgyODQyZTM0ZGFiIiwidGFnIjoiIn0%3D; expires=Mon, 09-Jun-2025 23:54:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068908787\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1864788759 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XvwSEK2nMJ21GwvXngu8OVdgkuV0g1LmSoHEyXMn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864788759\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard"}, "badge": null}}