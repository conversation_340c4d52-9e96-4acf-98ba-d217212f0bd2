<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class DashboardSettingsPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the dashboard settings.
     */
    public function view(User $user): bool
    {
        // Only allow users with specific roles to access the dashboard settings
        return $user->hasAnyRole(['super_admin', 'admin', 'manager']);
    }
}
