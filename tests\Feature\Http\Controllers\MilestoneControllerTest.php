<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Milestone;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Carbon;
use JMac\Testing\Traits\AdditionalAssertions;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * @see \App\Http\Controllers\MilestoneController
 */
final class MilestoneControllerTest extends TestCase
{
    use AdditionalAssertions, RefreshDatabase, WithFaker;

    #[Test]
    public function index_displays_view(): void
    {
        $milestones = Milestone::factory()->count(3)->create();

        $response = $this->get(route('milestones.index'));

        $response->assertOk();
        $response->assertViewIs('milestone.index');
        $response->assertViewHas('milestones');
    }


    #[Test]
    public function create_displays_view(): void
    {
        $response = $this->get(route('milestones.create'));

        $response->assertOk();
        $response->assertViewIs('milestone.create');
    }


    #[Test]
    public function store_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\MilestoneController::class,
            'store',
            \App\Http\Requests\MilestoneControllerStoreRequest::class
        );
    }

    #[Test]
    public function store_saves_and_redirects(): void
    {
        $project = Project::factory()->create();
        $title = fake()->sentence(4);
        $due_date = Carbon::parse(fake()->date());
        $percentage = fake()->randomFloat(/** decimal_attributes **/);
        $amount = fake()->randomFloat(/** decimal_attributes **/);
        $status = fake()->word();

        $response = $this->post(route('milestones.store'), [
            'project_id' => $project->id,
            'title' => $title,
            'due_date' => $due_date->toDateString(),
            'percentage' => $percentage,
            'amount' => $amount,
            'status' => $status,
        ]);

        $milestones = Milestone::query()
            ->where('project_id', $project->id)
            ->where('title', $title)
            ->where('due_date', $due_date)
            ->where('percentage', $percentage)
            ->where('amount', $amount)
            ->where('status', $status)
            ->get();
        $this->assertCount(1, $milestones);
        $milestone = $milestones->first();

        $response->assertRedirect(route('milestones.index'));
        $response->assertSessionHas('milestone.id', $milestone->id);
    }


    #[Test]
    public function show_displays_view(): void
    {
        $milestone = Milestone::factory()->create();

        $response = $this->get(route('milestones.show', $milestone));

        $response->assertOk();
        $response->assertViewIs('milestone.show');
        $response->assertViewHas('milestone');
    }


    #[Test]
    public function edit_displays_view(): void
    {
        $milestone = Milestone::factory()->create();

        $response = $this->get(route('milestones.edit', $milestone));

        $response->assertOk();
        $response->assertViewIs('milestone.edit');
        $response->assertViewHas('milestone');
    }


    #[Test]
    public function update_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\MilestoneController::class,
            'update',
            \App\Http\Requests\MilestoneControllerUpdateRequest::class
        );
    }

    #[Test]
    public function update_redirects(): void
    {
        $milestone = Milestone::factory()->create();
        $project = Project::factory()->create();
        $title = fake()->sentence(4);
        $due_date = Carbon::parse(fake()->date());
        $percentage = fake()->randomFloat(/** decimal_attributes **/);
        $amount = fake()->randomFloat(/** decimal_attributes **/);
        $status = fake()->word();

        $response = $this->put(route('milestones.update', $milestone), [
            'project_id' => $project->id,
            'title' => $title,
            'due_date' => $due_date->toDateString(),
            'percentage' => $percentage,
            'amount' => $amount,
            'status' => $status,
        ]);

        $milestone->refresh();

        $response->assertRedirect(route('milestones.index'));
        $response->assertSessionHas('milestone.id', $milestone->id);

        $this->assertEquals($project->id, $milestone->project_id);
        $this->assertEquals($title, $milestone->title);
        $this->assertEquals($due_date, $milestone->due_date);
        $this->assertEquals($percentage, $milestone->percentage);
        $this->assertEquals($amount, $milestone->amount);
        $this->assertEquals($status, $milestone->status);
    }


    #[Test]
    public function destroy_deletes_and_redirects(): void
    {
        $milestone = Milestone::factory()->create();

        $response = $this->delete(route('milestones.destroy', $milestone));

        $response->assertRedirect(route('milestones.index'));

        $this->assertModelMissing($milestone);
    }
}
