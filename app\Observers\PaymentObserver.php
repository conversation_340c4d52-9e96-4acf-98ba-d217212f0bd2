<?php

namespace App\Observers;

use App\Models\Payment;
use App\Models\IncentiveRule;
use App\Models\Incentive;
use Illuminate\Support\Facades\DB;

class PaymentObserver
{
    public function created(Payment $payment)
    {
        // If payment is created with status 'paid', create incentive
        if ($payment->status === 'paid') {
            $this->handlePaid($payment, true);
        }
    }

    public function updated(Payment $payment)
    {
        // If payment status changed to 'paid', create incentive
        if ($payment->isDirty('status') && $payment->status === 'paid') {
            $this->handlePaid($payment, false);
        }
        // If payment status changed from 'paid' to any other status, delete incentive
        if ($payment->isDirty('status') && $payment->getOriginal('status') === 'paid' && $payment->status !== 'paid') {
            $milestone = $payment->milestone;
            $project = $payment->project;
            if ($milestone && $project) {
                Incentive::where('milestone_id', $milestone->id)
                    ->where('project_id', $project->id)
                    ->where('user_id', $project->user_id)
                    ->delete();
            }
        }
    }

    private function handlePaid(Payment $payment, $isCreate = false)
    {
        $milestone = $payment->milestone;
        $project = $payment->project;
        if (!$milestone || !$project) {
            return;
        }
        $milestonePercent = $milestone->percentage ?? 0;
        $user = $project->user;
        if (!$user) {
            return;
        }
        // Get the user's role_id properly from model_has_roles table
        $roleId = \DB::table('model_has_roles')
            ->where('model_type', 'App\\Models\\User')
            ->where('model_id', $user->id)
            ->first()?->role_id;
        if (!$roleId) {
            return;
        }
        // Get the role name to match with incentive rules
        $roleName = \DB::table('roles')
            ->where('id', $roleId)
            ->value('name');
        if (!$roleName) {
            return;
        }
        // Find the best matching incentive rule for THIS user's role and milestone percentage
        $rule = IncentiveRule::select('incentive_rules.*')
            ->join('roles', 'roles.id', '=', 'incentive_rules.role_id')
            ->where('incentive_rules.project_type_id', $project->project_type_id)
            ->where('roles.name', $roleName)
            ->where('incentive_rules.duration_percentage', '<=', $milestonePercent)
            ->orderByDesc('incentive_rules.duration_percentage')
            ->first();
        if (!$rule) {
            return;
        }
        // Check if incentive already exists for this milestone
        $existing = Incentive::where('milestone_id', $milestone->id)
            ->where('project_id', $project->id)
            ->where('user_id', $user->id)
            ->first();
        if ($existing) {
            return;
        }
        // Calculate incentive amount
        $incentiveAmount = 0;
        switch ($rule->calculation_type) {
            case 'percentage':
            case 'Percentage of Contract Value':
                $incentiveAmount = ($project->total_payment ?? 0) * ($rule->percentage / 100);
                break;
            case 'fixed':
            case 'Fixed Amount':
                $incentiveAmount = $rule->fixed_amount;
                break;
            case 'hybrid':
            case 'Hybrid (Percentage + Fixed)':
                $incentiveAmount = (($project->total_payment ?? 0) * ($rule->percentage / 100)) + $rule->fixed_amount;
                break;
            case 'time-based':
            case 'Time-based (Monthly)':
                $months = max(1, $project->duration ?? 1);
                $incentiveAmount = $rule->fixed_amount * $months;
                break;
            default:
                $incentiveAmount = ($project->total_payment ?? 0) * ($rule->percentage / 100);
        }
        // Convert incentive amount to the target currency
        $projectCurrency = $project->currency;
        $ruleCurrency = $rule->currency;
        $convertedAmount = \App\Services\CurrencyConverter::convert($projectCurrency, $ruleCurrency, $incentiveAmount);
        // Create the incentive entry with converted amount
        Incentive::create([
            'user_id' => $user->id,
            'project_id' => $project->id,
            'milestone_id' => $milestone->id,
            'incentive_rule_id' => $rule->id,
            'amount' => round($convertedAmount, 2),
            'calculation_date' => now(),
            'status' => 'pending',
        ]);
    }

    public function deleted(Payment $payment)
    {
        $milestone = $payment->milestone;
        $project = $payment->project;
        if (!$milestone || !$project) {
            return;
        }
        // Find the best matching incentive rule for this milestone
        $roleId = null;
        if ($project->user_id) {
            $roleId = DB::table('model_has_roles')
                ->where('model_type', 'App\\Models\\User')
                ->where('model_id', $project->user_id)
                ->value('role_id');
        }
        $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
            ->where('role_id', $roleId)
            ->where('duration_percentage', '<=', $milestone->percentage)
            ->orderByDesc('duration_percentage')
            ->first();
        if ($rule) {
            // Delete the incentive for this milestone, project, user
            \App\Models\Incentive::where('milestone_id', $milestone->id)
                ->where('project_id', $project->id)
                ->where('user_id', $project->user_id)
                ->delete();
        }
    }
}
