<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fix sidebar navigation issues
        function fixSidebar() {
            console.log('Fixing sidebar...');
            
            // Remove any inline styles that might be hiding elements
            document.querySelectorAll('.fi-sidebar-item, .fi-sidebar-group, .fi-sidebar-group-items').forEach(el => {
                if (el.style.display === 'none') {
                    console.log('Found hidden element, making visible:', el);
                    el.style.display = '';
                }
            });
            
            // Make sure all navigation items are visible
            document.querySelectorAll('.fi-sidebar-item').forEach(item => {
                const computedStyle = window.getComputedStyle(item);
                if (computedStyle.display === 'none') {
                    console.log('Found hidden item via computed style, making visible:', item);
                    item.style.display = 'flex';
                    item.style.visibility = 'visible';
                    item.style.opacity = '1';
                }
            });
            
            // Fix sidebar groups
            document.querySelectorAll('.fi-sidebar-group').forEach(group => {
                const computedStyle = window.getComputedStyle(group);
                if (computedStyle.display === 'none') {
                    console.log('Found hidden group via computed style, making visible:', group);
                    group.style.display = 'block';
                    group.style.visibility = 'visible';
                    group.style.opacity = '1';
                }
            });
            
            // Ensure all navigation items have proper visibility
            document.querySelectorAll('[x-data]').forEach(el => {
                if (el.classList.contains('fi-sidebar-group') || el.classList.contains('fi-sidebar-item')) {
                    // Force Alpine.js to show the element
                    if (el.__x) {
                        console.log('Updating Alpine.js component:', el);
                        if (el.__x.$data.hasOwnProperty('isCollapsed')) {
                            el.__x.$data.isCollapsed = false;
                        }
                        if (el.__x.$data.hasOwnProperty('visible')) {
                            el.__x.$data.visible = true;
                        }
                    }
                }
            });
        }
        
        // Run initially
        fixSidebar();
        
        // Run again after a short delay to catch any dynamically loaded elements
        setTimeout(fixSidebar, 500);
        setTimeout(fixSidebar, 1000);
        setTimeout(fixSidebar, 2000);
        
        // Run whenever the sidebar might change
        document.addEventListener('click', function(e) {
            if (e.target.closest('.fi-sidebar')) {
                setTimeout(fixSidebar, 100);
            }
        });
        
        // Monitor for DOM changes in the sidebar
        const sidebarObserver = new MutationObserver(function(mutations) {
            fixSidebar();
        });
        
        const sidebar = document.querySelector('.fi-sidebar');
        if (sidebar) {
            sidebarObserver.observe(sidebar, { 
                childList: true, 
                subtree: true,
                attributes: true,
                attributeFilter: ['style', 'class']
            });
        }
    });
</script>
<?php /**PATH D:\wamp64\www\smms\resources\views/components/sidebar-fix-v2.blade.php ENDPATH**/ ?>