<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Milestone;
use App\Models\Payment;
use App\Models\Project;

class PaymentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'project_id' => Project::factory(),
            'milestone_id' => Milestone::factory(),
            'amount' => fake()->randomFloat(2, 0, 99999999.99),
            'due_date' => fake()->date(),
            'paid_date' => fake()->date(),
            'status' => fake()->word(),
            'payment_method' => fake()->word(),
            'transaction_id' => fake()->word(),
            'notes' => fake()->text(),
        ];
    }
}
