<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Milestone extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'project_id',
        'title',
        'description',
        'due_date',
        'percentage',
        'amount',
        'status',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'project_id' => 'integer',
        'due_date' => 'date',
        'percentage' => 'decimal:2',
        'amount' => 'decimal:2',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public static function boot()
    {
        parent::boot();

        static::updated(function ($milestone) {
            if ($milestone->isDirty('status') && $milestone->status === 'completed') {
                // Only create payment if not already exists for this milestone
                if (!\App\Models\Payment::where('milestone_id', $milestone->id)->exists()) {
                    \App\Models\Payment::create([
                        'project_id' => $milestone->project_id,
                        'milestone_id' => $milestone->id,
                        'amount' => $milestone->amount,
                        'due_date' => $milestone->due_date,
                        'status' => 'pending',
                    ]);
                }
            }
        });

        static::created(function ($milestone) {
            if ($milestone->status === 'completed') {
                if (!\App\Models\Payment::where('milestone_id', $milestone->id)->exists()) {
                    \App\Models\Payment::create([
                        'project_id' => $milestone->project_id,
                        'milestone_id' => $milestone->id,
                        'amount' => $milestone->amount,
                        'due_date' => $milestone->due_date,
                        'status' => 'pending',
                    ]);
                }
            }
        });

        static::deleting(function ($milestone) {
            \App\Models\Payment::where('milestone_id', $milestone->id)->delete();
            \App\Models\Incentive::where('milestone_id', $milestone->id)->delete();
        });
    }
}
