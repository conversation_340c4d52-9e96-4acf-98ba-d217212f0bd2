<?php

namespace App\Console\Commands;

use App\Models\Project;
use App\Models\User;
use Illuminate\Console\Command;

class AssignProjectsToBde extends Command
{
    protected $signature = 'projects:assign-to-bde {email} {--count=3}';
    protected $description = 'Assign projects to a BDE user';

    public function handle()
    {
        $email = $this->argument('email');
        $count = $this->option('count');
        
        $this->info("Assigning {$count} projects to BDE user with email {$email}...");
        
        // Find the user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found");
            return 1;
        }
        
        // Verify the user has BDE role
        if (!$user->hasRole('bde')) {
            $this->warn("User {$user->name} does not have BDE role. Assigning BDE role...");
            $this->call('role:fix-bde', ['email' => $email]);
        }
        
        // Get projects not assigned to this user
        $projects = Project::where('user_id', '!=', $user->id)
            ->inRandomOrder()
            ->limit($count)
            ->get();
        
        if ($projects->isEmpty()) {
            $this->error("No projects available to assign");
            return 1;
        }
        
        // Assign projects to the user
        foreach ($projects as $project) {
            $oldUserId = $project->user_id;
            $project->user_id = $user->id;
            $project->save();
            
            $this->info("Project '{$project->title}' assigned to {$user->name} (previously assigned to user ID: {$oldUserId})");
        }
        
        // Show summary
        $totalProjects = Project::where('user_id', $user->id)->count();
        $this->info("Total projects assigned to {$user->name}: {$totalProjects}");
        
        $this->info('Projects assigned successfully!');
        return 0;
    }
}
