<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fix sidebar navigation issues
        function fixSidebar() {
            // Ensure all sidebar items are visible
            const sidebarItems = document.querySelectorAll('.fi-sidebar-item');
            sidebarItems.forEach(item => {
                item.style.display = 'flex';
                item.style.width = '100%';
            });

            // Fix sidebar groups
            const sidebarGroups = document.querySelectorAll('.fi-sidebar-group');
            sidebarGroups.forEach(group => {
                group.style.display = 'block';
                group.style.width = '100%';
            });

            // Fix sidebar group items
            const sidebarGroupItems = document.querySelectorAll('.fi-sidebar-group-items');
            sidebarGroupItems.forEach(items => {
                items.style.display = 'block';
                items.style.width = '100%';
            });

            // Fix sidebar item labels
            const sidebarItemLabels = document.querySelectorAll('.fi-sidebar-item-label');
            sidebarItemLabels.forEach(label => {
                label.style.display = 'block';
                label.style.whiteSpace = 'nowrap';
                label.style.overflow = 'hidden';
                label.style.textOverflow = 'ellipsis';
            });

            // Remove "Tokens" menu from sidebar
            const tokensMenu = document.querySelector('.fi-sidebar-item[data-label="Tokens"]');
            if (tokensMenu) {
                tokensMenu.remove();
            }
        }

        // Run initially
        fixSidebar();

        // Run again after a short delay to catch any dynamically loaded elements
        setTimeout(fixSidebar, 500);

        // Run whenever the sidebar might change (e.g., collapsing/expanding)
        document.addEventListener('click', function(e) {
            if (e.target.closest('.fi-sidebar')) {
                setTimeout(fixSidebar, 100);
            }
        });
    });
</script>
