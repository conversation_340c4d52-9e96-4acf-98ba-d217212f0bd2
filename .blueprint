created:
    - app/Http/Controllers/DashboardController.php
    - app/Http/Controllers/ProjectController.php
    - app/Http/Controllers/ClientController.php
    - app/Http/Controllers/MilestoneController.php
    - app/Http/Controllers/PaymentController.php
    - app/Http/Controllers/IncentiveController.php
    - app/Http/Controllers/ReportController.php
    - app/Http/Controllers/NotificationController.php
    - app/Http/Controllers/UserController.php
    - database/factories/UserFactory.php
    - database/factories/RoleFactory.php
    - database/factories/PermissionFactory.php
    - database/factories/ClientFactory.php
    - database/factories/ProjectFactory.php
    - database/factories/ProjectTypeFactory.php
    - database/factories/MilestoneFactory.php
    - database/factories/PaymentFactory.php
    - database/factories/IncentiveFactory.php
    - database/factories/IncentiveRuleFactory.php
    - database/factories/NotificationFactory.php
    - database/factories/NotificationPreferenceFactory.php
    - database/factories/CurrencyRateFactory.php
    - database/factories/ReportFactory.php
    - database/migrations/2025_04_10_122108_create_users_table.php
    - database/migrations/2025_04_10_122109_create_roles_table.php
    - database/migrations/2025_04_10_122110_create_permissions_table.php
    - database/migrations/2025_04_10_122111_create_clients_table.php
    - database/migrations/2025_04_10_122112_create_projects_table.php
    - database/migrations/2025_04_10_122113_create_project_types_table.php
    - database/migrations/2025_04_10_122114_create_milestones_table.php
    - database/migrations/2025_04_10_122115_create_payments_table.php
    - database/migrations/2025_04_10_122116_create_incentives_table.php
    - database/migrations/2025_04_10_122117_create_incentive_rules_table.php
    - database/migrations/2025_04_10_122118_create_notifications_table.php
    - database/migrations/2025_04_10_122119_create_notification_preferences_table.php
    - database/migrations/2025_04_10_122120_create_currency_rates_table.php
    - database/migrations/2025_04_10_122121_create_reports_table.php
    - database/migrations/2025_04_10_122122_create_permission_role_table.php
    - app/Models/User.php
    - app/Models/Role.php
    - app/Models/Permission.php
    - app/Models/Client.php
    - app/Models/Project.php
    - app/Models/ProjectType.php
    - app/Models/Milestone.php
    - app/Models/Payment.php
    - app/Models/Incentive.php
    - app/Models/IncentiveRule.php
    - app/Models/Notification.php
    - app/Models/NotificationPreference.php
    - app/Models/CurrencyRate.php
    - app/Models/Report.php
    - tests/Feature/Http/Controllers/DashboardControllerTest.php
    - tests/Feature/Http/Controllers/ProjectControllerTest.php
    - tests/Feature/Http/Controllers/ClientControllerTest.php
    - tests/Feature/Http/Controllers/MilestoneControllerTest.php
    - tests/Feature/Http/Controllers/PaymentControllerTest.php
    - tests/Feature/Http/Controllers/IncentiveControllerTest.php
    - tests/Feature/Http/Controllers/ReportControllerTest.php
    - tests/Feature/Http/Controllers/NotificationControllerTest.php
    - tests/Feature/Http/Controllers/UserControllerTest.php
    - app/Http/Requests/ProjectStoreRequest.php
    - app/Http/Requests/ProjectUpdateRequest.php
    - app/Http/Requests/ClientStoreRequest.php
    - app/Http/Requests/ClientUpdateRequest.php
    - app/Http/Requests/MilestoneStoreRequest.php
    - app/Http/Requests/MilestoneUpdateRequest.php
    - app/Http/Requests/PaymentStoreRequest.php
    - app/Http/Requests/PaymentUpdateRequest.php
    - app/Http/Requests/IncentiveStoreRequest.php
    - app/Http/Requests/IncentiveUpdateRequest.php
    - app/Http/Requests/ReportStoreRequest.php
    - app/Http/Requests/ReportUpdateRequest.php
    - app/Http/Requests/NotificationStoreRequest.php
    - app/Http/Requests/NotificationUpdateRequest.php
    - app/Http/Requests/UserStoreRequest.php
    - app/Http/Requests/UserUpdateRequest.php
    - resources/views/dashboard/index.blade.php
    - resources/views/project/index.blade.php
    - resources/views/project/create.blade.php
    - resources/views/project/show.blade.php
    - resources/views/project/edit.blade.php
    - resources/views/client/index.blade.php
    - resources/views/client/create.blade.php
    - resources/views/client/show.blade.php
    - resources/views/client/edit.blade.php
    - resources/views/milestone/index.blade.php
    - resources/views/milestone/create.blade.php
    - resources/views/milestone/show.blade.php
    - resources/views/milestone/edit.blade.php
    - resources/views/payment/index.blade.php
    - resources/views/payment/create.blade.php
    - resources/views/payment/show.blade.php
    - resources/views/payment/edit.blade.php
    - resources/views/incentive/index.blade.php
    - resources/views/incentive/create.blade.php
    - resources/views/incentive/show.blade.php
    - resources/views/incentive/edit.blade.php
    - resources/views/report/index.blade.php
    - resources/views/report/create.blade.php
    - resources/views/report/show.blade.php
    - resources/views/report/edit.blade.php
    - resources/views/notification/index.blade.php
    - resources/views/notification/create.blade.php
    - resources/views/notification/show.blade.php
    - resources/views/notification/edit.blade.php
    - resources/views/user/index.blade.php
    - resources/views/user/create.blade.php
    - resources/views/user/show.blade.php
    - resources/views/user/edit.blade.php
updated:
    - routes/web.php
models:
    Book: { title: string, author: string, description: text }
    Contact: { name: string }
    Post: { title: string, content: text }
    User: { name: string, email: 'string unique', password: string, last_login_at: 'nullable timestamp', remember_token: 'nullable string', relationships: { belongsTo: Role, hasMany: 'Project, Notification, NotificationPreference, Incentive' } }
    Role: { name: string, description: 'nullable string', relationships: { hasMany: User, belongsToMany: Permission } }
    Permission: { name: string, description: 'nullable string', relationships: { belongsToMany: Role } }
    Client: { name: string, email: string, phone: 'nullable string', address: 'nullable text', contact_person: 'nullable string', status: 'string default:active', relationships: { hasMany: Project } }
    Project: { client_id: 'id foreign', user_id: 'id foreign:users', project_type_id: 'id foreign', title: string, description: 'nullable text', won_date: date, start_date: date, end_date: 'nullable date', total_payment: 'decimal:10,2', duration: integer, duration_unit: string, payment_cycle: string, status: 'string default:active', relationships: { belongsTo: 'Client, User, ProjectType', hasMany: 'Milestone, Payment, Incentive' } }
    ProjectType: { name: string, description: 'nullable text', relationships: { hasMany: 'Project, IncentiveRule' } }
    Milestone: { project_id: 'id foreign', title: string, description: 'nullable text', due_date: date, percentage: 'decimal:5,2', amount: 'decimal:10,2', status: 'string default:pending', relationships: { belongsTo: Project } }
    Payment: { project_id: 'id foreign', milestone_id: 'nullable id foreign', amount: 'decimal:10,2', due_date: date, paid_date: 'nullable date', status: 'string default:pending', payment_method: 'nullable string', transaction_id: 'nullable string', notes: 'nullable text', relationships: { belongsTo: 'Project, Milestone' } }
    Incentive: { user_id: 'id foreign', project_id: 'id foreign', amount: 'decimal:10,2', calculation_date: date, payment_date: 'nullable date', status: 'string default:pending', description: 'nullable text', approved_by: 'nullable id foreign:users', relationships: { belongsTo: 'User, Project' } }
    IncentiveRule: { project_type_id: 'id foreign', role_id: 'id foreign', percentage: 'decimal:5,2', duration_percentage: 'decimal:5,2', fixed_amount: 'nullable decimal:10,2', currency: 'string default:INR', calculation_type: string, relationships: { belongsTo: 'ProjectType, Role' } }
    Notification: { user_id: 'id foreign', title: string, message: text, type: string, read_at: 'nullable timestamp', data: 'nullable json', relationships: { belongsTo: User } }
    NotificationPreference: { user_id: 'id foreign', notification_type: string, email: 'boolean default:true', in_app: 'boolean default:true', relationships: { belongsTo: User } }
    CurrencyRate: { from_currency: string, to_currency: string, rate: 'decimal:10,6', last_updated_at: timestamp }
    Report: { user_id: 'id foreign', title: string, type: string, parameters: json, created_at: timestamp, file_path: 'nullable string', relationships: { belongsTo: User } }
