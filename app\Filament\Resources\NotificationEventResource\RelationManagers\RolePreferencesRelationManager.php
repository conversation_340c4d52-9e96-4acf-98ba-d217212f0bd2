<?php

namespace App\Filament\Resources\NotificationEventResource\RelationManagers;

use App\Models\Role;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RolePreferencesRelationManager extends RelationManager
{
    protected static string $relationship = 'rolePreferences';

    protected static ?string $recordTitleAttribute = 'role.name';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('role_id')
                    ->label('Role')
                    ->options(Role::pluck('name', 'id'))
                    ->required()
                    ->searchable()
                    ->unique(ignoreRecord: true, callback: function (Builder $query, $record) {
                        return $query->where('notification_event_id', $this->getOwnerRecord()->id);
                    }),
                
                Forms\Components\Toggle::make('is_enabled')
                    ->label('Enabled')
                    ->helperText('Whether this notification is enabled for this role')
                    ->default(true),
                
                Forms\Components\Toggle::make('email')
                    ->label('Email Notifications')
                    ->helperText('Send email notifications to users with this role')
                    ->default(true),
                
                Forms\Components\Toggle::make('in_app')
                    ->label('In-App Notifications')
                    ->helperText('Send in-app notifications to users with this role')
                    ->default(true),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('role.name')
            ->columns([
                Tables\Columns\TextColumn::make('role.name')
                    ->label('Role')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_enabled')
                    ->label('Enabled')
                    ->boolean()
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('email')
                    ->label('Email')
                    ->boolean()
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('in_app')
                    ->label('In-App')
                    ->boolean()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->preloadRecordSelect()
                    ->form(fn (Tables\Actions\AttachAction $action): array => [
                        Forms\Components\Select::make('role_id')
                            ->label('Role')
                            ->options(Role::pluck('name', 'id'))
                            ->required()
                            ->searchable()
                            ->preload()
                            ->unique(callback: function (Builder $query) {
                                return $query->where('notification_event_id', $this->getOwnerRecord()->id);
                            }),
                        
                        Forms\Components\Toggle::make('is_enabled')
                            ->label('Enabled')
                            ->helperText('Whether this notification is enabled for this role')
                            ->default(true),
                        
                        Forms\Components\Toggle::make('email')
                            ->label('Email Notifications')
                            ->helperText('Send email notifications to users with this role')
                            ->default(true),
                        
                        Forms\Components\Toggle::make('in_app')
                            ->label('In-App Notifications')
                            ->helperText('Send in-app notifications to users with this role')
                            ->default(true),
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DetachAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                ]),
            ]);
    }
}
