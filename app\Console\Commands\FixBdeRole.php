<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class FixBdeRole extends Command
{
    protected $signature = 'role:fix-bde {email?}';
    protected $description = 'Fix BDE role assignment and ensure proper permissions';

    public function handle()
    {
        $this->info('Fixing BDE role...');
        
        // Get or create BDE role
        $bdeRole = Role::firstOrCreate(['name' => 'bde', 'guard_name' => 'web']);
        $this->info("BDE role ID: {$bdeRole->id}");
        
        // If email is provided, assign B<PERSON> role to the user
        if ($email = $this->argument('email')) {
            $user = User::where('email', $email)->first();
            
            if (!$user) {
                $this->error("User with email {$email} not found");
                return 1;
            }
            
            // Remove all other roles from the user
            $user->roles()->detach();
            
            // Assign BDE role to the user
            $user->assignRole($bdeRole);
            $this->info("BDE role assigned to user {$user->name}");
            
            // Verify role assignment
            $this->info("User roles: " . implode(', ', $user->roles()->pluck('name')->toArray()));
        } else {
            // List all users with BDE role
            $bdeUsers = User::role('bde')->get();
            
            if ($bdeUsers->isEmpty()) {
                $this->info("No users with BDE role found");
            } else {
                $this->info("Users with BDE role:");
                foreach ($bdeUsers as $user) {
                    $this->info("- {$user->name} ({$user->email})");
                }
            }
        }
        
        // Clear cache
        $this->call('cache:clear');
        $this->call('optimize:clear');
        
        $this->info('BDE role fixed successfully!');
        return 0;
    }
}
